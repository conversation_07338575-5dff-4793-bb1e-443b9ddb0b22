<?php
/**
 * <PERSON><PERSON>t to update customer account numbers
 *
 * This script updates all existing customers with account numbers in the format YYYY-0000
 * where YYYY is the year from the created timestamp and 0000 is a sequential series number
 * based on the order of creation within that year.
 */

// Database configuration
$config = array(
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'inventta_250514'
);

try {
    // Connect to the database using PDO
    $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8";
    $pdo = new PDO($dsn, $config['username'], $config['password']);

    // Set PDO to throw exceptions on error
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if account_no column exists in the customers table
    $columnCheckQuery = "SHOW COLUMNS FROM customers LIKE 'account_no'";
    $stmt = $pdo->query($columnCheckQuery);

    // If the column doesn't exist, add it
    if ($stmt->rowCount() === 0) {
        $alterQuery = "ALTER TABLE customers ADD COLUMN account_no VARCHAR(10) DEFAULT NULL AFTER id";
        $pdo->exec($alterQuery);
        echo "Added account_no column to customers table.\n";
    }

    // Get all customers ordered by created timestamp
    $query = "SELECT id, created FROM customers WHERE created IS NOT NULL ORDER BY created ASC";
    $stmt = $pdo->query($query);

    // Group customers by year
    $customersByYear = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $created = new DateTime($row['created']);
        $year = $created->format('Y');

        if (!isset($customersByYear[$year])) {
            $customersByYear[$year] = array();
        }

        $customersByYear[$year][] = $row['id'];
    }

    // Update customers with account numbers
    $updateQuery = "UPDATE customers SET account_no = :account_no WHERE id = :id";
    $updateStmt = $pdo->prepare($updateQuery);

    foreach ($customersByYear as $year => $customerIds) {
        $counter = 1;
        foreach ($customerIds as $customerId) {
            $accountNo = $year . '-' . str_pad($counter, 4, '0', STR_PAD_LEFT);

            $updateStmt->bindParam(':account_no', $accountNo);
            $updateStmt->bindParam(':id', $customerId, PDO::PARAM_INT);

            if (!$updateStmt->execute()) {
                echo "Error updating customer ID $customerId\n";
            } else {
                echo "Updated customer ID $customerId with account number $accountNo\n";
            }

            $counter++;
        }
    }

    // Handle customers with NULL created dates
    $query = "SELECT id FROM customers WHERE created IS NULL";
    $stmt = $pdo->query($query);

    // Use current year for customers with NULL created dates
    $currentYear = date('Y');
    $counter = 1;

    $updateStmt = $pdo->prepare($updateQuery);

    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $customerId = $row['id'];
        $accountNo = $currentYear . '-' . str_pad($counter, 4, '0', STR_PAD_LEFT);

        $updateStmt->bindParam(':account_no', $accountNo);
        $updateStmt->bindParam(':id', $customerId, PDO::PARAM_INT);

        if (!$updateStmt->execute()) {
            echo "Error updating customer ID $customerId\n";
        } else {
            echo "Updated customer ID $customerId (NULL created date) with account number $accountNo\n";
        }

        $counter++;
    }

    echo "Customer account number update completed.\n";

} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}
