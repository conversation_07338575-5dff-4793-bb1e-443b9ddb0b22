<?php
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Sat, 29 Sep 1990 05:00:00 GMT");
?>
<?php include_once('utility.php');?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php include_once('elements/header.php');?>
</head>
<body class="bg-cover" ng-controller="indexController" ng-init="initializeController()" >
    <div id="bootstrap-message" ng-if="!IsInitialized"> Loading..</div>
         <div id="wrapper" ng-class="{toggled: isToggled }"ng-controller="uiController" ng-init="initializeController()">

            <!-- Sidebar -->
            <div id="sidebar-wrapper"  ng-class="{isLogin:IsloggedIn}">
                <ul class="sidebar-nav">
                    <li>
                        <a href="#/"  >
                            <i class="glyphicon glyphicon-home"></i>Home
                        </a>
                    </li>
                    <li ng-repeat="module in Modules | orderBy:['-type','order']">
                        <a ng-class={'active':activeModule==module.link} href="{{'#/'+module.link}}"  >
                            <i ng-class="'glyphicon glyphicon-'+module.icon"></i>{{module.title}}
                        </a>
                    </li>
                    <li>
                        <a href="#/logout"  >
                            <i class="glyphicon glyphicon-off"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
            <!-- /#sidebar-wrapper -->
             <!-- Navbar -->
            <div class="navbar navbar-default navbar-fixed-top" id="navbar-toggle">
                <button type="button" class="navbar-toggle"  id="menu-toggle"  ng-class="{isLogin:IsloggedIn}" ng-click="toggle()">
                  <span class="icon-bar"></span>
                  <span class="icon-bar"></span>
                  <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand hide" ng-class="{hide: !User}">Hello, {{User.username}}!</a>
            </div>
            <div id="progress-bar" ng-if="__Progress" style="height:{{__Progress==100||__Progress==0? 0: 1}}rem;width:{{__Progress}}%;opacity:{{__Progress==100||__Progress==0? 0: 1}}"></div>
            
            <div class="page-content-blind"></div>
            <!-- Page Content -->
            <div id="page-content-wrapper">
                
                <div class="container-fluid">
                    <div ng-view></div>
                </div>
                    <!--
                    <button type="button" class="btn btn-primary btn-md btn-fab bottom left"><span class="glyphicon glyphicon-plus" aria-hidden="true"></span></button>
                    <button type="button" class="btn btn-primary btn-md btn-fab bottom right"><span class="glyphicon glyphicon-play" aria-hidden="true"></span></button>
                    <button type="button" class="btn btn-primary btn-md btn-fab top right"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></button>
                    -->
            </div>
            <!-- /#page-content-wrapper -->

        </div>
        <!-- Root Modal -->
        <script type="text/ng-template" id="rootModalContent.html">

            <div ng-controller="rootModalController" ng-init="initializeController()">
            
              <div class="modal-header" ng-show="title">
                <h4 class="modal-title" >{{title}}</h4>
              </div>
              <div class="modal-body">
                <div class="text-center">
                    <div ng-bind-html="renderHtml(message)"></div>
                    <center><h4>{{time}}</h4></center>
                </div>
              </div>
            <div class="modal-footer" ng-show="buttons.length">
                <div >
                    <button class="btn" ng-repeat="btn in buttons" ng-click="btn.callback()">{{btn.label}}</button>
                </div>
            </div>
              
            </div>
            
        </script>
        <!-- /#wrapper -->
        <div class="alerts-tray" ng-show="alerts.length" ng-class="{'alerts-open' : IsInitialized}" style="z-index:2000;left:30%;width:40%">
                <div class="alert alert-{{alert.type}}"  ng-repeat="alert in alerts" >
                  <button type="button" class="close" ng-click="closeAlert($index)"><span>&times;</span></button>
                  {{alert.msg}}
                </div>
            </div>
    </body>
</html>