<?php 
    $versionNo = Utility::getVersionNo();
    $description = Utility::$versionData['DESCRIPTION'];
    $author = Utility::$versionData['AUTHOR'];
    $product = Utility::$versionData['PRODUCT'];
    $alias = Utility::$versionData['ALIAS'];
    $hash = Utility::$versionData['HASH_SHORT'];
    $title = sprintf("%s(%s)",$product,$alias);
    $description = Utility::$versionData['DESCRIPTION'];
    $copyright =sprintf("Copyright © %s,%s", Utility::$versionData['COPYRIGHT'],$product);
    $clientInfo = Utility::getClientInfo();
    $clientName = $clientInfo['NAME'];
    $clientLogo = $clientInfo['LOGO'];
    $clientIcon = 'assets/img/'.$clientInfo['ICON'];;
?>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="title" content="<?php echo $title; ?>" />
<meta name="application-name" content="<?php echo $product; ?>" />
<meta name="description" content="<?php echo $description; ?>" />
<meta name="author" content="<?php echo $author; ?>" />
<meta name="version" content="<?php echo $versionNo; ?>"/>
<meta name="hash" content="<?php echo $hash; ?>"/>
<meta name="copyright" content="<?php echo $copyright; ?>"/>
<link href="<?php echo $clientIcon; ?>" type="image/x-icon" rel="icon"/>
<link href="<?php echo $clientIcon; ?>" type="image/x-icon" rel="shortcut icon"/>

<title><?php echo $title; ?></title>

<script data-main="common/main.js?<?php echo $versionNo.'-'.$hash; ?>" src="libs/bower_components/requirejs/require.js"> </script>

<!-- Custom CSS -->
<?php 
    // Load css from an array using a foreach loop
    $cssFiles = array(
    'assets/css/bootstrap.min.css',
    'assets/css/simple-sidebar.css',
    'assets/css/custom.css',
    'assets/css/login.css',
    'assets/fonts/font-awesome-4.3.0/font-awesome.min.css',
    'libs/bower_components/angular-block-ui/dist/angular-block-ui.min.css',
    'libs/bower_components/angular-chart.js/dist/angular-chart.css',
    );
foreach($cssFiles as $fileName)
    echo Utility::loadStatic('css',$fileName);
?>