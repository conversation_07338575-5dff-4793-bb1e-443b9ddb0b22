<?php

class Utility {
    public static $versionData = null;
    public static $clientData = null;

    public static function getVersionInfo() {
        if (self::$versionData !== null) {
            return self::$versionData;
        }

        // Construct the path to the version.info file
        self::$versionData = self::loadInfoFile('../../version/version.info');
        self::$versionData['HASH_SHORT'] = substr(self::$versionData['HASH'],0,5);
        return self::$versionData;
    }

    public static function getClientInfo() {
        self::$clientData = self::loadInfoFile('../../version/client.info');
        return self::$clientData;
    }
    public static function loadInfoFile($path){
        // Construct the path to the *.info file
        $infoFilePath = dirname(__FILE__) . $path;
        $infoFile = file_get_contents($infoFilePath);

        if ($infoFile === false) {
            return "Error: Unable to read $infoFilePath";
        }

        $infoLines = explode("\n", $infoFile);
        $infoData = [];
        foreach ($infoLines as $line) {
            $lineParts = explode('=', $line, 2);
            if (count($lineParts) == 2) {
                $infoData[trim($lineParts[0])] = htmlspecialchars(trim($lineParts[1]));
            }
        }
        return $infoData;
    }
    public static function getVersionNo(){
        Utility::getVersionInfo();
        $basePath = strtolower(basename(dirname(dirname(dirname(__FILE__)))));
        if($basePath=='htdocs') $basePath = 'local';
        $versionNo = sprintf("v%s.%s.%s",self::$versionData['MAJOR'],self::$versionData['MINOR'],self::$versionData['PATCH']);
        $versionNo = htmlspecialchars($versionNo);
    
        return $versionNo;
    }
    public static function loadStatic($type, $name) {
        Utility::getVersionInfo();
        $versionInfo = self::$versionData;
        if (!is_array($versionInfo)) {
            return; // Handle error or do nothing
        }

        $versionString = $versionInfo['MAJOR'] . '.' . $versionInfo['MINOR'] . '.' . $versionInfo['PATCH'];
        $versionString .= '-'.$versionInfo['HASH_SHORT'];
        $filePath = "$name?v=$versionString";

        if ($type === 'css') {
            echo "<link rel='stylesheet' type='text/css' href='$filePath'>";
        }
        // Add more conditions for other file types if necessary
    }
}
?>
