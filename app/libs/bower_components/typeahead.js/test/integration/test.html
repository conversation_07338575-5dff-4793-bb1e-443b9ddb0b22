<!DOCTYPE html>
<html>
  <head>
    <title></title>
    <script src="../../bower_components/jquery/jquery.js"></script>
    <script src="../../dist/typeahead.bundle.js"></script>

    <style>
      .container {
        width: 800px;
        margin: 50px auto;
      }

      .typeahead-wrapper {
        display: block;
        margin: 50px 0;
      }

      .tt-dropdown-menu {
        background-color: #fff;
        border: 1px solid #000;
      }

      .tt-suggestion.tt-cursor {
        background-color: #ccc;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <form action="/where" method="GET">
        <div class="typeahead-wrapper">
          <input id="states" name="states" type="text">
          <input type="submit">
        </div>
      </form>
    </div>

    <script>
      var states = new Bloodhound({
        datumTokenizer: function(d) { 
          return Bloodhound.tokenizers.whitespace(d.val); 
        },
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        local: [
          { val: 'Alabama' },
          { val: 'Alaska' },
          { val: 'Arizona' },
          { val: 'Arkansas' },
          { val: 'California' },
          { val: 'Colorado' },
          { val: 'Connecticut' },
          { val: 'Delaware' },
          { val: 'Florida' },
          { val: 'Georgia' },
          { val: 'Hawaii' },
          { val: 'Idaho' },
          { val: 'Illinois' },
          { val: 'Indiana' },
          { val: 'Iowa' },
          { val: 'Kansas' },
          { val: 'Kentucky' },
          { val: 'Louisiana' },
          { val: 'Maine' },
          { val: 'Maryland' },
          { val: 'Massachusetts' },
          { val: 'Michigan' },
          { val: 'Minnesota' },
          { val: 'Mississippi' },
          { val: 'Missouri' },
          { val: 'Montana' },
          { val: 'Nebraska' },
          { val: 'Nevada' },
          { val: 'New Hampshire' },
          { val: 'New Jersey' },
          { val: 'New Mexico' },
          { val: 'New York' },
          { val: 'North Carolina' },
          { val: 'North Dakota' },
          { val: 'Ohio' },
          { val: 'Oklahoma' },
          { val: 'Oregon' },
          { val: 'Pennsylvania' },
          { val: 'Rhode Island' },
          { val: 'South Carolina' },
          { val: 'South Dakota' },
          { val: 'Tennessee' },
          { val: 'Texas' },
          { val: 'Utah' },
          { val: 'Vermont' },
          { val: 'Virginia' },
          { val: 'Washington' },
          { val: 'West Virginia' },
          { val: 'Wisconsin' },
          { val: 'Wyoming' },
          { val: 'this is a very long value so deal with it' }
        ]
      });

      states.initialize();

      $('#states').typeahead({
        highlight: true
      },
      {
        displayKey: 'val',
        source: states.ttAdapter()
      });
    </script>
  </body>
</html>
