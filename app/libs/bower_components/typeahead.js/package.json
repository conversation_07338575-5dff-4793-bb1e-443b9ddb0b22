{"name": "typeahead.js", "description": "fast and fully-featured autocomplete library", "keywords": ["typeahead", "autocomplete"], "homepage": "http://twitter.github.com/typeahead.js", "bugs": "https://github.com/twitter/typeahead.js/issues", "repository": {"type": "git", "url": "https://github.com/twitter/typeahead.js.git"}, "author": {"name": "Twitter, Inc.", "url": "https://twitter.com/twitteross"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/JakeHarding"}, {"name": "<PERSON>", "url": "https://twitter.com/timtrueman"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://twitter.com/vskarich"}], "devDependencies": {"chai": "^1.9.1", "colors": "^0.6.2", "grunt": "~0.4", "grunt-concurrent": "^0.5.0", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-concat": "~0.1", "grunt-contrib-connect": "~0.1", "grunt-contrib-jshint": "~0.8", "grunt-contrib-uglify": "~0.2.6", "grunt-contrib-watch": "~0.2", "grunt-exec": "~0.4.5", "grunt-sed": "~0.1", "grunt-step": "~0.2.0", "karma": "~0.8.6", "mocha": "^1.20.1", "semver": "~1.1.3", "underscore": "^1.6.0", "yiewd": "^0.5.0"}, "scripts": {"test": "./node_modules/.bin/karma start --single-run --browsers PhantomJS"}, "version": "0.10.5", "main": "dist/typeahead.bundle.js"}