{"name": "angular-block-ui", "description": "An AngularJS module that allows you to block user interaction on AJAX requests.", "version": "0.2.0", "keywords": ["angular", "<PERSON><PERSON>s", "block", "block-ui", "blockui", "loading"], "authors": ["<PERSON><PERSON> Mc<PERSON>ull <<EMAIL>>"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "gulp-tasks", "src", "dest", "/build-config.js", "/gulpfile.js", "/package.json", "/README.md", "/karma.conf.js"], "main": ["dist/angular-block-ui.js", "dist/angular-block-ui.css"], "dependencies": {"angular": "~1.2.26"}, "devDependencies": {"angular-route": "~1.2.26", "angular-resource": "~1.2.26", "angular-mocks": "~1.2.26", "angular-animate": "1.2.26", "angular-inform": "~0.0.8", "angular-response-lag": "~0.0.1", "bootstrap": "~3.2.0", "jquery": "1.*", "respond": "~1.4.2", "html5shiv": "~3.7.2", "showdown": "~0.3.1", "animate.css": "~3.2.0", "angular-markdown-text": "~0.0.2", "angular-sanitize": "1.2.26"}, "overrides": {"respond": {"main": "dest/respond.min.js"}, "showdown": {"main": "compressed/showdown.js"}, "angular-mocks": {"ignore": true}}, "resolutions": {"angular": "1.2.28"}, "homepage": "https://github.com/McNull/angular-block-ui", "_release": "0.2.0", "_resolution": {"type": "version", "tag": "v0.2.0", "commit": "7414db9e8f0a7fa39240c247a838a64c8b892f33"}, "_source": "git://github.com/McNull/angular-block-ui.git", "_target": "~0.2.0", "_originalSource": "angular-block-ui", "_direct": true}