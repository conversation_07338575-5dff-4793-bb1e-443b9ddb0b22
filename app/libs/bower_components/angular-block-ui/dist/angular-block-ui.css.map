{"version": 3, "sources": ["angular-block-ui-header.css", "angular-block-ui.css", "block-ui-animations.css"], "names": [], "mappings": "AAAA;;;;GAIA;;ACJA;EACA,oBAAA;EACA;;AAEA;EACA,8CAAA;;EAEA,kBAAA;EACA;;AAEA;;EAEA,iBAAA;EACA;;AAEA;EACA,oBAAA;EACA,gBAAA;EACA,QAAA;EACA,UAAA;EACA,WAAA;EACA,SAAA;EACA,WAAA;EACA,kBAAA;EACA,YAAA;EACA,2BAAA;EACA;;AAEA;EACA,cAAA;EACA,cAAA;EACA;;AAEA;EACA,WAAA;EACA;;AAEA;EACA,YAAA;EACA,4BAAA;EACA;;AAEA;EACA,aAAA;EACA,cAAA;EACA,cAAA;EACA,2BAAA;EACA,yBAAA;EACA;;AAEA;EACA,oBAAA;EACA,UAAA;EACA,SAAA;EACA,UAAA;EACA,WAAA;EACA,oBAAA;EACA,gBAAA;EACA;;AAEA;EACA,uBAAA;EACA,kBAAA;EACA,wBAAA;EACA,gBAAA;EACA,eAAA;EACA,oBAAA;EACA,iBAAA;EACA,mBAAA;EACA,oBAAA;;EAEA,4BAAA;EACA;;ACvEA,kFAAA;;AAEA;EACA,mEAAA;UAAA,2DAAA;EACA;;AAEA;EACA,iDAAA;EACA,2EAAA;EACA,8BAAA;UAAA,sBAAA;EACA;;AAEA,kFAAA", "file": "angular-block-ui.css", "sourcesContent": ["/*!\n   angular-block-ui v0.2.0\n   (c) 2015 (null) McNull https://github.com/McNull/angular-block-ui\n   License: MIT\n*/\n", ".block-ui {\n  position: relative;\n}\n\nbody.block-ui {\n  /* IE8 doesn't support .block-ui:not(body) */\n  \n  position: static;\n}\n\nbody.block-ui > .block-ui-container,\n.block-ui-main > .block-ui-container {\n  position: fixed;\n}\n\n.block-ui-container {\n  position: absolute;\n  z-index: 10000;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  height: 0;\n  overflow: hidden;\n  opacity: 0;\n  filter: alpha(opacity=00);\n}\n\n.block-ui-active > .block-ui-container {\n  height: 100%;\n  cursor: wait;\n}\n\n.block-ui-active .block-ui-active > .block-ui-container {\n  height: 0;\n}\n\n.block-ui-visible > .block-ui-container {\n  opacity: 1;\n  filter: alpha(opacity=100);\n}\n\n.block-ui-overlay {\n  width: 100%;\n  height: 100%;\n  opacity: 0.5;\n  filter: alpha(opacity=50);\n  background-color: white;\n}\n\n.block-ui-message-container {\n  position: absolute;\n  top: 35%;\n  left: 0;\n  right: 0;\n  height: 0;\n  text-align: center;\n  z-index: 10001;\n}\n\n.block-ui-message {\n  display: inline-block;\n  text-align: left;\n  background-color: #333;\n  color: #f5f5f5;\n  padding: 20px;\n  border-radius: 4px;\n  font-size: 20px;\n  font-weight: bold;\n  /* needed for IE */\n  \n  filter: alpha(opacity=100);\n}", "\n/* - - - - - - 8-< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - */\n\n.block-ui-anim-fade > .block-ui-container {\n  transition: height 0s linear 200ms, opacity 200ms ease 0s;\n}\n\n.block-ui-anim-fade.block-ui-active > .block-ui-container {\n  /*this resets the initial delay of the height */\n  /*and sizes the block to full height at once at the start of the block. */\n  transition-delay: 0s;\n}\n\n/* - - - - - - 8-< - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - */"], "sourceRoot": "../src/angular-block-ui"}