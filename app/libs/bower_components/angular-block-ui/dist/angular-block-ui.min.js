/*!
   angular-block-ui v0.2.0
   (c) 2015 (null) McNull https://github.com/McNull/angular-block-ui
   License: MIT
*/
!function(t){function e(e){try{t.module(e)}catch(n){return!1}return!0}function n(t,n,o){function c(){t.$on("$locationChangeStart",function(t){n.$_blockLocationChange&&n.state().blockCount>0&&t.preventDefault()}),t.$on("$locationChangeSuccess",function(){n.$_blockLocationChange=o.blockBrowserNavigation})}if(o.blockBrowserNavigation)if(e("ngRoute"))var r=t.$on("$viewContentLoaded",function(){r(),c()});else c()}var o=t.module("blockUI",[]);o.config(["$provide","$httpProvider",function(t,e){t.decorator("$exceptionHandler",["$delegate","$injector",function(t,e){var n,o;return function(c,r){if(o=o||e.get("blockUIConfig"),o.resetOnException)try{n=n||e.get("blockUI"),n.instances.reset()}catch(i){console.log("$exceptionHandler",c)}t(c,r)}}]),e.interceptors.push("blockUIHttpInterceptor")}]),o.run(["$document","blockUIConfig","$templateCache",function(t,e,n){e.autoInjectBodyBlock&&t.find("body").attr("block-ui","main"),e.template&&(e.templateUrl="$$block-ui-template$$",n.put(e.templateUrl,e.template))}]),o.config(["$provide",function(t){t.decorator("$location",c)}]);var c=["$delegate","blockUI","blockUIConfig",function(e,n,o){function c(t){var o=e[t];e[t]=function(){var t=o.apply(e,arguments);return t===e&&(n.$_blockLocationChange=!1),t}}if(o.blockBrowserNavigation){n.$_blockLocationChange=!0;var r=["url","path","search","hash","state"];t.forEach(r,c)}return e}];o.directive("blockUiContainer",["blockUIConfig","blockUiContainerLinkFn",function(t,e){return{scope:!0,restrict:"A",templateUrl:t.templateUrl,compile:function(){return e}}}]).factory("blockUiContainerLinkFn",["blockUI","blockUIUtils",function(){return function(t,e){var n=e.inheritedData("block-ui");if(!n)throw new Error("No parent block-ui service instance located.");t.state=n.state()}}]),o.directive("blockUi",["blockUiCompileFn",function(t){return{scope:!0,restrict:"A",compile:t}}]).factory("blockUiCompileFn",["blockUiPreLinkFn",function(t){return function(e){return e.append('<div block-ui-container class="block-ui-container"></div>'),{pre:t}}}]).factory("blockUiPreLinkFn",["blockUI","blockUIUtils","blockUIConfig",function(t,e,o){return function(c,r,i){r.hasClass("block-ui")||r.addClass(o.cssClass),i.$observe("blockUiMessageClass",function(t){c.$_blockUiMessageClass=t});var a=i.blockUi||"_"+c.$id,l=t.instances.get(a);if("main"===a)n(c,l,o);else{var s=r.inheritedData("block-ui");s&&(l._parent=s)}c.$on("$destroy",function(){l.release()}),l.addRef(),c.$_blockUiState=l.state(),c.$watch("$_blockUiState.blocking",function(t){r.attr("aria-busy",!!t),r.toggleClass("block-ui-visible",!!t)}),c.$watch("$_blockUiState.blockCount > 0",function(t){r.toggleClass("block-ui-active",!!t)});var u=i.blockUiPattern;if(u){var f=e.buildRegExp(u);l.pattern(f)}r.data("block-ui",l)}}]),o.constant("blockUIConfig",{templateUrl:"angular-block-ui/angular-block-ui.ng.html",delay:250,message:"Loading ...",autoBlock:!0,resetOnException:!0,requestFilter:t.noop,autoInjectBodyBlock:!0,cssClass:"block-ui block-ui-anim-fade",blockBrowserNavigation:!1}),o.factory("blockUIHttpInterceptor",["$q","$injector","blockUIConfig","$templateCache",function(t,e,n,o){function c(){a=a||e.get("blockUI")}function r(t){n.autoBlock&&t&&!t.$_noBlock&&t.$_blocks&&(c(),t.$_blocks.stop())}function i(e){try{r(e.config)}catch(n){console.log("httpRequestError",n)}return t.reject(e)}var a;return{request:function(t){if(n.autoBlock&&("GET"!=t.method||!o.get(t.url))){var e=n.requestFilter(t);e===!1?t.$_noBlock=!0:(c(),t.$_blocks=a.instances.locate(t),t.$_blocks.start(e))}return t},requestError:i,response:function(t){return t&&r(t.config),t},responseError:i}}]),o.factory("blockUI",["blockUIConfig","$timeout","blockUIUtils","$document",function(e,n,o,c){function r(r){var a,s=this,u={id:r,blockCount:0,message:e.message,blocking:!1},f=[];this._id=r,this._refs=0,this.start=function(r){r=u.blockCount>0?r||u.message||e.message:r||e.message,u.message=r,u.blockCount++;var i=t.element(c[0].activeElement);i.length&&o.isElementInBlockScope(i,s)&&(s._restoreFocus=i[0],n(function(){s._restoreFocus&&s._restoreFocus.blur()})),a||(a=n(function(){a=null,u.blocking=!0},e.delay))},this._cancelStartTimeout=function(){a&&(n.cancel(a),a=null)},this.stop=function(){u.blockCount=Math.max(0,--u.blockCount),0===u.blockCount&&s.reset(!0)},this.message=function(t){u.message=t},this.pattern=function(t){return void 0!==t&&(s._pattern=t),s._pattern},this.reset=function(e){s._cancelStartTimeout(),u.blockCount=0,u.blocking=!1,!s._restoreFocus||c[0].activeElement&&c[0].activeElement!==i[0]||(s._restoreFocus.focus(),s._restoreFocus=null);try{e&&t.forEach(f,function(t){t()})}finally{f.length=0}},this.done=function(t){f.push(t)},this.state=function(){return u},this.addRef=function(){s._refs+=1},this.release=function(){--s._refs<=0&&l.instances._destroy(s)}}var i=c.find("body"),a=[];a.get=function(t){if(!isNaN(t))throw new Error("BlockUI id cannot be a number");var e=a[t];return e||(e=a[t]=new r(t),a.push(e)),e},a._destroy=function(e){if(t.isString(e)&&(e=a[e]),e){e.reset();var n=o.indexOf(a,e);a.splice(n,1),delete a[e.state().id]}},a.locate=function(t){var e=[];o.forEachFnHook(e,"start"),o.forEachFnHook(e,"stop");for(var n=a.length;n--;){var c=a[n],r=c._pattern;r&&r.test(t.url)&&e.push(c)}return 0===e.length&&e.push(l),e},o.forEachFnHook(a,"reset");var l=a.get("main");return l.addRef(),l.instances=a,l}]),o.factory("blockUIUtils",function(){var e=t.element,n={buildRegExp:function(t){var e,n=t.match(/^\/(.*)\/([gim]*)$/);if(!n)throw Error("Incorrect regular expression format: "+t);return e=new RegExp(n[1],n[2])},forEachFn:function(t,e,n){for(var o=t.length;o--;){var c=t[o];c[e].apply(c,n)}},forEachFnHook:function(t,e){t[e]=function(){n.forEachFn(this,e,arguments)}},isElementInBlockScope:function(t,e){for(var n=t.inheritedData("block-ui");n;){if(n===e)return!0;n=n._parent}return!1},findElement:function(t,o,c){var r=null;if(o(t))r=t;else{var i;i=c?t.parent():t.children();for(var a=i.length;!r&&a--;)r=n.findElement(e(i[a]),o,c)}return r},indexOf:function(t,e,n){for(var o=n||0,c=t.length;c>o;o++)if(t[o]===e)return o;return-1}};return n}),t.module("blockUI").run(["$templateCache",function(t){t.put("angular-block-ui/angular-block-ui.ng.html",'<div class="block-ui-overlay"></div><div class="block-ui-message-container" aria-live="assertive" aria-atomic="true"><div class="block-ui-message" ng-class="$_blockUiMessageClass">{{ state.message }}</div></div>')}])}(angular);
//# sourceMappingURL=angular-block-ui.min.js.map