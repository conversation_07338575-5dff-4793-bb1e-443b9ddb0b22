{"version": 3, "sources": ["angular-block-ui-header.js", "angular-block-ui.js", "block-navigation.js", "block-ui-container-directive.js", "block-ui-directive.js", "config.js", "interceptor.js", "service.js", "utils.js", "angular-block-ui-templates.js", "angular-block-ui-footer.js"], "names": [], "mappings": "AAAA;GACA;GACA;GACA;AACA;AACA;;ACLA;;AAEA,aAAA,8BAAA;;EAEA;IACA;MACA;;MAEA;;QAEA;;QAEA;UACA;YACA;YACA;UACA;YACA;UACA;QACA;;QAEA;MACA;IACA;EACA;;EAEA;AACA,CAAA,CAAA;;AAEA,UAAA,iDAAA;EACA;IACA;EACA;;EAEA;;IAEA;IACA;;IAEA;IACA;EACA;AACA,CAAA,CAAA;;AAEA;EACA;IACA;EACA;IACA;EACA;EACA;AACA;ACnDA,aAAA,aAAA;EACA;AACA,CAAA,CAAA;;AAEA;EACA;EACA;;IAEA;;MAEA;;MAEA;;MAEA;QACA;QACA;;UAEA;;UAEA;;UAEA;;UAEA;;YAEA;;YAEA;UACA;;UAEA;QACA;MACA;;MAEA;;IAEA;;IAEA;AACA;;AAEA;;AAEA;;EAEA;;IAEA;;MAEA;;QAEA;;QAEA;UACA;QACA;MACA;;MAEA;QACA;;QAEA;MACA;IACA;;IAEA;;MAEA;MACA;;MAEA;;QAEA;QACA;;QAEA;QACA;;MAEA;;IAEA;MACA;IACA;;EAEA;AACA;ACtFA,oCAAA,4CAAA;EACA;IACA;IACA;IACA;IACA;MACA;IACA;EACA;AACA,CAAA,CAAA,oCAAA,4BAAA;;EAEA;;IAEA;;IAEA;MACA;IACA;;IAEA;;IAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;EACA;AACA,CAAA,CAAA;AC/BA,2BAAA,qBAAA;;EAEA;IACA;IACA;IACA;EACA;;AAEA,CAAA,CAAA,8BAAA,qBAAA;;EAEA;;IAEA;;IAEA;;IAEA;MACA;IACA;;EAEA;;AAEA,CAAA,CAAA,8BAAA,6CAAA;;EAEA;;IAEA;IACA;;IAEA;MACA;IACA;;IAEA;;IAEA;MACA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;;IAEA;MACA;IACA;MACA;MACA;;MAEA;QACA;QACA;MACA;IACA;;IAEA;;IAEA;MACA;IACA;;IAEA;;IAEA;;IAEA;;IAEA;;IAEA;MACA;MACA;MACA;IACA;;IAEA;MACA;IACA;;IAEA;;IAEA;;IAEA;MACA;MACA;IACA;;IAEA;;IAEA;;EAEA;;AAEA,CAAA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACtHA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;;;ACVA,wCAAA,uDAAA;;EAEA;;EAEA;IACA;EACA;;EAEA;IACA;MACA;MACA;IACA;EACA;;EAEA;;IAEA;MACA;IACA;MACA;IACA;;IAEA;EACA;;EAEA;IACA;;MAEA;MACA;;MAEA;QACA;;QAEA;;QAEA;;QAEA;UACA;UACA;QACA;;UAEA;;UAEA;UACA;QACA;MACA;;MAEA;IACA;;IAEA;;IAEA;;MAEA;MACA;;MAEA;QACA;MACA;;MAEA;IACA;;IAEA;EACA;;AAEA,CAAA,CAAA;;ACvEA,yBAAA,2DAAA;;EAEA;;EAEA;;IAEA;;IAEA;MACA;MACA;MACA;MACA;IACA;;IAEA;;IAEA;;IAEA;;MAEA;QACA;MACA;QACA;MACA;;MAEA;;MAEA;;MAEA;;MAEA;;MAEA;;QAEA;QACA;;QAEA;;QAEA;QACA;QACA;;QAEA;UACA;UACA;YACA;UACA;QACA;MACA;;MAEA;QACA;UACA;UACA;QACA;MACA;IACA;;IAEA;MACA;QACA;QACA;MACA;IACA;;IAEA;MACA;;MAEA;QACA;MACA;IACA;;IAEA;MACA;IACA;;IAEA;MACA;QACA;MACA;;MAEA;IACA;;IAEA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;;MAEA;SACA;QACA;QACA;MACA;;MAEA;QACA;UACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;;IAEA;MACA;IACA;;IAEA;MACA;IACA;;IAEA;MACA;IACA;;IAEA;MACA;QACA;MACA;IACA;EACA;;EAEA;;EAEA;;IAEA;MACA;IACA;;IAEA;;IAEA;MACA;MACA;MACA;IACA;;IAEA;EACA;;EAEA;IACA;MACA;IACA;;IAEA;MACA;;MAEA;MACA;;MAEA;IACA;EACA;;EAEA;;IAEA;;IAEA;IACA;;IAEA;IACA;;IAEA;;IAEA;MACA;MACA;;MAEA;QACA;MACA;IACA;;IAEA;MACA;IACA;;IAEA;EACA;;EAEA;;EAEA;;EAEA;;EAEA;EACA;;EAEA;AACA,CAAA,CAAA;;;AC9MA;;EAEA;;EAEA;IACA;MACA;;MAEA;QACA;MACA;QACA;MACA;;MAEA;IACA;IACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;MACA;QACA;MACA;IACA;IACA;MACA;;MAEA;QACA;UACA;QACA;;QAEA;MACA;;MAEA;IACA;IACA;MACA;;MAEA;QACA;MACA;;QAEA;;QAEA;UACA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;MACA;;MAEA;IACA;IACA;AACA;AACA;AACA;;MAEA;QACA;UACA;QACA;MACA;;MAEA;IACA;EACA;;EAEA;;AAEA;AClFA;AACA;AACA;AACA;EACA;AACA;ACLA", "sourcesContent": ["/*!\n   angular-block-ui v0.2.0\n   (c) 2015 (null) McNull https://github.com/McNull/angular-block-ui\n   License: MIT\n*/\n(function(angular) {\n", "var blkUI = angular.module('blockUI', []);\n\nblkUI.config(function ($provide, $httpProvider) {\n\n  $provide.decorator('$exceptionHandler', ['$delegate', '$injector',\n    function ($delegate, $injector) {\n      var blockUI, blockUIConfig;\n\n      return function (exception, cause) {\n\n        blockUIConfig = blockUIConfig || $injector.get('blockUIConfig');\n\n        if (blockUIConfig.resetOnException) {\n          try {\n            blockUI = blockUI || $injector.get('blockUI');\n            blockUI.instances.reset();\n          } catch (ex) {\n            console.log('$exceptionHandler', exception);\n          }\n        }\n\n        $delegate(exception, cause);\n      };\n    }\n  ]);\n\n  $httpProvider.interceptors.push('blockUIHttpInterceptor');\n});\n\nblkUI.run(function ($document, blockUIConfig, $templateCache) {\n  if (blockUIConfig.autoInjectBodyBlock) {\n    $document.find('body').attr('block-ui', 'main');\n  }\n\n  if (blockUIConfig.template) {\n\n    // Swap the builtin template with the custom template.\n    // Create a magic cache key and place the template in the cache.\n\n    blockUIConfig.templateUrl = '$$block-ui-template$$';\n    $templateCache.put(blockUIConfig.templateUrl, blockUIConfig.template);\n  }\n});\n\nfunction moduleLoaded(name) {\n  try {\n    angular.module(name);\n  } catch(ex) {\n    return false;\n  }\n  return true;\n}", "blkUI.config(function ($provide) {\n  $provide.decorator('$location', decorateLocation);\n});\n\nvar decorateLocation = [\n  '$delegate', 'blockUI', 'blockUIConfig',\n  function ($delegate, blockUI, blockUIConfig) {\n\n    if (blockUIConfig.blockBrowserNavigation) {\n\n      blockUI.$_blockLocationChange = true;\n\n      var overrides = ['url', 'path', 'search', 'hash', 'state'];\n\n      function hook(f) {\n        var s = $delegate[f];\n        $delegate[f] = function () {\n\n          //        console.log(f, Date.now(), arguments);\n\n          var result = s.apply($delegate, arguments);\n\n          // The call was a setter if the $location service is returned.\n\n          if (result === $delegate) {\n\n            // Mark the mainblock ui to allow the location change.\n\n            blockUI.$_blockLocationChange = false;\n          }\n\n          return result;\n        };\n      }\n\n      angular.forEach(overrides, hook);\n\n    }\n\n    return $delegate;\n}];\n\n// Called from block-ui-directive for the 'main' instance.\n\nfunction blockNavigation($scope, mainBlockUI, blockUIConfig) {\n\n  if (blockUIConfig.blockBrowserNavigation) {\n\n    function registerLocationChange() {\n\n      $scope.$on('$locationChangeStart', function (event) {\n\n        //        console.log('$locationChangeStart', mainBlockUI.$_blockLocationChange + ' ' + mainBlockUI.state().blockCount);\n\n        if (mainBlockUI.$_blockLocationChange && mainBlockUI.state().blockCount > 0) {\n          event.preventDefault();\n        }\n      });\n\n      $scope.$on('$locationChangeSuccess', function () {\n        mainBlockUI.$_blockLocationChange = blockUIConfig.blockBrowserNavigation;\n\n        //        console.log('$locationChangeSuccess', mainBlockUI.$_blockLocationChange + ' ' + mainBlockUI.state().blockCount);\n      });\n    }\n\n    if (moduleLoaded('ngRoute')) {\n\n      // After the initial content has been loaded we'll spy on any location\n      // changes and discard them when needed.\n\n      var fn = $scope.$on('$viewContentLoaded', function () {\n\n        // Unhook the view loaded and hook a function that will prevent\n        // location changes while the block is active.\n\n        fn();\n        registerLocationChange();\n\n      });\n\n    } else {\n      registerLocationChange();\n    }\n\n  }\n}", "blkUI.directive('blockUiContainer', function (blockUIConfig, blockUiContainerLinkFn) {\n  return {\n    scope: true,\n    restrict: 'A',\n    templateUrl: blockUIConfig.templateUrl,\n    compile: function($element) {\n      return blockUiContainerLinkFn;\n    }\n  };\n}).factory('blockUiContainerLinkFn', function (blockUI, blockUIUtils) {\n\n  return function ($scope, $element, $attrs) {\n\n    var srvInstance = $element.inheritedData('block-ui');\n\n    if (!srvInstance) {\n      throw new Error('No parent block-ui service instance located.');\n    }\n\n    // Expose the state on the scope\n\n    $scope.state = srvInstance.state();\n\n//    $scope.$watch('state.blocking', function(value) {\n//      $element.toggleClass('block-ui-visible', !!value);\n//    });\n//\n//    $scope.$watch('state.blockCount > 0', function(value) {\n//      $element.toggleClass('block-ui-active', !!value);\n//    });\n  };\n});", "blkUI.directive('blockUi', function (blockUiCompileFn) {\n\n  return {\n    scope: true,\n    restrict: 'A',\n    compile: blockUiCompileFn\n  };\n\n}).factory('blockUiCompileFn', function (blockUiPreLinkFn) {\n\n  return function ($element, $attrs) {\n\n    // Class should be added here to prevent an animation delay error.\n\n    $element.append('<div block-ui-container class=\"block-ui-container\"></div>');\n\n    return {\n      pre: blockUiPreLinkFn\n    };\n\n  };\n\n}).factory('blockUiPreLinkFn', function (blockUI, blockUIUtils, blockUIConfig) {\n\n  return function ($scope, $element, $attrs) {\n\n    // If the element does not have the class \"block-ui\" set, we set the\n    // default css classes from the config.\n\n    if (!$element.hasClass('block-ui')) {\n      $element.addClass(blockUIConfig.cssClass);\n    }\n\n    // Expose the blockUiMessageClass attribute value on the scope\n\n    $attrs.$observe('blockUiMessageClass', function (value) {\n      $scope.$_blockUiMessageClass = value;\n    });\n\n    // Create the blockUI instance\n    // Prefix underscore to prevent integers:\n    // https://github.com/McNull/angular-block-ui/pull/8\n\n    var instanceId = $attrs.blockUi || '_' + $scope.$id;\n    var srvInstance = blockUI.instances.get(instanceId);\n\n    // If this is the main (topmost) block element we'll also need to block any\n    // location changes while the block is active.\n\n    if (instanceId === 'main') {\n      blockNavigation($scope, srvInstance, blockUIConfig);\n    } else {\n      // Locate the parent blockUI instance\n      var parentInstance = $element.inheritedData('block-ui');\n\n      if (parentInstance) {\n        // TODO: assert if parent is already set to something else\n        srvInstance._parent = parentInstance;\n      }\n    }\n\n    // Ensure the instance is released when the scope is destroyed\n\n    $scope.$on('$destroy', function () {\n      srvInstance.release();\n    });\n\n    // Increase the reference count\n\n    srvInstance.addRef();\n\n    // Expose the state on the scope\n\n    $scope.$_blockUiState = srvInstance.state();\n\n    $scope.$watch('$_blockUiState.blocking', function (value) {\n      // Set the aria-busy attribute if needed\n      $element.attr('aria-busy', !!value);\n      $element.toggleClass('block-ui-visible', !!value);\n    });\n\n    $scope.$watch('$_blockUiState.blockCount > 0', function (value) {\n      $element.toggleClass('block-ui-active', !!value);\n    });\n\n    // If a pattern is provided assign it to the state\n\n    var pattern = $attrs.blockUiPattern;\n\n    if (pattern) {\n      var regExp = blockUIUtils.buildRegExp(pattern);\n      srvInstance.pattern(regExp);\n    }\n\n    // Store a reference to the service instance on the element\n\n    $element.data('block-ui', srvInstance);\n\n  };\n\n});\n//.factory('blockUiPostLinkFn', function(blockUIUtils) {\n//\n//  return function($scope, $element, $attrs) {\n//\n//    var $message;\n//\n//    $attrs.$observe('blockUiMessageClass', function(value) {\n//\n//      $message = $message || blockUIUtils.findElement($element, function($e) {\n//        return $e.hasClass('block-ui-message');\n//      });\n//\n//      $message.addClass(value);\n//\n//    });\n//  };\n//\n//});", "blkUI.constant('blockUIConfig', {\n    templateUrl: 'angular-block-ui/angular-block-ui.ng.html',\n    delay: 250,\n    message: \"Loading ...\",\n    autoBlock: true,\n    resetOnException: true,\n    requestFilter: angular.noop,\n    autoInjectBodyBlock: true,\n    cssClass: 'block-ui block-ui-anim-fade',\n    blockBrowserNavigation: false\n});\n\n", "blkUI.factory('blockUIHttpInterceptor', function($q, $injector, blockUIConfig, $templateCache) {\n\n  var blockUI;\n\n  function injectBlockUI() {\n    blockUI = blockUI || $injector.get('blockUI');\n  }\n\n  function stopBlockUI(config) {\n    if (blockUIConfig.autoBlock && (config && !config.$_noBlock && config.$_blocks)) {\n      injectBlockUI();\n      config.$_blocks.stop();\n    }\n  }\n\n  function error(rejection) {\n\n    try {\n      stopBlockUI(rejection.config);\n    } catch(ex) {\n      console.log('httpRequestError', ex);\n    }\n\n    return $q.reject(rejection);\n  }\n\n  return {\n    request: function(config) {\n\n      // Only block when autoBlock is enabled ...\n      // ... and the request doesn't match a cached template.\n\n      if (blockUIConfig.autoBlock &&\n        !(config.method == 'GET' && $templateCache.get(config.url))) {\n\n        // Don't block excluded requests\n\n        var result = blockUIConfig.requestFilter(config);\n\n        if (result === false) {\n          // Tag the config so we don't unblock this request\n          config.$_noBlock = true;\n        } else {\n\n          injectBlockUI();\n\n          config.$_blocks = blockUI.instances.locate(config);\n          config.$_blocks.start(result);\n        }\n      }\n\n      return config;\n    },\n\n    requestError: error,\n\n    response: function(response) {\n\n      // If the connection to the website goes down the response interceptor gets and error with \"cannot read property config of null\".\n      // https://github.com/McNull/angular-block-ui/issues/53\n\n      if(response) {\n        stopBlockUI(response.config);\n      }\n\n      return response;\n    },\n\n    responseError: error\n  };\n\n});\n", "blkUI.factory('blockUI', function(blockUIConfig, $timeout, blockUIUtils, $document) {\n\n  var $body = $document.find('body');\n\n  function BlockUI(id) {\n\n    var self = this;\n\n    var state = {\n      id: id,\n      blockCount: 0,\n      message: blockUIConfig.message,\n      blocking: false\n    }, startPromise, doneCallbacks = [];\n\n    this._id = id;\n\n    this._refs = 0;\n\n    this.start = function(message) {\n\n      if(state.blockCount > 0) {\n        message = message || state.message || blockUIConfig.message;\n      } else {\n        message = message || blockUIConfig.message;\n      }\n\n      state.message = message;\n\n      state.blockCount++;\n\n      // Check if the focused element is part of the block scope\n\n      var $ae = angular.element($document[0].activeElement);\n\n      if($ae.length && blockUIUtils.isElementInBlockScope($ae, self)) {\n\n        // Let the active element lose focus and store a reference \n        // to restore focus when we're done (reset)\n\n        self._restoreFocus = $ae[0];\n\n        // https://github.com/McNull/angular-block-ui/issues/13\n        // http://stackoverflow.com/questions/22698058/apply-already-in-progress-error-when-using-typeahead-plugin-found-to-be-relate\n        // Queue the blur after any ng-blur expression.\n\n        $timeout(function() {\n          // Ensure we still need to blur\n          if(self._restoreFocus) {\n            self._restoreFocus.blur();\n          }\n        });\n      }\n\n      if (!startPromise) {\n        startPromise = $timeout(function() {\n          startPromise = null;\n          state.blocking = true;\n        }, blockUIConfig.delay);\n      }\n    };\n\n    this._cancelStartTimeout = function() {\n      if (startPromise) {\n        $timeout.cancel(startPromise);\n        startPromise = null;\n      }\n    };\n\n    this.stop = function() {\n      state.blockCount = Math.max(0, --state.blockCount);\n\n      if (state.blockCount === 0) {\n        self.reset(true);\n      }\n    };\n\n    this.message = function(value) {\n      state.message = value;\n    };\n\n    this.pattern = function(regexp) {\n      if (regexp !== undefined) {\n        self._pattern = regexp;\n      }\n\n      return self._pattern;\n    };\n\n    this.reset = function(executeCallbacks) {\n      \n      self._cancelStartTimeout();\n      state.blockCount = 0;\n      state.blocking = false;\n\n      // Restore the focus to the element that was active\n      // before the block start, but not if the user has \n      // focused something else while the block was active.\n\n      if(self._restoreFocus && \n         (!$document[0].activeElement || $document[0].activeElement === $body[0])) {\n        self._restoreFocus.focus();\n        self._restoreFocus = null;\n      }\n      \n      try {\n        if (executeCallbacks) {\n          angular.forEach(doneCallbacks, function(cb) {\n            cb();\n          });\n        }\n      } finally {\n        doneCallbacks.length = 0;\n      }\n    };\n\n    this.done = function(fn) {\n      doneCallbacks.push(fn);\n    };\n\n    this.state = function() {\n      return state;\n    };\n\n    this.addRef = function() {\n      self._refs += 1;\n    };\n\n    this.release = function() {\n      if(--self._refs <= 0) {\n        mainBlock.instances._destroy(self);\n      }\n    };\n  }\n\n  var instances = [];\n\n  instances.get = function(id) {\n\n    if(!isNaN(id)) {\n      throw new Error('BlockUI id cannot be a number');\n    }\n\n    var instance = instances[id];\n\n    if(!instance) {\n      // TODO: ensure no array instance trashing [xxx] -- current workaround: '_' + $scope.$id\n      instance = instances[id] = new BlockUI(id);\n      instances.push(instance);\n    }\n\n    return instance;\n  };\n\n  instances._destroy = function(idOrInstance) {\n    if (angular.isString(idOrInstance)) {\n      idOrInstance = instances[idOrInstance];\n    }\n\n    if (idOrInstance) {\n      idOrInstance.reset();\n\n      var i = blockUIUtils.indexOf(instances, idOrInstance);\n      instances.splice(i, 1);\n\n      delete instances[idOrInstance.state().id];\n    }\n  };\n  \n  instances.locate = function(request) {\n\n    var result = [];\n\n    // Add function wrappers that will be executed on every item\n    // in the array.\n    \n    blockUIUtils.forEachFnHook(result, 'start');\n    blockUIUtils.forEachFnHook(result, 'stop');\n\n    var i = instances.length;\n\n    while(i--) {\n      var instance = instances[i];\n      var pattern = instance._pattern;\n\n      if(pattern && pattern.test(request.url)) {\n        result.push(instance);\n      }\n    }\n\n    if(result.length === 0) {\n      result.push(mainBlock);\n    }\n\n    return result;\n  };\n\n  // Propagate the reset to all instances\n\n  blockUIUtils.forEachFnHook(instances, 'reset');\n\n  var mainBlock = instances.get('main');\n\n  mainBlock.addRef();\n  mainBlock.instances = instances;\n\n  return mainBlock;\n});\n", "\nblkUI.factory('blockUIUtils', function() {\n\n  var $ = angular.element;\n\n  var utils = {\n    buildRegExp: function(pattern) {\n      var match = pattern.match(/^\\/(.*)\\/([gim]*)$/), regExp;\n\n      if(match) {\n        regExp = new RegExp(match[1], match[2]);\n      } else {\n        throw Error('Incorrect regular expression format: ' + pattern);\n      }\n\n      return regExp;\n    },\n    forEachFn: function(arr, fnName, args) {\n      var i = arr.length;\n      while(i--) {\n        var t = arr[i];\n        t[fnName].apply(t, args);\n      }\n    },\n    forEachFnHook: function(arr, fnName) {\n      arr[fnName] = function() {\n        utils.forEachFn(this, fnName, arguments);\n      }\n    },\n    isElementInBlockScope: function($element, blockScope) {\n      var c = $element.inheritedData('block-ui');\n\n      while(c) {\n        if(c === blockScope) {\n          return true;\n        }\n\n        c = c._parent;\n      }\n\n      return false;\n    },\n    findElement: function ($element, predicateFn, traverse) {\n      var ret = null;\n\n      if (predicateFn($element)) {\n        ret = $element;\n      } else {\n\n        var $elements;\n\n        if (traverse) {\n          $elements = $element.parent();\n        } else {\n          $elements = $element.children();\n        }\n\n        var i = $elements.length;\n        while (!ret && i--) {\n          ret = utils.findElement($($elements[i]), predicateFn, traverse);\n        }\n      }\n\n      return ret;\n    },\n    indexOf: function(arr, obj, start) {\n//      if(Array.prototype.indexOf) {\n//        return arr.indexOf(obj, start);\n//      }\n\n      for (var i = (start || 0), j = arr.length; i < j; i++) {\n        if (arr[i] === obj) {\n          return i;\n        }\n      }\n\n      return -1;\n    }\n  };\n\n  return utils;\n\n});", "// Automatically generated.\n// This file is already embedded in your main javascript output, there's no need to include this file\n// manually in the index.html. This file is only here for your debugging pleasures.\nangular.module('blockUI').run(['$templateCache', function($templateCache){\n  $templateCache.put('angular-block-ui/angular-block-ui.ng.html', '<div class=\\\"block-ui-overlay\\\"></div><div class=\\\"block-ui-message-container\\\" aria-live=\\\"assertive\\\" aria-atomic=\\\"true\\\"><div class=\\\"block-ui-message\\\" ng-class=\\\"$_blockUiMessageClass\\\">{{ state.message }}</div></div>');\n}]);", "})(angular);"], "file": "angular-block-ui.js", "sourceRoot": "../src/angular-block-ui"}