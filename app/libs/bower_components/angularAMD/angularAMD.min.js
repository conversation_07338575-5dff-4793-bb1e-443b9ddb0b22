/*!
 angularAMD v0.2.1
 (c) 2013-2014 <PERSON> https://github.com/marcoslin/
 License: MIT
*/

define(function(){"use strict";function a(){if(!l)throw new Error("angularAMD not initialized.  Need to call angularAMD.bootstrap(app) first.")}function b(){if(k)throw new Error("setAlternateAngular can only be called once.");k={},a(),j.extend(k,j),k.module=function(a,b){if("undefined"==typeof b)return q.hasOwnProperty(a)?p[a]:j.module(a);var c=j.module.apply(null,arguments),d={name:a,module:c};return r.push(d),j.extend(c,n),q[a]=!0,p[a]=c,c},window.angular=k}function c(){}function d(a){return function(b,c){return l?n[a](b,c):o.push({recipe:a,name:b,constructor:c}),this}}var e,f,g,h,i,j,k,l=!1,m={},n={},o=[],p={},q={},r=[];return c.prototype.route=function(a){var b;if(a.hasOwnProperty("controllerUrl")?(b=a.controllerUrl,delete a.controllerUrl,"undefined"==typeof a.controller&&(a.controller=["$scope","__AAMDCtrl","$injector",function(a,b,c){"undefined"!=typeof b&&c.invoke(b,this,{$scope:a})}])):"string"==typeof a.controller&&(b=a.controller),b){var c=a.resolve||{};c.__AAMDCtrl=["$q","$rootScope",function(a,c){var d=a.defer();return require([b],function(a){d.resolve(a),c.$apply()}),d.promise}],a.resolve=c}return a},c.prototype.appname=function(){return a(),e},c.prototype.processQueue=function(){function b(a){h.invoke(a)}if(a(),"undefined"==typeof k)throw new Error("Alternate angular not set.  Make sure that `enable_ngload` option has been set when calling angularAMD.bootstrap");for(;r.length;){var c,d=r.shift(),e=d.module._invokeQueue;for(c=0;c<e.length;c+=1){var f=e[c],g=f[0],j=f[1],l=f[2];if(m.hasOwnProperty(g)){var n;n="$injector"===g&&"invoke"===j?i:m[g],n[j].apply(null,l)}else window.console&&window.console.error('"'+g+'" not found!!!')}if(d.module._configBlocks){var o=d.module._configBlocks;for(c=0;c<o.length;c+=1){var q=o[c],s=q[1],t=q[2];i[s].apply(null,t)}}d.module._runBlocks&&angular.forEach(d.module._runBlocks,b),p={}}},c.prototype.getCachedProvider=function(b){a();var c;switch(b){case"__orig_angular":c=j;break;case"__alt_angular":c=k;break;case"__orig_app":c=f;break;case"__alt_app":c=g;break;default:c=m[b]}return c},c.prototype.inject=function(){return a(),h.invoke.apply(null,arguments)},c.prototype.config=function(){return a(),i.invoke.apply(null,arguments)},c.prototype.reset=function(){"undefined"!=typeof j&&(window.angular=j,f=void 0,g=void 0,k=void 0,j=void 0,n={},o=[],r=[],e=void 0,h=void 0,i=void 0,m={},l=!1)},c.prototype.bootstrap=function(a,c,d){if(l)throw Error("bootstrap can only be called once.");if("undefined"==typeof c&&(c=!0),j=angular,f=a,g={},j.extend(g,f),d=d||document.documentElement,a.config(["$controllerProvider","$compileProvider","$filterProvider","$animateProvider","$provide","$injector",function(a,b,c,d,e,f){i=f,m={$controllerProvider:a,$compileProvider:b,$filterProvider:c,$animateProvider:d,$provide:e},angular.extend(n,{provider:function(a,b){return e.provider(a,b),this},controller:function(b,c){return a.register(b,c),this},directive:function(a,c){return b.directive(a,c),this},filter:function(a,b){return c.register(a,b),this},factory:function(a,b){return e.factory(a,b),this},service:function(a,b){return e.service(a,b),this},constant:function(a,b){return e.constant(a,b),this},value:function(a,b){return e.value(a,b),this},animation:angular.bind(d,d.register)}),angular.extend(g,n)}]),a.run(["$injector",function(a){h=a,m.$injector=h}]),e=a.name,o.length>0){for(var k=0;k<o.length;k+=1){var p=o[k];f[p.recipe](p.name,p.constructor)}o=[]}return f.register=n,j.element(document).ready(function(){j.bootstrap(d,[e]),l=!0,c&&b()}),g},c.prototype.provider=d("provider"),c.prototype.controller=d("controller"),c.prototype.directive=d("directive"),c.prototype.filter=d("filter"),c.prototype.factory=d("factory"),c.prototype.service=d("service"),c.prototype.constant=d("constant"),c.prototype.value=d("value"),c.prototype.animation=d("animation"),new c});
//# sourceMappingURL=angularAMD.min.map