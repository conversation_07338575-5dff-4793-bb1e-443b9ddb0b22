angular.module("siyfion.sfTypeahead",[]).directive("sfTypeahead",function(){return{restrict:"AC",require:"?ngModel",scope:{options:"=",datasets:"=",suggestionKey:"@"},link:function(a,b,c,d){function e(){if(j)b.typeahead(a.options,a.datasets),j=!1;else{var c=b.val();b.typeahead("destroy"),b.typeahead(a.options,a.datasets),d.$setViewValue(c),b.trigger<PERSON><PERSON><PERSON>("typeahead:opened")}}function f(a,b){var c=-1;return angular.forEach(a,function(a,d){angular.equals(b,a)&&(c=d)}),c>=0}function g(b,c){a.$apply(function(){var b=angular.isDefined(a.suggestionKey)?c[a.suggestionKey]:c;d.$setViewValue(b)})}var h=a.options||{},i=(angular.isArray(a.datasets)?a.datasets:[a.datasets])||[],j=!0;e(),a.$watch("options",e),angular.isArray(a.datasets)?a.$watchCollection("datasets",e):a.$watch("datasets",e),d.$parsers.push(function(a){var b=angular.isObject(a);return h.editable===!1?(d.$setValidity("typeahead",b),b?a:void 0):a}),d.$formatters.push(function(c){if(angular.isObject(c)){var e=!1;return $.each(i,function(g,j){function k(j){var k=f(j,c);k?(d.$setViewValue(c),e=!0):d.$setViewValue(h.editable===!1?void 0:c),(e||g===i.length-1)&&setTimeout(function(){a.$apply(function(){b.typeahead("val",n)})},0)}var l=j.source,m=j.displayKey||"value",n=(angular.isFunction(m)?m(c):c[m])||"";return e?!1:n?void l(n,k):void k([])}),""}return null==c&&b.typeahead("val",null),c}),b.bind("typeahead:selected",function(b,c,d){g(b,c,d),a.$emit("typeahead:selected",c,d)}),b.bind("typeahead:autocompleted",function(b,c,d){g(b,c,d),a.$emit("typeahead:autocompleted",c,d)}),b.bind("typeahead:opened",function(){a.$emit("typeahead:opened")}),b.bind("typeahead:closed",function(){a.$emit("typeahead:closed")}),b.bind("typeahead:cursorchanged",function(b,c,d){a.$emit("typeahead:cursorchanged",b,c,d)})}}});