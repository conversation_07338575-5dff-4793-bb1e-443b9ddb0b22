{"name": "angular-chart.js", "version": "0.7.6", "main": ["./dist/angular-chart.js", "./dist/angular-chart.css"], "authors": ["<PERSON>-<PERSON><PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/jtblin/angular-chart.js.git"}, "description": "An angular.js wrapper for Chart.js - reactive, responsive, beautiful charts.", "moduleType": ["globals"], "keywords": ["angular", "angular.js", "chartjs", "chart", "reactive", "responsive", "graph", "bar", "line", "area", "donut"], "license": "BSD", "ignore": ["**/.*", "node_modules", "bower_components", "examples", "test", "tests"], "dependencies": {"angular": "1.x", "Chart.js": "~1.0.1"}, "devDependencies": {"angular-bootstrap": "~0.11.0", "font-awesome": "~4.1.0", "rainbow": "~1.1.9", "Chart.StackedBar.js": "~1.0.1", "angular-mocks": "1.x"}, "resolutions": {"Chart.js": "~1.0.1", "angular": "1.x", "angular-mocks": "1.3.10"}}