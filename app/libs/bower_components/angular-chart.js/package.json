{"name": "angular-chart.js", "version": "0.7.6", "description": "An angular.js wrapper for Chart.js", "main": "dist/angular-chart.js", "directories": {"example": "examples"}, "scripts": {"test": "gulp check"}, "author": "<PERSON>-<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/jtblin/angular-chart.js.git"}, "license": "BSD", "devDependencies": {"chai": "^1.10.0", "chai-string": "^1.1.1", "cp": "^0.2.0", "gm": "^1.17.0", "gulp": "^3.9.0", "gulp-bump": "^0.1.11", "gulp-csso": "^0.2.9", "gulp-git": "^0.5.6", "gulp-gzip": "0.0.8", "gulp-jscs": "^1.4.0", "gulp-jshint": "^1.9.2", "gulp-less": "^1.3.1", "gulp-ng-annotate": "^0.5.2", "gulp-rename": "^1.2.0", "gulp-sequence": "^0.3.1", "gulp-shell": "^0.2.11", "gulp-sourcemaps": "^1.0.0", "gulp-spawn-mocha": "^2.0.1", "gulp-tar": "^0.5.0", "gulp-uglify": "^0.3.1", "imgur-node-api": "^0.1.0", "jshint-stylish": "^1.0.0", "less": "^1.7.3", "mkdirp": "^0.5.0", "mocha": "^2.1.0", "mocha-phantomjs": "^3.6.0", "sinon": "^1.12.2", "sinon-chai": "^2.7.0", "testatic": "^0.1.0", "tmp-sync": "^1.1.0", "webshot": "^0.15.3"}}