{"version": 3, "sources": ["angular-chart.less"], "names": [], "mappings": "AAAA;AAAe;AAAa;AAAc;AAAa;AAAe;AAAmB;EACvF,qBAAA;EACA,eAAA;EACA,kBAAA;;EAEA,wBAAA;;EACA,qBAAA;;EACA,eAAA;;;AAPF,aASE;AATa,WASb;AAT0B,YAS1B;AATwC,WASxC;AATqD,aASrD;AAToE,iBASpE;AATuF,gBASvF;EACE,qBAAA;EACA,mBAAA;EACA,kBAAA;EACA,kBAAA;EACA,kBAAA;EACA,yBAAA;EACA,kBAAA;EACA,eAAA;;AAjBJ,aASE,GAUE;AAnBW,WASb,GAUE;AAnBwB,YAS1B,GAUE;AAnBsC,WASxC,GAUE;AAnBmD,aASrD,GAUE;AAnBkE,iBASpE,GAUE;AAnBqF,gBASvF,GAUE;EACE,cAAA;EACA,kBAAA;EACA,OAAA;EACA,MAAA;EACA,WAAA;EACA,YAAA;EACA,kBAAA", "file": "angular-chart.css", "sourcesContent": [".chart-legend, .bar-legend, .line-legend, .pie-legend, .radar-legend, .polararea-legend, .doughnut-legend {\n  list-style-type: none;\n  margin-top: 5px;\n  text-align: center;\n  /* NOTE: Browsers automatically add 40px of padding-left to all lists, so we should offset that, otherwise the legend is off-center */\n  -webkit-padding-start:0; /* Webkit */\n  -moz-padding-start:0; /* Mozilla */\n  padding-left:0; /* IE (handles all cases, really, but we should also include the vendor-specific properties just to be safe) */\n\n  li {\n    display: inline-block;\n    white-space: nowrap;\n    position: relative;\n    margin-bottom: 4px;\n    border-radius: 5px;\n    padding: 2px 8px 2px 28px;\n    font-size: smaller;\n    cursor: default;\n\n    span {\n      display: block;\n      position: absolute;\n      left: 0;\n      top: 0;\n      width: 20px;\n      height: 20px;\n      border-radius: 5px;\n    }\n  }\n}\n"], "sourceRoot": "/source/"}