{"version": 3, "sources": ["angular-chart.min.js"], "names": ["factory", "define", "amd", "exports", "module", "require", "angular", "Chart", "ChartJsProvider", "options", "ChartJs", "getOptions", "type", "typeOptions", "extend", "this", "setOptions", "customOptions", "$get", "ChartJsFactory", "$timeout", "canUpdateChart", "newVal", "oldVal", "length", "Array", "isArray", "every", "element", "index", "reduce", "sum", "carry", "val", "getEventHandler", "scope", "chart", "action", "evt", "atEvent", "getPointsAtEvent", "getBarsAtEvent", "getSegmentsAtEvent", "activePoints", "call", "$apply", "getColours", "colours", "copy", "defaults", "global", "data", "push", "getColour", "map", "convertColour", "colour", "hexToRgb", "substr", "getRandomColour", "getRandomInt", "fillColor", "rgba", "strokeColor", "pointColor", "pointStrokeColor", "pointHighlightFill", "pointHighlightStroke", "min", "max", "Math", "floor", "random", "alpha", "usingExcanvas", "join", "concat", "hex", "bigint", "parseInt", "r", "g", "b", "getDataSets", "labels", "series", "datasets", "item", "i", "label", "getData", "value", "color", "highlight", "setLegend", "elem", "$parent", "parent", "$oldLegend", "find", "legend", "generateLegend", "replaceWith", "append", "updateChart", "values", "for<PERSON>ach", "dataset", "points", "bars", "dataItem", "j", "segments", "segment", "update", "$emit", "isEmpty", "Object", "keys", "isResponsive", "responsive", "restrict", "chartType", "click", "hover", "link", "reset<PERSON><PERSON>", "equals", "destroy", "createChart", "clientHeight", "container", "cvs", "ctx", "getContext", "document", "createElement", "className", "append<PERSON><PERSON><PERSON>", "window", "G_vmlCanvasManager", "initElement", "$watch", "$on", "multiTooltipTemplate", "animation", "provider", "directive"], "mappings": "CAAC,SAAUA,GACT,YACsB,mBAAXC,SAAyBA,OAAOC,IAEzCD,QAAQ,UAAW,YAAaD,GACJ,gBAAZG,SAEhBC,OAAOD,QAAUH,EAAQK,QAAQ,WAAYA,QAAQ,aAGrDL,EAAQM,QAASC,QAEnB,SAAUD,EAASC,GACnB,YAyCA,SAASC,KACP,GAAIC,MACAC,GACFH,MAAOA,EACPI,WAAY,SAAUC,GACpB,GAAIC,GAAcD,GAAQH,EAAQG,MAClC,OAAON,GAAQQ,UAAWL,EAASI,IAOvCE,MAAKC,WAAa,SAAUJ,EAAMK,GAEhC,MAAMA,IAMNR,EAAQG,GAAQN,EAAQQ,OAAOL,EAAQG,OAAaK,GAApDR,SALEQ,EAAgBL,EAChBH,EAAUH,EAAQQ,OAAOL,EAASQ,GAClC,SAMJF,KAAKG,KAAO,WACV,MAAOR,IAIX,QAASS,GAAgBT,EAASU,GA+FhC,QAASC,GAAgBC,EAAQC,GAC/B,MAAID,IAAUC,GAAUD,EAAOE,QAAUD,EAAOC,OACvCC,MAAMC,QAAQJ,EAAO,IAC5BA,EAAOE,SAAWD,EAAOC,QAAUF,EAAOK,MAAM,SAAUC,EAASC,GACjE,MAAOD,GAAQJ,SAAWD,EAAOM,GAAOL,SACxCD,EAAOO,OAAOC,EAAK,GAAK,EAAIT,EAAOE,SAAWD,EAAOC,QAAS,GAE3D,EAGT,QAASO,GAAKC,EAAOC,GACnB,MAAOD,GAAQC,EAGjB,QAASC,GAAiBC,EAAOC,EAAOC,GACtC,MAAO,UAAUC,GACf,GAAIC,GAAUH,EAAMI,kBAAoBJ,EAAMK,gBAAkBL,EAAMM,kBACtE,IAAIH,EAAS,CACX,GAAII,GAAeJ,EAAQK,KAAKR,EAAOE,EACvCH,GAAME,GAAQM,EAAcL,GAC5BH,EAAMU,WAKZ,QAASC,GAAYlC,EAAMuB,GAKzB,IAJA,GAAIY,GAAUzC,EAAQ0C,KAAKb,EAAMY,SAC/BrC,EAAQC,WAAWC,GAAMmC,SACzBxC,EAAM0C,SAASC,OAAOH,SAEjBA,EAAQvB,OAASW,EAAMgB,KAAK3B,QACjCuB,EAAQK,KAAKjB,EAAMkB,YAErB,OAAON,GAAQO,IAAIC,GAGrB,QAASA,GAAeC,GACtB,MAAsB,gBAAXA,IAAkC,OAAXA,EAAwBA,EACpC,gBAAXA,IAAqC,MAAdA,EAAO,GAAmBH,EAAUI,EAASD,EAAOE,OAAO,KACtFC,IAGT,QAASA,KACP,GAAIH,IAAUI,EAAa,EAAG,KAAMA,EAAa,EAAG,KAAMA,EAAa,EAAG,KAC1E,OAAOP,GAAUG,GAGnB,QAASH,GAAWG,GAClB,OACEK,UAAWC,EAAKN,EAAQ,IACxBO,YAAaD,EAAKN,EAAQ,GAC1BQ,WAAYF,EAAKN,EAAQ,GACzBS,iBAAkB,OAClBC,mBAAoB,OACpBC,qBAAsBL,EAAKN,EAAQ,KAIvC,QAASI,GAAcQ,EAAKC,GAC1B,MAAOC,MAAKC,MAAMD,KAAKE,UAAYH,EAAMD,EAAM,IAAMA,EAGvD,QAASN,GAAMN,EAAQiB,GACrB,MAAIC,GAEK,OAASlB,EAAOmB,KAAK,KAAO,IAE5B,QAAUnB,EAAOoB,OAAOH,GAAOE,KAAK,KAAO,IAKtD,QAASlB,GAAUoB,GACjB,GAAIC,GAASC,SAASF,EAAK,IACzBG,EAAKF,GAAU,GAAM,IACrBG,EAAKH,GAAU,EAAK,IACpBI,EAAa,IAATJ,CAEN,QAAQE,EAAGC,EAAGC,GAGhB,QAASC,GAAaC,EAAQjC,EAAMkC,EAAQtC,GAC1C,OACEqC,OAAQA,EACRE,SAAUnC,EAAKG,IAAI,SAAUiC,EAAMC,GACjC,MAAOlF,GAAQQ,UAAWiC,EAAQyC,IAChCC,MAAOJ,EAAOG,GACdrC,KAAMoC,OAMd,QAASG,GAASN,EAAQjC,EAAMJ,GAC9B,MAAOqC,GAAO9B,IAAI,SAAUmC,EAAOD,GACjC,MAAOlF,GAAQQ,UAAWiC,EAAQyC,IAChCC,MAAOA,EACPE,MAAOxC,EAAKqC,GACZI,MAAO7C,EAAQyC,GAAGzB,YAClB8B,UAAW9C,EAAQyC,GAAGrB,yBAK5B,QAAS2B,GAAWC,EAAM3D,GACxB,GAAI4D,GAAUD,EAAKE,SACfC,EAAaF,EAAQG,KAAK,gBAC1BC,EAAS,iBAAmBhE,EAAMiE,iBAAmB,iBACrDH,GAAW1E,OAAQ0E,EAAWI,YAAYF,GACzCJ,EAAQO,OAAOH,GAGtB,QAASI,GAAapE,EAAOqE,EAAQtE,EAAO4D,GACtCtE,MAAMC,QAAQS,EAAMgB,KAAK,IAC3Bf,EAAMkD,SAASoB,QAAQ,SAAUC,EAASnB,IACvCmB,EAAQC,QAAUD,EAAQE,MAAMH,QAAQ,SAAUI,EAAUC,GAC3DD,EAASnB,MAAQc,EAAOjB,GAAGuB,OAI/B3E,EAAM4E,SAASN,QAAQ,SAAUO,EAASzB,GACxCyB,EAAQtB,MAAQc,EAAOjB,KAG3BpD,EAAM8E,SACN/E,EAAMgF,MAAM,SAAU/E,GAClBD,EAAMiE,QAA2B,UAAjBjE,EAAMiE,QAAoBN,EAAUC,EAAM3D,GAGhE,QAASgF,GAASzB,GAChB,OAASA,GACNlE,MAAMC,QAAQiE,KAAYA,EAAMnE,QACf,gBAAVmE,KAAwB0B,OAAOC,KAAK3B,GAAOnE,OAGvD,QAAS+F,GAAc3G,EAAMuB,GAC3B,GAAI1B,GAAUH,EAAQQ,UAAWP,EAAM0C,SAASC,OAAQxC,EAAQC,WAAWC,GAAOuB,EAAM1B,QACxF,OAAOA,GAAQ+G,WAvOjB,MAAO,UAAgB5G,GACrB,OACE6G,SAAU,KACVtF,OACEgB,KAAM,IACNiC,OAAQ,IACR3E,QAAS,IACT4E,OAAQ,IACRtC,QAAS,KACTM,UAAW,KACXqE,UAAW,IACXtB,OAAQ,IACRuB,MAAO,IACPC,MAAO,KAETC,KAAM,SAAU1F,EAAO4D,GAuCrB,QAAS+B,GAAYxG,EAAQC,GAC3B,IAAI6F,EAAQ9F,KACRhB,EAAQyH,OAAOzG,EAAQC,GAA3B,CACA,GAAImG,GAAY9G,GAAQuB,EAAMuF,SACxBA,KAIFtF,GAAOA,EAAM4F,UAEjBC,EAAYP,KAGd,QAASO,GAAarH,GACpB,GAAI2G,EAAa3G,EAAMuB,IAAmC,IAAzB4D,EAAK,GAAGmC,cAAiD,IAA3BC,EAAUD,aACvE,MAAO9G,GAAS,WACd6G,EAAYrH,IACX,GAEL,IAAMuB,EAAMgB,MAAUhB,EAAMgB,KAAK3B,OAAjC,CACAW,EAAMkB,UAAuC,kBAApBlB,GAAMkB,UAA2BlB,EAAMkB,UAAYM,EAC5ExB,EAAMY,QAAUD,EAAWlC,EAAMuB,EACjC,IAAIiG,GAAMrC,EAAK,GAAIsC,EAAMD,EAAIE,WAAW,MACpCnF,EAAO1B,MAAMC,QAAQS,EAAMgB,KAAK,IAClCgC,EAAYhD,EAAMiD,OAAQjD,EAAMgB,KAAMhB,EAAMkD,WAAclD,EAAMY,SAChE2C,EAAQvD,EAAMiD,OAAQjD,EAAMgB,KAAMhB,EAAMY,SACtCtC,EAAUH,EAAQQ,UAAWJ,EAAQC,WAAWC,GAAOuB,EAAM1B,QACjE2B,GAAQ,GAAI1B,GAAQH,MAAM8H,GAAKzH,GAAMuC,EAAM1C,GAC3C0B,EAAMgF,MAAM,SAAU/E,IAErB,QAAS,SAASsE,QAAQ,SAAUrE,GAC/BF,EAAME,KACR+F,EAAe,UAAX/F,EAAqB,UAAY,eAAiBH,EAAgBC,EAAOC,EAAOC,MAEpFF,EAAMiE,QAA2B,UAAjBjE,EAAMiE,QAAoBN,EAAUC,EAAM3D,IAxEhE,GAAIA,GAAO+F,EAAYI,SAASC,cAAc,MAC9CL,GAAUM,UAAY,kBACtB1C,EAAKO,YAAY6B,GACjBA,EAAUO,YAAY3C,EAAK,IAEvBrB,GAAeiE,OAAOC,mBAAmBC,YAAY9C,EAAK,IAI9D5D,EAAM2G,OAAO,OAAQ,SAAUxH,EAAQC,GACrC,GAAMD,GAAYA,EAAOE,UAAWC,MAAMC,QAAQJ,EAAO,KAASA,EAAO,GAAGE,QAA5E,CACA,GAAIkG,GAAY9G,GAAQuB,EAAMuF,SAC9B,IAAMA,EAAN,CAEA,GAAItF,EAAO,CACT,GAAIf,EAAeC,EAAQC,GAAS,MAAOiF,GAAYpE,EAAOd,EAAQa,EAAO4D,EAC7E3D,GAAM4F,UAGRC,EAAYP,OACX,GAEHvF,EAAM2G,OAAO,SAAUhB,GAAY,GACnC3F,EAAM2G,OAAO,SAAUhB,GAAY,GACnC3F,EAAM2G,OAAO,UAAWhB,GAAY,GACpC3F,EAAM2G,OAAO,UAAWhB,GAAY,GAEpC3F,EAAM2G,OAAO,YAAa,SAAUxH,EAAQC,GACtC6F,EAAQ9F,IACRhB,EAAQyH,OAAOzG,EAAQC,KACvBa,GAAOA,EAAM4F,UACjBC,EAAY3G,MAGda,EAAM4G,IAAI,WAAY,WAChB3G,GAAOA,EAAM4F,eAxH3BzH,EAAM0C,SAASC,OAAOsE,YAAa,EACnCjH,EAAM0C,SAASC,OAAO8F,qBAAuB,6DAE7CzI,EAAM0C,SAASC,OAAOH,SACpB,UACA,UACA,UACA,UACA,UACA,UACA,UAGF,IAAI2B,GAAqD,gBAA9BiE,QAAOC,oBACF,OAA9BD,OAAOC,oBAC0C,kBAA1CD,QAAOC,mBAAmBC,WAE/BnE,KAAenE,EAAM0C,SAASC,OAAO+F,WAAY,GAErD3I,EAAQF,OAAO,eACZ8I,SAAS,UAAW1I,GACpBR,QAAQ,kBAAmB,UAAW,WAAYmB,IAClDgI,UAAU,aAAc,iBAAkB,SAAUhI,GAAkB,MAAO,IAAIA,MACjFgI,UAAU,aAAc,iBAAkB,SAAUhI,GAAkB,MAAO,IAAIA,GAAe,WAChGgI,UAAU,YAAa,iBAAkB,SAAUhI,GAAkB,MAAO,IAAIA,GAAe,UAC/FgI,UAAU,cAAe,iBAAkB,SAAUhI,GAAkB,MAAO,IAAIA,GAAe,YACjGgI,UAAU,iBAAkB,iBAAkB,SAAUhI,GAAkB,MAAO,IAAIA,GAAe,eACpGgI,UAAU,YAAa,iBAAkB,SAAUhI,GAAkB,MAAO,IAAIA,GAAe,UAC/FgI,UAAU,kBAAmB,iBAAkB,SAAUhI,GAAkB,MAAO,IAAIA,GAAe", "file": "angular-chart.min.js", "sourcesContent": ["(function (factory) {\n  'use strict';\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(['angular', 'chart.js'], factory);\n  } else if (typeof exports === 'object') {\n    // Node/CommonJS\n    module.exports = factory(require('angular'), require('chart.js'));\n  } else {\n    // Browser globals\n    factory(angular, Chart);\n  }\n}(function (angular, Chart) {\n  'use strict';\n\n  Chart.defaults.global.responsive = true;\n  Chart.defaults.global.multiTooltipTemplate = '<%if (datasetLabel){%><%=datasetLabel%>: <%}%><%= value %>';\n\n  Chart.defaults.global.colours = [\n    '#97BBCD', // blue\n    '#DCDCDC', // light grey\n    '#F7464A', // red\n    '#46BFBD', // green\n    '#FDB45C', // yellow\n    '#949FB1', // grey\n    '#4D5360'  // dark grey\n  ];\n\n  var usingExcanvas = typeof window.G_vmlCanvasManager === 'object' &&\n    window.G_vmlCanvasManager !== null &&\n    typeof window.G_vmlCanvasManager.initElement === 'function';\n\n  if (usingExcanvas) Chart.defaults.global.animation = false;\n\n  angular.module('chart.js', [])\n    .provider('ChartJs', ChartJsProvider)\n    .factory('ChartJsFactory', ['ChartJs', '$timeout', ChartJsFactory])\n    .directive('chartBase', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory(); }])\n    .directive('chartLine', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Line'); }])\n    .directive('chartBar', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Bar'); }])\n    .directive('chartRadar', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Radar'); }])\n    .directive('chartDoughnut', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Doughnut'); }])\n    .directive('chartPie', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('Pie'); }])\n    .directive('chartPolarArea', ['ChartJsFactory', function (ChartJsFactory) { return new ChartJsFactory('PolarArea'); }]);\n\n  /**\n   * Wrapper for chart.js\n   * Allows configuring chart js using the provider\n   *\n   * angular.module('myModule', ['chart.js']).config(function(ChartJsProvider) {\n   *   ChartJsProvider.setOptions({ responsive: true });\n   *   ChartJsProvider.setOptions('Line', { responsive: false });\n   * })))\n   */\n  function ChartJsProvider () {\n    var options = {};\n    var ChartJs = {\n      Chart: Chart,\n      getOptions: function (type) {\n        var typeOptions = type && options[type] || {};\n        return angular.extend({}, options, typeOptions);\n      }\n    };\n\n    /**\n     * Allow to set global options during configuration\n     */\n    this.setOptions = function (type, customOptions) {\n      // If no type was specified set option for the global object\n      if (! customOptions) {\n        customOptions = type;\n        options = angular.extend(options, customOptions);\n        return;\n      }\n      // Set options for the specific chart\n      options[type] = angular.extend(options[type] || {}, customOptions);\n    };\n\n    this.$get = function () {\n      return ChartJs;\n    };\n  }\n\n  function ChartJsFactory (ChartJs, $timeout) {\n    return function chart (type) {\n      return {\n        restrict: 'CA',\n        scope: {\n          data: '=',\n          labels: '=',\n          options: '=',\n          series: '=',\n          colours: '=?',\n          getColour: '=?',\n          chartType: '=',\n          legend: '@',\n          click: '=',\n          hover: '='\n        },\n        link: function (scope, elem/*, attrs */) {\n          var chart, container = document.createElement('div');\n          container.className = 'chart-container';\n          elem.replaceWith(container);\n          container.appendChild(elem[0]);\n\n          if (usingExcanvas) window.G_vmlCanvasManager.initElement(elem[0]);\n\n          // Order of setting \"watch\" matter\n\n          scope.$watch('data', function (newVal, oldVal) {\n            if (! newVal || ! newVal.length || (Array.isArray(newVal[0]) && ! newVal[0].length)) return;\n            var chartType = type || scope.chartType;\n            if (! chartType) return;\n\n            if (chart) {\n              if (canUpdateChart(newVal, oldVal)) return updateChart(chart, newVal, scope, elem);\n              chart.destroy();\n            }\n\n            createChart(chartType);\n          }, true);\n\n          scope.$watch('series', resetChart, true);\n          scope.$watch('labels', resetChart, true);\n          scope.$watch('options', resetChart, true);\n          scope.$watch('colours', resetChart, true);\n\n          scope.$watch('chartType', function (newVal, oldVal) {\n            if (isEmpty(newVal)) return;\n            if (angular.equals(newVal, oldVal)) return;\n            if (chart) chart.destroy();\n            createChart(newVal);\n          });\n\n          scope.$on('$destroy', function () {\n            if (chart) chart.destroy();\n          });\n\n          function resetChart (newVal, oldVal) {\n            if (isEmpty(newVal)) return;\n            if (angular.equals(newVal, oldVal)) return;\n            var chartType = type || scope.chartType;\n            if (! chartType) return;\n\n            // chart.update() doesn't work for series and labels\n            // so we have to re-create the chart entirely\n            if (chart) chart.destroy();\n\n            createChart(chartType);\n          }\n\n          function createChart (type) {\n            if (isResponsive(type, scope) && elem[0].clientHeight === 0 && container.clientHeight === 0) {\n              return $timeout(function () {\n                createChart(type);\n              }, 50);\n            }\n            if (! scope.data || ! scope.data.length) return;\n            scope.getColour = typeof scope.getColour === 'function' ? scope.getColour : getRandomColour;\n            scope.colours = getColours(type, scope);\n            var cvs = elem[0], ctx = cvs.getContext('2d');\n            var data = Array.isArray(scope.data[0]) ?\n              getDataSets(scope.labels, scope.data, scope.series || [], scope.colours) :\n              getData(scope.labels, scope.data, scope.colours);\n            var options = angular.extend({}, ChartJs.getOptions(type), scope.options);\n            chart = new ChartJs.Chart(ctx)[type](data, options);\n            scope.$emit('create', chart);\n\n            ['hover', 'click'].forEach(function (action) {\n              if (scope[action])\n                cvs[action === 'click' ? 'onclick' : 'onmousemove'] = getEventHandler(scope, chart, action);\n            });\n            if (scope.legend && scope.legend !== 'false') setLegend(elem, chart);\n          }\n        }\n      };\n    };\n\n    function canUpdateChart (newVal, oldVal) {\n      if (newVal && oldVal && newVal.length && oldVal.length) {\n        return Array.isArray(newVal[0]) ?\n        newVal.length === oldVal.length && newVal.every(function (element, index) {\n          return element.length === oldVal[index].length; }) :\n          oldVal.reduce(sum, 0) > 0 ? newVal.length === oldVal.length : false;\n      }\n      return false;\n    }\n\n    function sum (carry, val) {\n      return carry + val;\n    }\n\n    function getEventHandler (scope, chart, action) {\n      return function (evt) {\n        var atEvent = chart.getPointsAtEvent || chart.getBarsAtEvent || chart.getSegmentsAtEvent;\n        if (atEvent) {\n          var activePoints = atEvent.call(chart, evt);\n          scope[action](activePoints, evt);\n          scope.$apply();\n        }\n      };\n    }\n\n    function getColours (type, scope) {\n      var colours = angular.copy(scope.colours ||\n        ChartJs.getOptions(type).colours ||\n        Chart.defaults.global.colours\n      );\n      while (colours.length < scope.data.length) {\n        colours.push(scope.getColour());\n      }\n      return colours.map(convertColour);\n    }\n\n    function convertColour (colour) {\n      if (typeof colour === 'object' && colour !== null) return colour;\n      if (typeof colour === 'string' && colour[0] === '#') return getColour(hexToRgb(colour.substr(1)));\n      return getRandomColour();\n    }\n\n    function getRandomColour () {\n      var colour = [getRandomInt(0, 255), getRandomInt(0, 255), getRandomInt(0, 255)];\n      return getColour(colour);\n    }\n\n    function getColour (colour) {\n      return {\n        fillColor: rgba(colour, 0.2),\n        strokeColor: rgba(colour, 1),\n        pointColor: rgba(colour, 1),\n        pointStrokeColor: '#fff',\n        pointHighlightFill: '#fff',\n        pointHighlightStroke: rgba(colour, 0.8)\n      };\n    }\n\n    function getRandomInt (min, max) {\n      return Math.floor(Math.random() * (max - min + 1)) + min;\n    }\n\n    function rgba (colour, alpha) {\n      if (usingExcanvas) {\n        // rgba not supported by IE8\n        return 'rgb(' + colour.join(',') + ')';\n      } else {\n        return 'rgba(' + colour.concat(alpha).join(',') + ')';\n      }\n    }\n\n    // Credit: http://stackoverflow.com/a/11508164/1190235\n    function hexToRgb (hex) {\n      var bigint = parseInt(hex, 16),\n        r = (bigint >> 16) & 255,\n        g = (bigint >> 8) & 255,\n        b = bigint & 255;\n\n      return [r, g, b];\n    }\n\n    function getDataSets (labels, data, series, colours) {\n      return {\n        labels: labels,\n        datasets: data.map(function (item, i) {\n          return angular.extend({}, colours[i], {\n            label: series[i],\n            data: item\n          });\n        })\n      };\n    }\n\n    function getData (labels, data, colours) {\n      return labels.map(function (label, i) {\n        return angular.extend({}, colours[i], {\n          label: label,\n          value: data[i],\n          color: colours[i].strokeColor,\n          highlight: colours[i].pointHighlightStroke\n        });\n      });\n    }\n\n    function setLegend (elem, chart) {\n      var $parent = elem.parent(),\n          $oldLegend = $parent.find('chart-legend'),\n          legend = '<chart-legend>' + chart.generateLegend() + '</chart-legend>';\n      if ($oldLegend.length) $oldLegend.replaceWith(legend);\n      else $parent.append(legend);\n    }\n\n    function updateChart (chart, values, scope, elem) {\n      if (Array.isArray(scope.data[0])) {\n        chart.datasets.forEach(function (dataset, i) {\n          (dataset.points || dataset.bars).forEach(function (dataItem, j) {\n            dataItem.value = values[i][j];\n          });\n        });\n      } else {\n        chart.segments.forEach(function (segment, i) {\n          segment.value = values[i];\n        });\n      }\n      chart.update();\n      scope.$emit('update', chart);\n      if (scope.legend && scope.legend !== 'false') setLegend(elem, chart);\n    }\n\n    function isEmpty (value) {\n      return ! value ||\n        (Array.isArray(value) && ! value.length) ||\n        (typeof value === 'object' && ! Object.keys(value).length);\n    }\n\n    function isResponsive (type, scope) {\n      var options = angular.extend({}, Chart.defaults.global, ChartJs.getOptions(type), scope.options);\n      return options.responsive;\n    }\n  }\n}));\n"], "sourceRoot": "/source/"}