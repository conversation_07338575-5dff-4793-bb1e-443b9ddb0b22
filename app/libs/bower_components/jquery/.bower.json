{"name": "j<PERSON>y", "version": "2.1.3", "main": "dist/jquery.js", "license": "MIT", "ignore": ["**/.*", "build", "speed", "test", "*.md", "AUTHORS.txt", "Gruntfile.js", "package.json"], "devDependencies": {"sizzle": "2.1.1-jquery.2.1.2", "requirejs": "2.1.10", "qunit": "1.14.0", "sinon": "1.8.1"}, "keywords": ["j<PERSON>y", "javascript", "library"], "homepage": "https://github.com/jquery/jquery", "_release": "2.1.3", "_resolution": {"type": "version", "tag": "2.1.3", "commit": "8f2a9d9272d6ed7f32d3a484740ab342c02541e0"}, "_source": "git://github.com/jquery/jquery.git", "_target": "~2.1.3", "_originalSource": "j<PERSON>y", "_direct": true}