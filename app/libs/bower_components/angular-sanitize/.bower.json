{"name": "angular-sanitize", "version": "1.3.14", "main": "./angular-sanitize.js", "ignore": [], "dependencies": {"angular": "1.3.14"}, "homepage": "https://github.com/angular/bower-angular-sanitize", "_release": "1.3.14", "_resolution": {"type": "version", "tag": "v1.3.14", "commit": "ae307cd94fc71360c376c666ebe964fb95918132"}, "_source": "git://github.com/angular/bower-angular-sanitize.git", "_target": "~1.3.14", "_originalSource": "angular-sanitize", "_direct": true}