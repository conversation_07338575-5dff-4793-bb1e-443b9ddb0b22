language: node_js
node_js:
  - "6"

addons:
  firefox: "47.0.2"

before_script:
  # credits for FF configuration: https://github.com/pouchdb/pouchdb/blob/master/.travis.yml
  # Our test-browser.js script uses FIREFOX_BIN to find the Firefox binary
  # See https://github.com/travis-ci/travis-ci/issues/4649
  - echo "using firefox $(firefox --version)"
  - export FIREFOX_BIN=$(which firefox)
  - npm install -g bower
  - bower install
  - export DISPLAY=:99.0
  - sh -e /etc/init.d/xvfb start
  - gulp serve &
  - sleep 3 # give webserver some time to start

script:
  - npm test

after_success:
  - npm run coveralls
  - npm run codacy

notifications:
  - email: false

sudo: false
