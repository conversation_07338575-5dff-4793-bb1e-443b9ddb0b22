{"name": "angular-ui-tree", "version": "2.22.6", "homepage": "https://github.com/angular-ui-tree/angular-ui-tree", "authors": ["<PERSON> <https://github.com/JimLiu>", "Voles <https://github.com/Voles>", "<PERSON> <https://github.com/StefanCodes>"], "description": "Tree directive for AngularJS", "main": ["dist/angular-ui-tree.js", "dist/angular-ui-tree.css"], "keywords": ["angular", "NestedSortable", "sortable", "nested", "drag", "drop", "tree", "node"], "dependencies": {"angular": "1.2.x || 1.3.x || 1.4.x || 1.5.x"}, "devDependencies": {"angular-bootstrap": "~0.13.4", "angular-mocks": "1.2.x || 1.3.x || 1.4.x || 1.5.x", "angular-route": "1.2.x || 1.3.x || 1.4.x || 1.5.x", "angularfire": "2.0.x", "bootstrap-css": "~3.2.0", "jasmine-jquery": "2.1.1", "jquery": "~2.1.1"}, "ignore": ["examples", "demo", "build", "guide", "node_modules", "source", "tasks", "tests", "gulpfile.js", "karma.conf.js", "package.json"], "resolutions": {"angular": "1.2.x || 1.3.x || 1.4.x || 1.5.x"}}