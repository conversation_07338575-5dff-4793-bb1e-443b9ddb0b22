// Import Bootstrap Variables and Mixins
@import "bootstrap/variables";
@import "bootstrap/mixins";

//Declare Other App Variables
$bootstrap-sass-asset-helper: false !default;

.bg-cover {
	background-image: url(../img/bg-cover.jpg);
	background-size: cover;
	background-repeat: no-repeat;
}

.header {
	background-color: #fff;
}

.form-signin {
	// max-width: 350px;
	padding: $grid-gutter-width $grid-gutter-width*2;
	margin: 0 auto;
	.form-signin-heading {
		margin-bottom: 10px;
	}
	.checkbox {
		margin-bottom: 10px;
		font-weight: normal;
	}
	.form-control {
		position: relative;
		font-size: 16px;
		height: auto;
		padding: 10px;
		//Instead of the line below you could use @includebox-sizing($bs)
		box-sizing: border-box;
		&:focus {
			z-index: 2;
		}
	}
	input[type="text"] {
		margin-bottom: -1px;
		//Instead of the line below you could use @includeborder-bottom-left-radius($radius)
		border-bottom-left-radius: 0;
		//Instead of the line below you could use @includeborder-bottom-right-radius($radius)
		border-bottom-right-radius: 0;
	}
	input[type="password"] {
		margin-bottom: 10px;
		//Instead of the line below you could use @includeborder-top-left-radius($radius)
		border-top-left-radius: 0;
		//Instead of the line below you could use @includeborder-top-right-radius($radius)
		border-top-right-radius: 0;
	}
}
.account-wall {
	margin-top: 40px;
	padding: 40px 0 20px;
	background-color: #EFEFEF;
	box-shadow: 0 2px 2px $black_30;
	border-radius: $border-radius-base;

	img {
		width: 60%;
		height: auto;
		padding: $grid-gutter-width;
	}
}
.login-title {
	color: $color_fuscous_gray_approx;
	font-size: 18px;
	font-weight: 400;
	display: block;
}
.need-help {
	margin-top: 10px;
}
.new-account {
	display: block;
	margin-top: 10px;
}

.well-cover {
	background-color: rgba(255,255,255,0.75);
	margin-top: $navbar-height + ($navbar-height/2-15);
	border-radius: 20px; 

	.list-group, .panel {
		box-shadow: 0 1px 20px rgba(0,0,0,0.3);
		-webkit-box-shadow: 0 1px 20px rgba(0,0,0,0.3);
	}
}