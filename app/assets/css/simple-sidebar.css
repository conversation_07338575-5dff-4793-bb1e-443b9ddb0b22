/*!
 * Start Bootstrap - Simple Sidebar HTML Template (http://startbootstrap.com)
 * Code licensed under the Apache License v2.0.
 * For details, see http://www.apache.org/licenses/LICENSE-2.0.
 */

/* Toggle Styles */
#wrapper {
    padding-left: 0;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

#wrapper.toggled {
    padding-left: 250px;
}

#sidebar-wrapper {
    z-index: 4;
    position: fixed;
    left: 250px;
    width: 0;
    height: 100%;
    margin-left: -250px;
    overflow-y: auto;
    background: #F8F8F8;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
	overflow-x: hidden;
	border-right: 1px solid #eee;
}

#wrapper.toggled #sidebar-wrapper {
    width: 250px;
}
#page-content-wrapper {
    width: 100%;
    position: absolute;
    padding: 15px;
	margin-top:40px;
}
#wrapper.toggled #navbar-toggle{
	left:250px;
	right:-250px;
}
#navbar-toggle,#footer-controls{
	z-index:4;
	-webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}
#wrapper.toggled #footer-controls{
	margin-left:250px;
}
#footer-controls{
	background: none;
	border: none;
	padding-bottom: 3rem;
	margin-left:0;
}
#wrapper.toggled #page-content-wrapper {
    position: absolute;
    margin-right: -250px;
}

/* Sidebar Styles */

.sidebar-nav {
    position: absolute;
    top: 24px;
    width: 250px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.sidebar-nav li {
    text-indent: 20px;
    line-height: 40px;
}

.sidebar-nav li a {
    display: block;
    text-decoration: none;
    color: #49494D;
	margin-left:15px;
}

.sidebar-nav li a:hover {
    text-decoration: none;
    color: #fff;
    background: rgba(255,255,255,0.2);
}

.sidebar-nav li a:active,
.sidebar-nav li a:focus {
    text-decoration: none;
}

.sidebar-nav > .sidebar-brand {
    height: 65px;
    font-size: 18px;
    line-height: 60px;
}

.sidebar-nav > .sidebar-brand a {
    color: #999999;
}

.sidebar-nav > .sidebar-brand a:hover {
    color: #fff;
    background: none;
}
.sidebar-nav li a>i{
	margin-left:-24px;
	margin-right: 24px;
	font-size: 1.666rem;
}
.sidebar-nav li:hover {
	background:#eee;
}
.sidebar-nav li:hover  a{
	color:#337ab7;
}
.navbar-toggle{
	float: left;
	margin-left: 15px;
}
.btn-fab{
    position:fixed;
	border-radius: 50%;
	width: 6rem;
	z-index: 5;
	position: fixed;
	height: 6rem;
	box-shadow: 0 2px 5px rgba(0,0,0,.5);
}
.btn-fab.top{ top:2rem;}
.btn-fab.bottom{bottom:2rem;}
.btn-fab.left{left:15px;}
.btn-fab.right{right:15px;}
@media (min-width: 0) {
	.navbar-toggle{
		  display: block; /* force showing the toggle */
	}
}
@media(min-width:768px) {
    #wrapper {
       padding-left: 64px;
    }
    #wrapper.toggled {
       padding-left: 250px;
    }
	#wrapper.toggled  #navbar-toggle,#navbar-toggle{
		left:0;
		right:0;
	 }
    #sidebar-wrapper {
        width: 64px;
    }

    #wrapper.toggled #sidebar-wrapper {
        width: 250px;
    }

    #page-content-wrapper {
        padding: 20px 66px;
        position: relative;
    }

    #wrapper.toggled #page-content-wrapper {
        position: relative;
        margin-right: 0;
    }
	#wrapper.toggled #footer-controls{
		margin-left: 200px;
	}
	#footer-controls{
		margin-left: 0;
	}
}