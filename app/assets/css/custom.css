.btn-fab:focus{
	outline: 0;
}
.search-box{
	position: absolute;
	top: 10px;
	z-index: 4;
	width: 100%;
	margin-left: -80px;
	background: rgba(0,0,0,.075);
	padding: 45px 80px;
	height:auto;
}
.login .search-box{
	left: 0px;
    background: none;
    top: 0;
    width: auto;
    margin: auto;
}
.custom-date input{
	border-radius:4px !important;
}
.list-group-search-results{
	height:470px;
	overflow-y:auto; 
	background: rgba(255, 255, 255, 0.95);
}
#search-results .list-group-item{
	background: rgba(255, 255, 255, 0.95);
	border:1px solid #eee;
}
#search-form{margin-bottom:1.33rem;}
.blur {
	filter: blur(3px);
	-webkit-filter: blur(3px);
	-moz-filter: blur(3px);
	-o-filter: blur(3px);
	-ms-filter: blur(3px);
}
.typeahead,
.tt-query,
.tt-hint {
  width: 100%;
  border: none;
  outline: none;
}
.typeahead-wrapper{padding-top: 4px !important;}
.twitter-typeahead{width:100% !important;}
.typeahead-wrapper.drop-up .tt-dropdown-menu{top:initial !important;bottom:100%;}
.typeahead {
  background-color: #fff;
  padding:6px 12px;
}


.tt-query {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
     -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.tt-hint {
  color: #999
}

.tt-dropdown-menu {
  width: 100%;
  padding: 6px 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0,0,0,.2);
     -moz-box-shadow: 0 5px 10px rgba(0,0,0,.2);
          box-shadow: 0 5px 10px rgba(0,0,0,.2);
}

.tt-suggestion {
  padding: 3px 20px;
  font-size: 18px;
  line-height: 24px;
}

.tt-suggestion.tt-cursor {
  color: #fff;
  background-color: #0097cf;
}

.tt-suggestion p {
  margin: 0;
}

.numeric{text-align:right !important;}
.table>thead>tr>th{text-align:center;}
.alerts-tray{ 
 display:none;
  position: absolute;
  top: 1%;
  z-index: 6;
  width: 90%;
  padding-left: 1%;
}
.alerts-tray.alerts-open{ display:block;}
div#progress-bar{
  height: 0rem;
  width: 0%;
  opacity:0;
  background-color: #4B93C8;
  position: relative;
  top: 50px;
  transition: all ease 1s;
 }
 .table-data{
	margin-top: 1rem;
	padding-bottom:6.66rem;
 }
 .inventory tr.suceess{ font-weight:bold;}
 
 input.ng-dirty.ng-invalid{border:1px solid red;}
 .modal-body section{padding-top:2rem; min-height:180px;}
 input[type="number"]{text-align:right;}
 
 h3.total{
	margin-top: 6px;
 }
 h3.total span{
  font-size: 15px;
  text-transform: uppercase;
 }
 .modal-header{
  background-color: #337AB7;
  color: #FFF;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
 }
 .btn-fab,.navbar-fixed-bottom{
	-webkit-transition: all .333s ease-in-out;
    -o-transition: all .333s ease-in-out;
    transition: all .333s ease-in-out
 }
 .toggled .btn-fab.right{
	right:-6.66rem;
}
.toggled .navbar-fixed-bottom{
	  bottom: -9rem;
}
span.module{
	margin-right:0.66rem;
	margin-top:0.66rem;
}
.page-content-blind{
	position: absolute;
	width: 0%;
	opacity:0;
	height: 100%;
	background-color: rgba(245, 243, 243, 0.35);
	top: 0;
	left: 0;
	z-index: 3;
	transition: all ease 0.33s;
	cursor: not-allowed;
}
.toggled .page-content-blind{
	width:100%;
	opacity:1;
}
nav.row{
	margin-top: -6.66rem;
}
.invalid-amount{
	color:red;
}

.alert-success {
    color: #ffffff;
    background-color: #52a032;
    border-color: #92a017;
}
.alert-danger{
  color: #ffffff;
    background-color: #c50e0e;
    border-color: #ad293e;
}