@font-face {
    font-family: 'PTSans-Bold';
    src: url('../fonts/ptsans/PT_Sans-Web-Bold-webfont.eot');
    src: url('../fonts/ptsans/PT_Sans-Web-Bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/ptsans/PT_Sans-Web-Bold-webfont.woff2') format('woff2'),
         url('../fonts/ptsans/PT_Sans-Web-Bold-webfont.woff') format('woff'),
         url('../fonts/ptsans/PT_Sans-Web-Bold-webfont.ttf') format('truetype'),
         url('../fonts/ptsans/PT_Sans-Web-Bold-webfont.svg#pt_sansbold') format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'PTSans-Regular';
    src: url('../fonts/ptsans/PT_Sans-Web-Regular-webfont.eot');
    src: url('../fonts/ptsans/PT_Sans-Web-Regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/ptsans/PT_Sans-Web-Regular-webfont.woff2') format('woff2'),
         url('../fonts/ptsans/PT_Sans-Web-Regular-webfont.woff') format('woff'),
         url('../fonts/ptsans/PT_Sans-Web-Regular-webfont.ttf') format('truetype'),
         url('../fonts/ptsans/PT_Sans-Web-Regular-webfont.svg#pt_sansregular') format('svg');
    font-weight: normal;
    font-style: normal;

}