<link rel="stylesheet" href="libs/bower_components/angular-ui-tree/dist/angular-ui-tree.min.css">
<div ng-controller="PurchaseOrderController" ng-init="initializeController()">
<div class="row">
	<div class="col-lg-12">
		<h1 class="pull-left">Purchase</h1>
		<h3 class="pull-right text-right total"><span>Total</span> <div>{{POAmount  | currency:""}}</div></h3>
		<div class="clearfix"></div>
		<div class="row">
			<div class="col-sm-12">
				<div class="input-group input-group-lg">
					 <div class="input-group-btn">
					 	<button type="button" class="btn btn-default" ng-click="toggleSort('sort')" ng-hide="sortMode" ng-disabled="editMode || deleteMode || !POItems.length"><span class="glyphicon glyphicon-sort" aria-hidden="true"></span></button>

						<button type="button" class="btn btn-default" ng-click="toggleSort('save')" ng-show="sortMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>

						<button type="button" class="btn btn-default" ng-click="toggleEdit('edit')" ng-hide="editMode" ng-disabled="sortMode || deleteMode || !POItems.length"><span class="glyphicon glyphicon-pencil" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleEdit('save')" ng-show="editMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleDelete('delete')" ng-hide="deleteMode" ng-disabled="sortMode || editMode || !POItems.length"><span class="glyphicon glyphicon-trash" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleDelete('save')" ng-show="deleteMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="resetCustomerObject()" ng-disabled="CustomerObject.id==undefined || POToken"><span class="glyphicon glyphicon-user" aria-hidden="true"></span></button>
					</div>
					<div class="typeahead-wrapper">
					 <input type="text" class="form-control input-lg" options="typeAheadOptions" datasets="typeAheadData" ng-readonly="CustomerObject.id!=undefined" ng-disabled="POToken"  ng-model="CustomerObjectTypeAhead" class="typeahead " sf-typeahead ng-blur="validateCustomer(CustomerObject)" placeholder="Customer Name" />
					 </div>
					 <div class="input-group-btn dropdown" ng-class="{open : openPODD}">
						 <button type="button" class="btn btn-default custom" ng-click="openPODD=!openPODD">
						 	PO No.<span ng-show="PONumber">: {{PONumber}}</span> 
						</button>
						 <ul class="dropdown-menu pull-right">
						  <li class="dropdown-controls"> 
						  	<input type="text" ng-model="PONo" class="form-control" placeholder="Enter PO No."/>
						  	<div class="clearfix"></div>
						  </li>
						  

						  <li class="dropdown-controls">
				  			<button class="btn btn-default"  ng-click="cancelPOEntry()">Cancel</button>
				  			<button class="btn btn-primary"  ng-click="confirmPOEntry()">Confirm</button>
						  		
						  	</li>
						</ul>
					</div>
					 <div class="input-group-btn">
					 <button type="button" class="btn btn-default" ng-click="resetPO()"  ng-disabled="sortMode || editMode||deleteMode||!POItems.length"><span class="glyphicon glyphicon-remove"></span></button>
					 </div>
				  </div>
			</div>
		</div>
		<div class="row table-data">
			<div class="col-sm-12">				
			   <table class="table table-hover table-bordered" 
			   ui-tree ng-show="sortMode">
			   	<thead>
					<tr>
						<th>&nbsp;</th>
						<th>Material</th>
						<th>Price</th>
						<th>Quantity</th>
						<th ng-if="0">Code</th>
						<th>Amount</th>
					</tr>
				</thead>
			   	<tbody ui-tree-nodes ng-model="POSortItems">
			   		<tr ui-tree-node ng-repeat="item in POSortItems"  >
						<td class="col-md-1 text-center" ui-tree-handle >
							<button class="btn btn-default" >
								<span class="glyphicon glyphicon-sort"></span>
							</button>

						</td>
						<td>
							{{item.particular}} {{item.part_no}}  
							{{item.thickness}}{{item.unit}}  &times;
							<span ng-show="item.category_id=='PLT8'">  {{item.width}}{{item.unit}} &times;
							</span>
							  {{item.length}}{{item.unit}} 
							
						</td>
						<td class="numeric">
							<span >{{item.price | currency:""}}</span>
							
						</td>
						<td class="numeric"> 
							<span>{{item.quantity}}</span>
							
						</td>
						<td class="numeric">
							<span >{{item.amount | currency:""}}</span>
							
						</td>
					</tr>
					</tbody>
			   </table>
			   <table class="table table-hover table-bordered" ng-hide="sortMode">
				<thead>
					<tr>
						<th ng-show="deleteMode" >&nbsp;</th>
						<th>Material</th>
						<th>Price</th>
						<th>Quantity</th>
						<th ng-if="0">Code</th>
						<th>Amount</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="item in POItems" ng-class="{danger:item.is_delete}" ng-show="!sortMode">
						<td ng-show="deleteMode" >
							<input type="checkbox" ng-model="item.is_delete" ng-checked="item.is_delete" /></td>
						<td>
							{{item.particular}} {{item.part_no}}  
							{{item.thickness}}{{item.unit}}  &times;
							<span ng-show="item.category_id=='PLT8'">  {{item.width}}{{item.unit}} &times;
							</span>
							  {{item.length}}{{item.unit}} 
							
						</td>
						<td class="numeric">
							<span ng-hide="editMode">{{item.price | currency:""}}</span>
							<input  ng-show="editMode" type="number" ng-model="item.price" class="input-sm" auto-select />
						</td>
						<td class="numeric"> 
							<span ng-hide="editMode">{{item.quantity}}</span>
							<input  ng-show="editMode" type="number" ng-model="item.quantity" class="input-sm" auto-select />
						</td>
						<td class="numeric">
							<span  ng-hide="editMode">{{item.amount | currency:""}}</span>
							<span  ng-show="editMode">{{(item.quantity * item.price)| currency:"" }}</span>
						</td>
					</tr>
				</tbody>
				<tfoot ng-if="!POItems.length"> 
					<tr>
						<td class="text-center" colspan="7">No items added yet.</td>
					</tr>
				</tfoot>
			   </table>
		</div>
	   </div>
	   <button type="button" class="btn btn-primary btn-md btn-fab top right" ng-click="openTransactionModal()" ng-disabled="transactionInProgress || (!POAmount || !CustomerObject) || (deleteMode||editMode)"><span class="glyphicon glyphicon-ok" aria-hidden="true"></span></button>
	   <form action="../api/reports/po" method="POST" target="_blank" ng-hide="1" id="POReports">
	   		<input type="text" name="token" ng-model="TrnxToken" />
	   		<input type="text" name="trnx_id" ng-model="TrnxID" />
	   </form>
	   <div ng-include="'views/shared/transactionModal.php'"></div>
	   <div ng-include="'views/shared/productTypeaheadv2.php'"></div>
	</div>
</div>
</div>
<style>
	.custom{
		-webkit-border-radius: 0px !important;
		-moz-border-radius: 0px !important;
		border-radius: 0px !important;
	}
	.input-group-btn.dropdown ul{
		margin-top:-5px;
	}
	.dropdown-controls{
    	padding: 8px;
    	padding-top: 4px;
	}
	.dropdown-controls>input.form-control{
		border-radius: 4px!important;
	}
</style>

          