<div ng-controller="JobOrderController" ng-init="initializeController()">
<div class="row">
	<div class="col-lg-12">
	   
		<h1 class="pull-left">Job Order</h1>
		<h3 class="pull-right text-right total"><span>Total</span> <div>{{JOTotalItems  | number}} item(s)</div></h3>
		<div class="clearfix"></div>
		<div class="row">
			<div class="col-sm-12">
				<div class="input-group input-group-lg">
					 <div class="input-group-btn">
						<button type="button" class="btn btn-default" ng-click="toggleEdit('edit')" ng-hide="editMode" ng-disabled="deleteMode || !JOItems.length"><span class="glyphicon glyphicon-pencil" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleEdit('save')" ng-show="editMode" ng-disabled="!JOForm.$valid"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleDelete('delete')" ng-hide="deleteMode" ng-disabled="editMode || !JOItems.length"><span class="glyphicon glyphicon-trash" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleDelete('save')" ng-show="deleteMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
						
					</div>
					<select ng-model="ActivePO" id="ActivePO" class="form-control">
						<option value="">Select PO to proccess</option>
						<option value="{{trnx.id}}" ng-repeat="trnx in Transactions">PO {{trnx.po_no}}   &mdash;  {{trnx.customer.name}}, {{trnx.items.length}} item(s)</option>
					</select>
					 <div class="input-group-btn ">
					 	<button type="button" class="btn btn-default custom" ng-class="{'btn-success':CutCalculated}" ng-disabled="!JOItems.length" ng-click="calculateCut()">
					 		Calculate
					 	</button>
					 </div>
					 <div class="input-group-btn">
					 <button type="button" class="btn btn-default" ng-click="resetJO()"  ng-disabled="editMode||deleteMode||!JOItems.length"><span class="glyphicon glyphicon-remove"></span></button>
					 </div>
				  </div>
			</div>
		</div>
		<div class="row table-data">
			<div class="col-sm-12">
				<form name="JOForm">
				   <table class="table table-hover table-bordered">
					<thead>
						<tr>
							<th rowspan="2" ng-show="deleteMode" >&nbsp;</th>
							<th rowspan="2" class="col-md-4">Material</th>
							<th colspan="4">Actual</th>
						</tr>
						<tr>
							<th>Width</th>
							<th>Length</th>
							<th>Code</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="item in JOItems" ng-class="{danger:item.is_delete}">
							<td ng-show="deleteMode" ><input type="checkbox" ng-model="item.is_delete" ng-checked="item.is_delete" /></td>
							<td  class="col-md-4">
								{{item.quantity}}  &times;
								{{item.particular}} {{item.part_no}}  

								{{item.thickness}}{{item.unit}} &times; 
								<span ng-show="item.category_id=='PLT8'">  {{item.width}}{{item.unit}} &times;</span>
								{{item.length}}{{item.unit}}
								
							</td>
							<td class="numeric" ng-class="{danger:JOForm['JOItem-W'+$index].$error.max || JOForm['JOItem-W'+$index].$error.min}"> 
								<span ng-show="item.category_id!=='PLT8'">
									N/A
								</span>
								<span ng-show="item.category_id=='PLT8'">
								<span ng-show="JOForm['JOItem-W'+$index].$error.max" class="danger">Excess</span>
								<span ng-show="JOForm['JOItem-W'+$index].$error.min" class="danger">Short</span>
								<span ng-hide="editMode">{{item.width_actual}}</span>
								<input  name="JOItem-W{{$index}}"  ng-show="editMode" type="number" ng-model="item.width_actual" ng-min="{{item.width}}" ng-max="{{item.width+item.allowance}}" class="input-sm" auto-select />
								</span>
							</td>

							<td class="numeric" ng-class="{danger:JOForm['JOItem-L'+$index].$error.max || JOForm['JOItem-L'+$index].$error.min }"> 
								<span ng-show="JOForm['JOItem-L'+$index].$error.max" class="danger">Excess</span>
								<span ng-show="JOForm['JOItem-L'+$index].$error.min" class="danger">Short</span>
								<span ng-hide="editMode">{{item.length_actual}}</span>
								<input  name="JOItem-L{{$index}}" ng-show="editMode" type="number" ng-model="item.length_actual" ng-min="{{item.length}}" ng-max="{{item.length+item.allowance}}" class="input-sm" auto-select />
							</td>
							
							
							<td class="numeric" ng-class="{'warning':!item.code_actual}"> 
								<span ng-hide="editMode">{{item.code_actual?item.code_actual:'--'}}</span>
								<input  ng-show="editMode" type="text" ng-maxlength="5" ng-model="item.code_actual" class="input-sm" auto-select />
								<select class="input-sm"  ng-show="editMode" ng-model="item.code_actual" ng-options="stock.id as stock.id for stock in item.stocks"></select>
							</td>
						</tr>
					</tbody>
					<tfoot ng-if="!JOItems.length"> 
						<tr>
							<td class="text-center" colspan="7">No items added yet.</td>
						</tr>
					</tfoot>
				   </table>
			   </form>
		</div>
	   </div>
	   <button type="button" class="btn btn-primary btn-md btn-fab top right" ng-click="openTransactionModal()" ng-disabled="transactionInProgress || (!JOTotalItems || !ActivePO || !CutCalculated) || (deleteMode||editMode)"><span class="glyphicon glyphicon-ok" aria-hidden="true"></span></button>
	   <div ng-include="'views/shared/transactionModal.php'"></div>
	   <div ng-include="'views/shared/cutPlannerModal.php'"></div>
	</div>
</div>
</div>
<style>
	.custom{
		-webkit-border-radius: 0px !important;
		-moz-border-radius: 0px !important;
		border-radius: 0px !important;
	}
	.input-group-btn.dropdown ul{
		margin-top:-5px;
	}
	.dropdown-controls{
    	padding: 8px;
    	padding-top: 4px;
	}
	.dropdown-controls>input.form-control{
		border-radius: 4px!important;
	}
</style>

          