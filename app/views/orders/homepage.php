<div ng-controller="OrdersController" ng-init="initializeController()">
	 <div class="row">
		<div class="col-lg-12">
		   
		<h1 class="pull-left">Orders</h1>
		<h3 class="pull-right text-right total"><span>Total</span> <div>{{OrdersAmount  | currency:""}}</div></h3>
		<div class="clearfix"></div>
		<div class="row">
			<div class="col-sm-12">
				<div class="input-group input-group-lg">
				 <div class="input-group-btn">
					<button type="button" class="btn btn-default" ng-click="toggleEdit('edit')" ng-hide="editMode" ng-disabled="deleteMode || !OrdersItems.length"><span class="glyphicon glyphicon-pencil" aria-hidden="true"></span></button>
					<button type="button" class="btn btn-default" ng-click="toggleEdit('save')" ng-show="editMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
					<button type="button" class="btn btn-default" ng-click="toggleDelete('delete')" ng-hide="deleteMode" ng-disabled="editMode || !OrdersItems.length"><span class="glyphicon glyphicon-trash" aria-hidden="true"></span></button>
					<button type="button" class="btn btn-default" ng-click="toggleDelete('save')" ng-show="deleteMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
					<button type="button" class="btn btn-default" ng-click="resetVendorObject()" ng-disabled="VendorObject.id==undefined"><span class="glyphicon glyphicon-user" aria-hidden="true"></span></button>
				</div>
				<div class=" typeahead-wrapper">
				 <input class="form-control input-lg" type="text" options="typeAheadOptions" datasets="typeAheadData" ng-readonly="VendorObject.id!=undefined" ng-model="VendorObjectTypeahead" ng-blur="validateVendor(VendorObject)" class="typeahead input-lg" sf-typeahead  placeholder="Supplier Name" />
				 </div>
				 <div class="input-group-btn dropdown" ng-class="{open : openSPODD}">
						 <button type="button" class="btn btn-default custom" ng-click="openSPODD=!openSPODD">
						 	PO No.<span ng-show="SPONumber">: {{SPONumber}}</span> 
						</button>
						 <ul class="dropdown-menu pull-right">
						  <li class="dropdown-controls"> 
						  	<input type="text" ng-model="SPONo" class="form-control" placeholder="Enter PO No."/>
						  	<div class="clearfix"></div>
						  </li>
						  

						  <li class="dropdown-controls">
				  			<button class="btn btn-default"  ng-click="cancelSPOEntry()">Cancel</button>
				  			<button class="btn btn-primary"  ng-click="confirmSPOEntry()">Confirm</button>
						  		
						  	</li>
						</ul>
					</div>
				  <div class="input-group-btn">
				 <button type="button" class="btn btn-default" ng-click="resetOrders()"  ng-disabled="editMode||deleteMode||!OrdersItems.length"><span class="glyphicon glyphicon-remove"></span></button>
				 </div>
			  </div>
			</div>
		</div>
		<div class="row table-data">
			<div class="col-sm-12">
			 <table class="table table-hover table-bordered">
				<thead>
					<tr>
						<th ng-show="deleteMode" >&nbsp;</th>
						<th class="col-md-4">Material</th>
						<th>Price</th>
						<th>Quantity</th>
						<th>Amount</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="item in OrdersItems" ng-class="{danger:item.is_delete}">
						<td ng-show="deleteMode" ><input type="checkbox" ng-model="item.is_delete" ng-checked="item.is_delete" /></td>
						<td>
							{{item.particular}} {{item.part_no}}  
							{{item.thickness}}{{item.unit}}  &times;
							<span ng-show="item.category_id=='PLT8'">  {{item.width}}{{item.unit}} &times;
							</span>
							  {{item.length}}{{item.unit}} 
							

						</td>
						<td class="numeric">
							<span ng-hide="editMode">{{item.capital | currency:""}}</span>
							<input  ng-show="editMode" type="number" auto-select ng-model="item.capital" class="input-sm" />
						</td>
						<td class="numeric"> 
							<span ng-hide="editMode">{{item.quantity}}</span>
							<input  ng-show="editMode" type="number" auto-select ng-model="item.quantity" class="input-sm" />
						</td>
						<td class="numeric">
							<span  ng-hide="editMode">{{item.amount | currency:""}}</span>
							<span  ng-show="editMode">{{(item.quantity * item.capital)| currency:"" }}</span>
						</td>
					</tr>
				</tbody>
				<tfoot ng-if="!OrdersItems.length"> 
					<tr>
						<td class="text-center" colspan="5">No items added yet.</td>
					</tr>
				</tfoot>
			   </table>
			</div>
		</div>
		<button type="button" class="btn btn-primary btn-md btn-fab top right" ng-click="openTransactionModal()" ng-disabled="transactionInProgress || (!OrdersAmount || !VendorObject) || (deleteMode||editMode)"><span class="glyphicon glyphicon-ok" aria-hidden="true"></span></button>

		 <form action="../api/reports/orders" method="POST" target="_blank" ng-hide="1" id="SPOReports">
	   		<input type="text" name="token" ng-model="TrnxToken" />
	   		<input type="text" name="trnx_id" ng-model="TrnxID" />
	   </form>
		<div ng-include="'views/shared/transactionModal.php'"></div>
	   <div ng-include="'views/shared/productTypeaheadv2.php'"></div>
	 </div>
</div>
<style>
	.custom{
		-webkit-border-radius: 0px !important;
		-moz-border-radius: 0px !important;
		border-radius: 0px !important;
	}
	.input-group-btn.dropdown ul{
		margin-top:-5px;
	}
	.dropdown-controls{
    	padding: 8px;
    	padding-top: 4px;
	}
	.dropdown-controls>input.form-control{
		border-radius: 4px!important;
	}
</style>