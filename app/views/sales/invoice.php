<div ng-controller="SalesInvoiceInvoiceController" ng-init="initializeController()">
<div class="row">
	<div class="col-lg-12">
	   
		<h1 class="pull-left">Sales Invoice</h1>
		<h3 class="pull-right text-right total"><span>Total</span> <div>{{SalesInvoiceAmount  | currency:""}}</div></h3>
		<div class="clearfix"></div>
		<div class="row">
			<div class="col-sm-12">
				<div class="input-group input-group-lg">
					 <div class="input-group-btn">
						<button type="button" class="btn btn-default" ng-click="toggleEdit('edit')" ng-hide="editMode" ng-disabled="deleteMode || !SalesInvoiceItems.length"><span class="glyphicon glyphicon-pencil" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleEdit('save')" ng-show="editMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleDelete('delete')" ng-hide="deleteMode" ng-disabled="editMode || !SalesInvoiceItems.length"><span class="glyphicon glyphicon-trash" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default" ng-click="toggleDelete('save')" ng-show="deleteMode"><span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span></button>
					</div>
					<select ng-model="ActivePO" id="ActivePO" class="form-control">
						<option value="">Select PO to proccess</option>
						<option value="{{trnx.id}}" ng-repeat="trnx in Transactions">PO {{trnx.po_no}}   &mdash;  {{trnx.customer.name}}, {{trnx.items.length}} item(s)</option>
					</select>
					 <div class="input-group-btn">
					 <button type="button" class="btn btn-default" ng-click="resetSalesInvoice()"  ng-disabled="editMode||deleteMode||!SalesInvoiceItems.length"><span class="glyphicon glyphicon-remove"></span></button>
					 </div>
				  </div>
			</div>
		</div>
		<div class="row table-data">
			<div class="col-sm-12">
			   <table class="table table-hover table-bordered">
				<thead>
					<tr>
						<th ng-show="deleteMode" >&nbsp;</th>
						<th class="col-md-4">Material</th>
						<th>Price</th>
						<th>Quantity</th>
						<th ng-if="0">Code</th>
						<th>Amount</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="item in SalesInvoiceItems" ng-class="{danger:item.is_delete}">
						<td ng-show="deleteMode" ><input type="checkbox" ng-model="item.is_delete" ng-checked="item.is_delete" /></td>
						<td class="col-md-4">
								{{item.description}}
						</td>
						<td class="numeric">
							<span ng-hide="editMode">
							{{item.price | currency:""}}
							</span>
							<input  ng-show="editMode" type="number" ng-model="item.price" class="input-sm" auto-select />
						</td>
						<td class="numeric"> 
							<span ng-hide="editMode">{{item.quantity}}</span>
							<input  ng-show="editMode" type="number" ng-model="item.quantity" class="input-sm" auto-select />
						</td>
						
						<td class="numeric">
							<span  ng-hide="editMode">{{item.amount | currency:""}}</span>
							<span  ng-show="editMode">{{(item.quantity * item.price)| currency:"" }}</span>
						</td>
					</tr>
				</tbody>
				<tfoot ng-if="!SalesInvoiceItems.length"> 
					<tr>
						<td class="text-center" colspan="7">No items added yet.</td>
					</tr>
				</tfoot>
			   </table>
		</div>
	   </div>
	   <button type="button" class="btn btn-primary btn-md btn-fab top right" ng-click="openTransactionModal()" ng-disabled="transactionInProgress || (!SalesInvoiceAmount || !CustomerObject) || (deleteMode||editMode)"><span class="glyphicon glyphicon-ok" aria-hidden="true"></span></button>
	   <form action="../api/reports/invoice" method="POST" target="_blank" ng-hide="1" id="InvoiceReports">
	   		<input type="text" name="token" ng-model="TrnxToken" />
	   		<input type="text" name="trnx_id" ng-model="TrnxID" />
	   		<input type="text" name="version" ng-value="SINewVersion?'2024-2':'old'" />
	   </form>
	    <form action="../api/reports/delivery" method="POST" target="_blank" ng-hide="1" id="DeliveryReports">
	   		<input type="text" name="token" ng-model="TrnxToken" />
	   		<input type="text" name="trnx_id" ng-model="TrnxID" />
	   </form>
	    <form action="../api/reports/collection" method="POST" target="_blank" ng-hide="1" id="CollectReports">
	   		<input type="text" name="token" ng-model="TrnxToken" />
	   		<input type="text" name="trnx_id" ng-model="TrnxID" />
	   </form>
	   <div ng-include="'views/shared/transactionModal.php'"></div>
	   <div ng-include="'views/shared/productTypeahead.php'"></div>
	</div>
</div>
</div>
          