<div ng-controller="SupplyMonitoringController" ng-init="initializeController()">
	<div class="row">
		<div class="col-lg-12">
			<h1>Monitoring</h1>
			<div class="row">
					<div class="col-sm-12">
					<div class="input-group input-group-lg">
						 <div class="input-group-btn" ng-class="{open : FilterEnabled}">
							<button type="button" class="btn btn-default" ng-disabled="SearchEnabled || FilterEnabled" ng-click="toggleFilter()">
								<span class="glyphicon glyphicon-filter" aria-hidden="true"></span>
							</button>
							<ul class="dropdown-menu">
							  <li class="dropdown-header"><i class="glyphicon glyphicon-transfer"></i> Transaction Types</li>
							  <li ng-class="{active :!FilterKeys.type || FilterKeys.type=='ALL'}" ng-hide="1"><a ng-click="setFilterKey('type','ALL')">All</a></li>
							  <li ng-repeat="type in TransactionTypes" ng-class="{active :type.id == FilterKeys.type}"><a ng-click="setFilterKey('type',type.id)">{{type.value}}</a></li>
							  <li class="dropdown-header"><i class="glyphicon glyphicon-calendar"></i> Coverage</li>
							  <li ng-class="{active :!FilterKeys.coverage || FilterKeys.coverage=='ALL'}"><a ng-click="setFilterKey('coverage','ALL')">All</a></li>
							  <li ng-repeat="coverage in Coverages" ng-class="{active :coverage.id == FilterKeys.coverage}"><a ng-click="setFilterKey('coverage',coverage.id)">{{coverage.value}}</a></li>
							  <li ng-show="FilterKeys.coverage==='custom'" class="dropdown-header custom-date">
									<div class="form-group">
										<label for="">From</label> 
										<input type="date" class="form-control"   ng-model="CustomDate.from" />
									</div>
									<div class="clearfix"></div>
							  </li>
							  <li ng-show="FilterKeys.coverage==='custom'" class="dropdown-header custom-date">
									<div class="form-group">
									<label for="">To</label>
									<input type="date" class="form-control" ng-model="CustomDate.to" min="{{CustomDate.from}}" />
									</div>
									<div class="clearfix"></div>
							  </li>
								<li role="separator" class="divider"></li>
					
							   <li class="dropdown-header">
								<button class="btn btn-default" ng-click="cancelFilter()">Cancel</button>
								<button class="btn btn-primary" ng-click="confirmFilter()">Confirm</button>
							   </li>
							</ul>
							<button type="button" class="btn btn-default" ng-disabled="SearchEnabled || FilterEnabled" ng-click="searchFor(transactionSearchBox)"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></button>
						</div>
						 <input type="text" class="form-control" placeholder="Search Transaction by Customer , PO or SI" ng-disabled="SearchEnabled || FilterEnabled"   ng-model="transactionSearchBox"/>
						  <div class="input-group-btn">
							<button type="button" class="btn btn-default"  ng-disabled="!SearchEnabled" ng-click="resetSearch()"><span class="glyphicon glyphicon-remove" aria-hidden="true"></span></button>
							<button type="button" class="btn btn-default" ng-click="exportData(SearchKeyword,FilterKeys)"><span class="glyphicon glyphicon-save-file" aria-hidden="true"></span></button>
						</div>
					  </div>
					 </div>
				</div>
			<div class="row table-data">
				<div class="col-md-6">
						<canvas id="bar" class="chart chart-bar" data="Graph.data" labels="Graph.labels" click="onClick"></canvas>
				</div>
				<div class="col-md-6">
					<table class="table table-hover table-bordered inventory">
						<thead>
							<tr>
								<th class="col-md-1">Code</th>
								<th >
									
										Item 
									
								</th>
								<th>
									Area (mm<sup>2</sup>)
								</th>
							</tr>
							<tr>
								<td colspan="2">
									<button class="btn btn-default" ng-show="GraphData.length" ng-click="toggleGraph()">Show {{GraphShow=='ALL'?'Top 10':'All'}}</button>
								</td>
								<td class="text-right">
									<h4>
									{{GraphTotalArea | number}}
									</h4>
								</td>
							</tr>
							
						</thead>
						<tbody>
							<tr ng-repeat="(key, value) in Graph.labels">
								<td>{{value}}</td>
								<td>{{Graph.items[key]}}</td>
								<td class="text-right">{{Graph.data[0][key]|number}}</td>
							</tr>
						</tbody>
					</table>
				</div>
				
			</div>
			<div class="row table-data" ng-show="FilterKeys.type=='orders'">
					<div class="col-sm-12">
					   <table class="table table-hover table-bordered inventory">
						<thead>
							<tr>
								<th>Date</th>
								<th>Vendor</th>
								<th>PO No.</th>
								<th>SI No.</th>
								<th>Material</th>
								<th>Qty O</th>
								<th>Qty R</th>
								<th>Price</th>
								<th>Amount</th>
								<th>Grand Total</th>
								<th>Status</th>
							</tr>
						</thead>
						<tbody>
							<tr ng-show="( FilterKeys.type|| FilterKeys.coverage ) && !LoadingProducts && !FilterEnabled"  class="text-center"> 
								<td colspan="12">
									<span ng-if="!FilterKeys.type || FilterKeys.type=='ALL'">All items</span>
									<span  ng-repeat="type in TransactionTypes">
										<span ng-if="FilterKeys.type == type.id">All {{type.value}}</span>
									</span>
									<span ng-if="FilterKeys.coverage != 'ALL' && FilterKeys.coverage"> covered </span>
									<span ng-if="FilterKeys.coverage =='today'">today</span>
									<span ng-if="FilterKeys.coverage =='7D'">last 7 days</span>
									<span ng-if="FilterKeys.coverage =='30D'">last 30 days</span>
									<span ng-if="FilterKeys.coverage =='MON'">this month</span>
									.
									 Click <i class="glyphicon glyphicon-filter"></i> to modify.
								</td>
							</tr>
							<tr ng-show="Transactions.length && transactionSearchBox && !LoadingTransactions && !SearchEnabled"  class="text-center"> 
								<td colspan="12">Click the <span class="glyphicon glyphicon-search" aria-hidden="true"></span> to look further.</td>
							</tr>
							<tr ng-show="Transactions.length && transactionSearchBox && SearchEnabled"  class="text-center"> 
								<td colspan="12">Search result(s) for <b><i>{{transactionSearchBox}}</i></b>. Click the <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> to cancel.</td>
							</tr>
							<tr ng-show="!Transactions.length && transactionSearchBox && SearchEnabled && !LoadingTransactions"  class="text-center"> 
								<td colspan="12">No search result(s) for <b><i>{{transactionSearchBox}}</i></b>. Click the <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> to cancel.</td>
							</tr>
							<tr ng-show="LoadingTransactions"  class="text-center"> 
								<td colspan="12">Loading..</td>
							</tr>
							<tr ng-class="{'active':FetchTransactionId == transaction.id, 'success':NewTransaction  == transaction.id, 'cancelled' : transaction.status=='cancelled' }"style="opacity:{{LoadingTransactions||FetchingTransaction || FetchTransactionId == transaction.id ?0.5:1}}" ng-repeat-start="transaction in Transactions | filter:transactionSearchFilter" ng-click="openTransactionModal(transaction)">
								<td rowspan="{{transaction.TransactionDetail.length}}">
									{{transaction.timestamp | date: "mediumDate"}}
								</td>
								<td rowspan="{{transaction.TransactionDetail.length}}">
										{{transaction.entity.name}}
								</td>
								<td rowspan="{{transaction.TransactionDetail.length}}">
									{{transaction.Order.spo_no}}
								</td>
								<td rowspan="{{transaction.TransactionDetail.length}}">
									{{transaction.Order.Delivery.si_no}}
								</td>
								<td>
									{{transaction.TransactionDetail[0].Product.display_title}}
									&times 
									{{transaction.Order.OrderDetail[0].width}}
									{{transaction.TransactionDetail[0].Product.unit}}
									<span ng-if="transaction.TransactionDetail[0].Product.category_id=='PLT8'">
									&times
									{{transaction.Order.OrderDetail[0].length}}
									 {{transaction.TransactionDetail[0].Product.unit}}
									</span>

								</td>
								<td class="numeric">{{transaction.TransactionDetail[0].quantity}}</td>
								<td class="numeric">{{transaction.TransactionDetail[0].quantity}}</td>
								<td class="numeric">{{transaction.TransactionDetail[0].price| currency:""}}</td>
								<td class="numeric">{{transaction.TransactionDetail[0].amount| currency:""}}</td>
								<td class="numeric" rowspan="{{transaction.TransactionDetail.length}}">
									{{transaction.amount| currency:""}}
								</td>

								
								
								<td rowspan="{{transaction.TransactionDetail.length}}">
									{{transaction.status}}
								</td>
							</tr>
							<tr  ng-class="{'active':FetchTransactionId == transaction.id, 'success':NewTransaction  == transaction.id, 'cancelled' : transaction.status=='cancelled' }" ng-repeat-end ng-repeat="(i,item) in transaction.TransactionDetail.slice(1)">
								<td>
									<span ng-show="item.Discount">
										Discount:{{item.Discount.percent}}%
									</span>
									<span ng-show="item.Product">
									{{item.Product.display_title}}

									&times 
									{{transaction.Order.OrderDetail[i+1].width}}
									{{item.Product.unit}}
									<span ng-if="item.Product.category_id=='PLT8'">
									&times
									{{transaction.Order.OrderDetail[i+1].length}}
									 {{item.Product.unit}}
									</span>

									</span>
								</td>
								<td class="numeric">{{item.quantity}}</td>
								<td class="numeric">{{item.quantity}}</td>
								<td class="numeric">{{item.price | currency:""}}</td>
								<td class="numeric">
									<span ng-show="item.Discount">
										{{item.Discount.amount | currency:""}}
									</span>
									<span ng-show="item.Product">
										{{item.amount | currency:""}}
									</span>
								</td>
							</tr>
							<tr ng-repeat="fillers in Fillers track by $index">
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
							</tr>
						</tbody>
					   </table>
				   </div>
			   </div>

			   <div class="row table-data" ng-show="FilterKeys.type=='invoice'">
					<div class="col-sm-12">
					   <table class="table table-hover table-bordered inventory">
						<thead>
							<tr>
								<th>Date</th>
								<th>Customer</th>
								<th>Invoice No.</th>
								<th>DR No.</th>
								<th>Cash</th>
								<th>Check</th>
								<th>Terms</th>
								<th>CR</th>
							</tr>
						</thead>
						<tbody>
							<tr ng-show="( FilterKeys.type|| FilterKeys.coverage ) && !LoadingProducts && !FilterEnabled"  class="text-center"> 
								<td colspan="8">
									<span ng-if="!FilterKeys.type || FilterKeys.type=='ALL'">All items</span>
									<span  ng-repeat="type in TransactionTypes">
										<span ng-if="FilterKeys.type == type.id">All {{type.value}}</span>
									</span>
									<span ng-if="FilterKeys.coverage != 'ALL' && FilterKeys.coverage"> covered </span>
									<span ng-if="FilterKeys.coverage =='today'">today</span>
									<span ng-if="FilterKeys.coverage =='7D'">last 7 days</span>
									<span ng-if="FilterKeys.coverage =='30D'">last 30 days</span>
									<span ng-if="FilterKeys.coverage =='MON'">this month</span>
									.
									 Click <i class="glyphicon glyphicon-filter"></i> to modify.
								</td>
							</tr>
							<tr ng-show="Transactions.length && transactionSearchBox && !LoadingTransactions && !SearchEnabled"  class="text-center"> 
								<td colspan="8">Click the <span class="glyphicon glyphicon-search" aria-hidden="true"></span> to look further.</td>
							</tr>
							<tr ng-show="Transactions.length && transactionSearchBox && SearchEnabled"  class="text-center"> 
								<td colspan="8">Search result(s) for <b><i>{{transactionSearchBox}}</i></b>. Click the <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> to cancel.</td>
							</tr>
							<tr ng-show="!Transactions.length && transactionSearchBox && SearchEnabled && !LoadingTransactions"  class="text-center"> 
								<td colspan="8">No search result(s) for <b><i>{{transactionSearchBox}}</i></b>. Click the <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> to cancel.</td>
							</tr>
							<tr ng-show="LoadingTransactions"  class="text-center"> 
								<td colspan="12">Loading..</td>
							</tr>
							<tr ng-class="{'active':FetchTransactionId == transaction.id, 'success':NewTransaction  == transaction.id, 'cancelled' : transaction.status=='cancelled' }"style="opacity:{{LoadingTransactions||FetchingTransaction || FetchTransactionId == transaction.id ?0.5:1}}" ng-repeat="transaction in Transactions | filter:transactionSearchFilter" ng-click="openTransactionModal(transaction)">
								<td>
									{{transaction.timestamp | date: "mediumDate"}}
								</td>
								<td>
									{{transaction.entity.name }}
								</td>
								<td>
									{{transaction.Invoice.si_no}}
								</td>
								<td>
									{{transaction.Invoice.dr_no}}
								</td>
								<td class="numeric">
									{{transaction.cash | currency:""}}
								</td>
								<td class="numeric">
									{{transaction.check | currency:""}}
								</td>
								<td class="numeric">
									{{transaction.terms | currency:""}}
								</td>
								<td class="numeric">
									{{transaction.cr}}
								</td>
								
							</tr>
							
							<tr ng-repeat="fillers in Fillers track by $index">
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								
							</tr>
						</tbody>
					   </table>
				   </div>
			   </div>
			   <nav class="row">
					<div class="col-sm-4">
						<div class="input-group">
							 <span class="input-group-addon">Go to page</span>
							 <input type="text" class="form-control" placeholder="Page number"  ng-disabled="Pages.length==0" type="number" ng-model="GoToPage" />
							 <div class="input-group-btn">
								<button class="btn btn-default" ng-disabled="LoadingTransactions"  ng-disabled="Pages.length==0" ng-click="movePage(GoToPage)">Go</button>
							</div>
						</div>
					</div>
					<div class="col-sm-8 text-right" ng-hide="Pages.length==0">
						<div class="input-group">
							 <div class="input-group-btn">
								<button class="btn btn-default" ng-disabled="LoadingTransactions || CurrentPage==1" ng-click="movePage(CurrentPage-1,SearchKeyword,FilterKeys)">&laquo;</button>
								<button class="btn btn-default" ng-disabled="LoadingTransactions"  ng-repeat="page in ActivePages track by $index" ng-class="{'btn-primary':page===CurrentPage}"  ng-click="movePage(page,SearchKeyword,FilterKeys)">{{page}}</button>
								<button class="btn btn-default" ng-disabled="LoadingTransactions || CurrentPage==LastPage" ng-click="movePage(CurrentPage+1,SearchKeyword,FilterKeys)">&raquo;</button>
							</div>
						</div>
					</div>
			   </nav>
		</div>
		<form action="../api/reports/monitoring" method="POST" target="_blank" id="POMonitoring" ng-hide="1">
			<input type="text" ng-model="SearchKeyword" name="keyword"   ng-disabled="!SearchKeyword"/>
			<input type="text" value="entity_name,po_no,si_no" name="fields"  ng-disabled="!SearchKeyword"/>
			<input type="text" ng-model="ExportFilter" name="filter" />
			<input type="text" ng-model="ExportImage" name="graph" />
			<input type="text" ng-model="ExportTable" name="table" />
		</form>
		<form action="../api/reports/monthly_sales" method="GET" target="_blank" id="SalesMonitoring" ng-hide="1">
			<input type="text" ng-model="ExportFilter" name="filter" />
		</form>
		<div ng-include="'views/shared/transactionModal.php'"></div>
	</div>
</div>