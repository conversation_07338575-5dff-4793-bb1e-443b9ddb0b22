<div class="row" ng-controller="SOAController" ng-init="initializeController()">
	<div class="col-lg-12">
		<h1>Statment of Account</h1>
		<div class="row">
				<div class="col-sm-12">
				<div class="input-group input-group-lg">
					 <div class="input-group-btn" ng-class="{open : FilterEnabled}">
						<button type="button" class="btn btn-default" ng-disabled="SearchEnabled || FilterEnabled" ng-click="toggleFilter()">
							<span class="glyphicon glyphicon-filter" aria-hidden="true"></span>
						</button>
						<ul class="dropdown-menu">
						  <li class="dropdown-header"><i class="glyphicon glyphicon-user"></i> Account Type</li>
						  <li ng-repeat="entity in Entities" ng-class="{active :entity.id == FilterKeys.entity}"><a ng-click="setFilterKey('entity',entity.id)">{{entity.value}}</a></li>
						    <li role="separator" class="divider"></li>
						   <li class="dropdown-header">
							<button class="btn btn-default" ng-click="cancelFilter()">Cancel</button>
							<button class="btn btn-primary" ng-click="confirmFilter()">Confirm</button>
						   </li>
						</ul>
						<button type="button" class="btn btn-default" ng-disabled="SearchEnabled || FilterEnabled" ng-click="searchFor(accountSearchBox)"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></button>
					</div>
					 <input type="text" class="form-control" placeholder="Search {{FilterKeys.entity}} account" ng-disabled="SearchEnabled || FilterEnabled"   ng-model="accountSearchBox"/>
					  <div class="input-group-btn">
						<button type="button" class="btn btn-default hide" ng-click="exportData(SearchKeyword,FilterKeys)"><span class="glyphicon glyphicon-save-file" aria-hidden="true"></span></button>
						<button type="button" class="btn btn-default"  ng-disabled="!SearchEnabled" ng-click="resetSearch()"><span class="glyphicon glyphicon-remove" aria-hidden="true"></span></button>
					</div>
				  </div>
				 </div>
			</div>
			<div class="row table-data">
				<div class="col-sm-12">
				   <table class="table table-hover table-bordered inventory">
					<thead>
						<tr>
							<th>SOA No.</th>
							<th>{{FilterKeys.entity}}</th>
							<th>Due Amount</th>
							<th>Due Date</th>
							<th>Status</th>
						</tr>
					</thead>
					<tbody>
						
						<tr ng-show="Accounts.length && accountSearchBox && !LoadingAccounts && !SearchEnabled"  class="text-center"> 
							<td colspan="6">Click the <span class="glyphicon glyphicon-search" aria-hidden="true"></span> to look further.</td>
						</tr>
						<tr ng-show="Accounts.length && accountSearchBox && SearchEnabled"  class="text-center"> 
							<td colspan="6">Search result(s) for <b><i>{{accountSearchBox}}</i></b>. Click the <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> to cancel.</td>
						</tr>
						<tr ng-show="!Accounts.length && accountSearchBox && SearchEnabled && !LoadingAccounts"  class="text-center"> 
							<td colspan="6">No search result(s) for <b><i>{{accountSearchBox}}</i></b>. Click the <span class="glyphicon glyphicon-remove" aria-hidden="true"></span> to cancel.</td>
						</tr>
						<tr ng-show="LoadingAccounts"  class="text-center"> 
							<td colspan="6">Loading..</td>
						</tr>
						<tr ng-class="{'active':FetchAccountId == account.id, 'success':NewAccount  == account.id }"style="opacity:{{LoadingAccounts||FetchingAccount || FetchAccountId == account.id ?0.5:1}}" ng-repeat="account in Accounts | filter:accountSearchFilter" ng-click="editAccount(account.id)">
							<td>{{account.id}} <span class="label label-danger" ng-if="account.status=='close'">CLOSED</span></td>
							<td>{{account.customer.name}}</td>
							<td class="numeric">{{account.due_amount | currency:""}}</td>
							<td>{{account.due_date}}</td>
							<td>{{account.status}}</td>
						</tr>
						<tr ng-repeat="fillers in Fillers track by $index">
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
						</tr>
					</tbody>
				   </table>
			   </div>
		   </div>
			<button type="button" class="btn btn-primary btn-md btn-fab top right" ng-click="openSOAModal()" ng-disabled="accountInProgress"><span class="glyphicon glyphicon-plus" aria-hidden="true"></span></button>
		<div ng-include="'views/shared/soaModal.php'"></div>
	</div>
</div>