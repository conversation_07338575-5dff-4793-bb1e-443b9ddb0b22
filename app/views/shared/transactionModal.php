
<?php date_default_timezone_set('Asia/Manila');
	echo "<script type='text/ng-template' id='createTransactionModalContent.html'>";
?>


    <div ng-controller="transactionModalController" ng-init="initializeController()">
	
	  <div class="modal-header">
		<h4 class="modal-title">Transaction Information</h4>		
	  </div>
	  <div class="modal-body">
		<form action="" name="TransactionAddForm" class="form-horizontal">
			<div class="row">
				<input type="hidden" ng-model="id"/>
				<div class="col-md-7 col-xs-7">
					<div class="form-group" ng-if="ReadOnly">
						<label class="control-label col-sm-4 ">Type</label><div class="col-sm-8"><p class="form-control-static">{{header.type}} <span ng-if="header.status=='cancelled'" class="label label-danger">CANCELLED</span></p></div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-4 ">Date</label>
						<div class="col-sm-8">
							<p ng-if="!BackLogEnable" class="form-control-static">{{header.display_date | date: 'mediumDate'}}</p>
							<input  ng-if="BackLogEnable" type="date" class="form-control" ng-required="BackLogEnable" name="backLogDate" ng-model="backLogDate" ng-change="updateBackLog(backLogDate)" max="<?php echo date('Y-m-d',time());?>" />
						</div>
					</div>
					<div class="form-group">
						<label class="control-label col-sm-4 " ng-show="header.entity_type==='customer'">Customer</label>
						<label class="control-label col-sm-4 " ng-show="header.entity_type==='supplier'">Supplier</label>
						<div class="col-sm-8">
							<p class="form-control-static" ng-if="!header.entity.name">{{header.entity}}</p>
							<p class="form-control-static" ng-if="header.entity.name">{{header.entity.name}} <span ng-if="header.entity.status=='close'" class="label label-danger">ACCOUNT CLOSED</span></p>
						</div>	
					</div>
					<div ng-show="header.status=='invoiced' || header.status=='served'">
						<div class="form-group">
							<label class="control-label col-sm-4 ">SI/C No.</label>
							<div class="col-sm-8" ng-show="editInvObj">
								<input type="text" class="form-control" ng-model="invoiceObj.si_no">
							</div>
							<div class="col-sm-8" ng-show="!editInvObj">
								<span class="form-control-static">{{invoiceObj.si_no}}</span> &nbsp;
								<input type="checkbox" name="SINewVersion"  ng-model="SINewVersion"/> Use new version
								&nbsp;
								<a class="btn btn-default btn-sm" ng-click="printReceipt('si',invoiceObj.id)" ng-disabled="disableReprint">REPRINT</a> 
							</div>		
						</div>
						<div class="form-group">
							<label class="control-label col-sm-4 ">DR No.</label>
							<div class="col-sm-8" ng-show="editInvObj">
								<input type="text" class="form-control" ng-model="invoiceObj.dr_no">
							</div>
							<div class="col-sm-8" ng-show="!editInvObj">
								<span class="form-control-static">{{invoiceObj.dr_no}}</span>
								&nbsp;
								<a class="btn btn-default btn-sm" ng-click="printReceipt('dr',invoiceObj.id)" ng-show="invoiceObj.dr_no" ng-disabled="disableReprint">REPRINT</a>
								
								
							</div>		
						</div>

					</div>
					<div ng-show="header.type=='orders'">
						<div class="form-group">
							<label class="control-label col-sm-4 ">PO No.</label>
							<div class="col-sm-8" ng-show="!header.id">
								<input type="text" class="form-control" ng-model="header.spo_no">
							</div>
							<div class="col-sm-8" ng-show="header.id">
								<span class="form-control-static">{{orderObj.spo_no}}</span> 
								
								&nbsp;
								<a class="btn btn-default btn-sm" ng-click="printReceipt('spo',orderObj.id)" ng-disabled="disableReprint">REPRINT</a> 
							</div>		
						</div>

						<div class="form-group">
							<label class="control-label col-sm-4 ">Attention</label>
							<div class="col-sm-8" ng-show="!header.id">
								<input type="text" class="form-control" ng-model="header.attention">
							</div>
							<div class="col-sm-8" ng-show="header.id">
								<span class="form-control-static">{{orderObj.attention}}</span> 
							</div>		
						</div>
						<div class="form-group">
							<label class="control-label col-sm-4 ">Terms</label>
							<div class="col-sm-8" ng-show="!header.id">
								<input type="text" class="form-control" ng-model="header.terms">
							</div>
							<div class="col-sm-8" ng-show="header.id">
								<span class="form-control-static">{{orderObj.terms}}</span> 
							</div>		
						</div>
					</div>
					
				</div>
				<div class="col-md-5 col-xs-5 text-right">					
					<label class="total-label">Total</label>
					<div class="total-amount" ng-if="header.type=='jo'">{{TotalCharges}} item(s)</div>
					<div class="total-amount" ng-if="header.type!=='jo'">{{TotalCharges | currency:""}}</div>
					<div ng-hide="ReadOnly|| !payCash" className="change-amount">Change <strong>P{{CashChange | currency:""}}</strong></div>
					<div class="total-adjustment">
						<div class="input-group">
							<div class="input-group-btn">
								<span class="btn btn-xs btn-success" ng-if="Commission && !header.tax_type=='VAT'"> P{{Commission | currency:""}}</span>
								<span class="btn btn-xs btn-warning" ng-if="Tax"> 
									<span ng-if="header.tax_type=='VAT'">VAT </span>
								P{{Tax | currency:""}}</span>
								<span class="btn btn-xs btn-danger" ng-if="Discount"> P{{Discount | currency:""}}</span>
								<span class="btn btn-xs btn-default" ng-if="Commission||Tax||Discount">
									{{header.tax_type=='VAT'?'Net of VAT':'Adjustments'}} 
									<strong>P{{ (TotalCharges+Commission) | currency:""}}</strong></span>
									<br/>
									<div class="btn btn-xs btn-danger" ng-if="WithHoldingTax"> 
								<span >With Holding Tax </span>
								P{{WithHoldingTax | currency:""}}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="payment-information" ng-show="!OptionalPayment">
					<ul class="nav nav-tabs">
					  <li role="presentation" ng-class="{active:ActiveTransactionTab=='adjustment'}"><a  ng-click="ActiveTransactionTab='adjustment'" >Adjustment</a></li>
					  <li role="presentation" ng-class="{active:ActiveTransactionTab=='payment'}"><a  ng-click="ActiveTransactionTab='payment'" >Payment</a></li>
					   <li role="presentation" ng-class="{active:ActiveTransactionTab=='receipt'}"><a  ng-click="ActiveTransactionTab='receipt'" ng-show="!OptionalReceipt" >Receipts</a></li>
					</ul>
				<section ng-show="ActiveTransactionTab=='adjustment'">
					<div class="row-fluid">
						<div class="col-md-6 col-xs-6">
							<div class="form-group"><label class="control-label col-sm-6 ">Gross Comm</label><div class="col-sm-6"><input ng-model="GrossCommission" class="form-control numeric" ng-keyup="updateTax()" ng-change="updateCharges()" type="number" auto-select placeholder="Amount added"/></div></div>
							<div class="form-group"><label class="control-label col-sm-6 ">Value Added Tax (VAT)</label><div class="col-sm-6"><input ng-model="Tax" class="form-control numeric" ng-keyup="updateCommission()" ng-change="updateCharges()"  type="number" auto-select placeholder="Amount taxed"/></div></div>
							<div class="form-group"><label class="control-label col-sm-6 ">With Hold Tax (1% NetVAT)</label><div class="col-sm-6"><input ng-model="WithHoldingTax" class="form-control numeric" ng-keyup="updateCommission()" ng-change="updateCharges()"  type="number" auto-select placeholder="Amount withheld"/></div></div>
							<div class="form-group"  ng-show="!header.tax_type"><label class="control-label col-sm-6 ">Net Comm</label><div class="col-sm-6"><input ng-model="Commission" class="form-control numeric" ng-change="updateCharges()" type="number" auto-select placeholder="Amount added"/></div></div>
						</div>
						<div class="col-md-6 col-xs-6">
							<div class="form-group">
								<label class="control-label col-sm-6 ">Discount</label>
								<div class="col-sm-6">
									<div class="input-group">
										
									<input ng-model="DiscountPercent" class="form-control numeric" ng-blur="checkDiscount(true)"  type="number" auto-select placeholder="Amount discounted"/>
									<span class="input-group-addon">%</span>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-sm-6 ">&nbsp;</label>
								<div class="col-sm-6">
									<div class="input-group">
										<span class="input-group-addon">₱</span>
										<input ng-model="Discount" class="form-control numeric" ng-blur="checkDiscount(false)"   type="number" auto-select placeholder="Amount discounted"/>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-sm-6 ">Interest</label>
								<div class="col-sm-6">
									<input ng-model="Interest" class="form-control numeric" ng-change="updateCharges()"  type="number" auto-select placeholder="Amount interested"/>
								</div>
							</div>
						</div>
						
					</div>
				</section>
				<section ng-show="ActiveTransactionTab=='payment'">
						<div class="row">
							<div class="col-md-12 col-xs-12">
								<div class="form-group">
									<label class="col-sm-2 control-label">Types</label>
									<div class="col-sm-10">
										<label class="checkbox-inline">
										  <input type="checkbox" value="cash" ng-model="payCash" /> Cash
										</label>
										<label class="checkbox-inline">
										  <input type="checkbox" value="card" ng-model="payCheque"/> Check
										</label>
										<label class="checkbox-inline">
										  <input type="checkbox" value="card" ng-model="payCard"/> Card
										</label>
										<label class="checkbox-inline">
										  <input type="checkbox" value="charge" ng-model="payCharge" ng-disabled="header.entity.status=='close'" /> Charge
										</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row-fluid" ng-show="payCheque">
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 "><i class="glyphicon glyphicon-saved pull-left"></i> <div class="pull-right">Check</div></label><div class="col-sm-8"><input  ng-required="payCheque"  ng-model="ChequeReceived" class="form-control numeric" type="number" auto-select   ng-change="updatePayments('cheque')"  placeholder="Amount received"/></div></div></div>
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 ">Details</label><div class="col-sm-8"><input type="text" ng-model="ChequeDetails" ng-required="payCheque" name="ChequeDetail" class="form-control numeric" placeholder="Check details"/></div></div></div>
						</div>
						<div class="row-fluid" ng-show="payCard">
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 "><i class="glyphicon glyphicon-credit-card pull-left"></i> <div class="pull-right">Card</div> </label><div class="col-sm-8"><input  ng-required="payCard"  ng-model="CardReceived" class="form-control numeric" type="number" auto-select ng-change="updatePayments('card')" placeholder="Amount received"/></div></div></div>
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 ">Details</label><div class="col-sm-8"><input type="text" ng-model="CardDetails"  ng-required="payCard" name="CardDetail"  class="form-control numeric" placeholder="Card details"/></div></div></div>
						</div>
						<div class="row-fluid" ng-show="payCharge">
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 "><i class="glyphicon glyphicon-user pull-left"></i> <div class="pull-right">Charge</div> </label><div class="col-sm-8"><input  ng-required="payCharge"  ng-model="ChargeReceived" class="form-control numeric" type="number" auto-select ng-change="updatePayments('charge')" placeholder="Amount received"/></div></div></div>
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 ">Terms</label>
							<div class="col-sm-8">
								<div class="input-group">
									<input type="number" ng-model="ChargeDetails"  ng-required="payCharge" name="ChargeDetail"  class="form-control numeric" placeholder="Enter terms"/>
									<span class="input-group-addon">days</span>
								</div>
							</div>
						</div></div>
						</div>
						<div class="row-fluid" ng-show="payCash">
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 "><i class="glyphicon glyphicon-usd pull-left"></i>  <div class="pull-right">Cash</div></label><div class="col-sm-8"><input ng-model="CashReceived" class="form-control numeric" type="number" auto-select  ng-required="payCash"  ng-change="updatePayments('cash')"  placeholder="Amount received"/></div></div></div>
							<div class="col-md-6 col-xs-6"><div class="form-group"><label class="control-label col-sm-4 ">Details</label><div class="col-sm-8"><input type="text" ng-model="CashDetails"  ng-required="payCash" name="ChargeDetail"  class="form-control numeric" placeholder="Cash details"/></div></div></div>
						</div>
						<div class="row-fluid" ng-if="header.entity.status=='close'">
							<div class="col-md-12"> 
								<div class="alert alert-warning">
									<strong>Note:</strong> This account has been closed, charge is not possible. Please refer to the higher management for more information.
								</div>
							</div>
						</div>
						<div class="clearfix"></div>
				</section>
				<section ng-show="ActiveTransactionTab=='receipt'" >
					<div class="row-fluid">
						<div class="col-md-8 col-xs-8">
							<div class="form-group">
								<label class="control-label col-sm-6 ">Total Items</label>
								<div class="col-sm-6">
									<input ng-model="TotalItems" class="form-control" type="text" disabled />
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-sm-6 ">SI/C No.</label>
								<div class="col-sm-6">
									<input ng-model="SINo" class="form-control" type="text" auto-select placeholder="Sales Invoice {{TotalItems>MaxIPR?'(Start)':''}}" ng-required="PaymentValid"/>
									<input ng-model="SINoEnd" ng-show="TotalItems>MaxIPR" class="form-control" type="text" auto-select placeholder="Sales Invoice (End)" ng-required="PaymentValid"/>
									<input type="checkbox" name="SINewVersion"  ng-model="SINewVersion"/> Use new version
								</div>
							</div>
							<div class="form-group"  ng-show="header.tax_type=='ZRO' || true">
								<label class="control-label col-sm-6 ">DR No.</label>
								<div class="col-sm-6">
									<input ng-model="DRNo" class="form-control" type="text" auto-select placeholder="Delivery Receipt {{TotalItems>MaxIPR?'(Start)':''}}" ng-required="PaymentValid && header.tax_type=='ZRO'"/>
									<input ng-model="DRNoEnd" ng-show="TotalItems>MaxIPR" class="form-control" type="text" auto-select placeholder="Delivery Receipt (End)" ng-required="PaymentValid && header.tax_type=='ZRO'"/>
								</div>
							</div>
							
							<div class="form-group" ng-show="header.type!='deliveries'">
								<label class="control-label col-sm-6">CR No.</label>
								<div class="col-sm-6">
									<input ng-model="CRNo" class="form-control" type="text" auto-select placeholder="Collection Receipt" />
								</div>
							</div>
						</div>
					</div>
					<div class="clearfix"></div>
				</section>
			</div>
		</form>
		 <form action="../api/reports/invoice" method="POST" target="_blank" ng-hide="1" id="InvoiceReprint">
					   		<input type="text" name="ref_no" ng-model="invoiceObj.id" />
					   		<input type="text" name="version" ng-value="SINewVersion?'2024-2':'old'" />
					   </form>
					    <form action="../api/reports/delivery" method="POST" target="_blank" ng-hide="1" id="DeliveryReprint">
					   		<input type="text" name="ref_no" ng-model="invoiceObj.id" />
					   </form>
		 <form action="../api/reports/orders" method="POST" target="_blank" ng-hide="1" id="OrderReprint">
					   		<input type="text" name="ref_no" ng-model="orderObj.id" />
					   </form>
	  </div>
	  <div class="modal-footer">
		<div ng-if="!ReadOnly">
			<button type="button" class="btn btn-danger pull-left" ng-if="!BackLogEnable" ng-click="enableBackLog(true)">Change Date</button>
			<button type="button" class="btn btn-success pull-left" ng-if="BackLogEnable" ng-disabled="!TransactionAddForm.backLogDate.$valid" ng-click="enableBackLog(false)">Confirm Date</button>
			<button type="button" class="btn btn-default" ng-click="cancel()"  ng-disabled="BackLogEnable" >
				Cancel
			</button>
			<button type="button" class="btn btn-primary" ng-click="confirm(header.token)"
			ng-disabled="((!PaymentValid  || !ReceiptValid)&&!OptionalPayment && !OptionalReceipt) || BackLogEnable">Confirm</button>
		</div>
		<div ng-if="ReadOnly">
			<button type="button" class="btn btn-danger pull-left" ng-click="cancel(id)" ng-disabled="header.status=='cancelled'|| !cancellable" ng-hide="editInvObj" >
				{{ header.type=='po' && header.status=='created'?'Delete':'Cancel'}}

			</button>
			<a class="btn btn-warning pull-left" ng-click="editRefNos(invoiceObj)"  ng-show="invoiceObj && !editInvObj" >
						Edit SI/DR
					</a>
				<a class="btn btn-warning pull-left" ng-click="editPO(header.id)" ng-show="header.status=='created' && header.type=='po'" >
						Edit PO
					</a>
			<a class="btn btn-warning pull-left" ng-click="confirmRefNos(invoiceObj)"  ng-hide="!editInvObj">
						Confirm Changes
					</a>
			<button type="button" class="btn btn-primary pull-left" ng-click="serve(id)" ng-disabled="header.status=='served'|| !servable"  ng-hide="editInvObj || header.type=='orders'">
				{{header.status=='served'?'SERVED':'Tag as SERVED'}}
			</button>
			<button type="button" class="btn btn-default" ng-hide="editInvObj" ng-click="close()">Close</button>

			<button type="button" class="btn btn-default" ng-hide="!editInvObj" ng-click="cancelRefNos()">Cancel</button>
		</div>
	  </div>
	</div>
</script>
<script type="text/ng-template" id="viewTransactionModalContent.html">

    <div ng-controller="transactionModalController" ng-init="initializeController()">
	
	  <div class="modal-header">
		<h4 class="modal-title">Transaction Information</h4>		
	  </div>
	  <div class="modal-body">
		<form action="" class="form-horizontal">
			<div class="row">
				<div class="col-md-12 col-xs-12">
					<div class="form-group">
						<div class="row"><div class="form-group"><label class="control-label col-sm-3 ">Transaction Id</label><div class="col-sm-8"><input type="text" ng-model="TransactionId" class="form-control numeric" placeholder="Enter transaction id"/></div></div></div>
					</div>
				</div>
			</div>
		</form>
	  </div>
	  <div class="modal-footer">
		<button type="button" class="btn btn-default" ng-click="cancel()">Cancel</button>
		<button type="button" class="btn btn-primary" ng-click="confirm(TransactionId)" ng-disabled="false">Confirm</button>
	  </div>
	</div>
</script>
<style type="text/css">
.total-label{
	display: block;
	margin-bottom: 0;
	text-transform: uppercase;
	  font-size: 24px;
	  font-weight: 100;
}
.total-amount{
	font-size:3.3rem;
}
</style>