<footer class="navbar navbar-default navbar-fixed-bottom" id="productTypeaheadv2" ng-controller="productTypeaheadController" ng-init="initializeController('v2')">
	<div class="row">
		<div class="col-lg-10 col-lg-offset-1 col-sm-10 col-sm-offset-1 col-xs-10 col-xs-offset-1">
			<form action="" class="form-horizontal">
				<div class="input-group input-group-lg">
				<span class="input-group-btn" ng-if="AllowClearSelect">
				 <button type="button" class="btn btn-default" ng-click="resetProductObj()"><i class="glyphicon glyphicon-remove"></i></button>
				</span> 
				<div class="form-control typeahead-wrapper drop-up  input-lg " style="width:{{AllowClearSelect?500:550;}}px;">
				 <input type="text" ng-disabled="transactionInProgress || ProductLoading || AllowClearSelect" options="typeAheadOptions" datasets="typeAheadData" ng-model="productObject" class="typeahead" sf-typeahead placeholder="{{ProductLoading ? 'Loading...':'Enter product description'}}" />
				 </div>
				 <span class="input-group-btn spacer"> {{selectedItem.category_id}}</span>
				<input ng-show="selectedItem.category_id!=='RODS'" type="number" ng-model="defaultWidth" placeholder="W" class="form-control" auto-select>
				<span class="input-group-addon" ng-show="selectedItem.category_id!=='RODS'">mm &times</span>
				<input type="number" ng-model="defaultLength"  placeholder="L" class="form-control"  auto-select>
				<span class="input-group-addon">mm</span>
				 <span class="input-group-addon">QTY:</span>
				<input type="number"  ng-model="defaultQuantity" class="form-control"  auto-select>
				<span class="input-group-btn">
					<button type="button" class="btn btn-success" ng-disabled="!isItemValid" ng-click="addSelectedItem(defaultLength,defaultWidth,defaultQuantity)">
						<span class="glyphicon glyphicon-plus"></span>
					</button>
				</span>
				</div><!-- /input-group -->
			</form>
		</div>
	</div>
</footer>
<style type="text/css"> 
	#productTypeaheadv2{ padding:2rem 0;}
	.spacer{
		width: 0px;
	}
	.custom{
		-webkit-border-radius: 0px !important;
		-moz-border-radius: 0px !important;
		border-radius: 0px !important;
	}
	#page-content-wrapper >.container-fluid>div>div.row{
		margin-bottom:55px;
	}
</style>