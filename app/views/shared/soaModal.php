<?php
echo '<script type="text/ng-template" id="soaModalContent.html">';
?>
 <div ng-controller="accountModalController" ng-init="initializeController()">
	
	  <div class="modal-header">
		<h4 class="modal-title">Account Information</h4>
	  </div>
	  <div class="modal-body">
		<form action="" class="form-verical">
			<div class="row">
				<div class="col-md-12 col-xs-12" ng-hide="true"><div class="form-group">
					<label for="">
						Account
						<div class="pull-right">
							<div class="checkbox-inline">
							  <input type="radio" value="supplier" name="accountType" ng-checked="accountType=='supplier'"  ng-disabled="id" ng-click="setAccountType('supplier')"/> Supplier
							</div>
							<div class="checkbox-inline">
							  <input type="radio" value="customer" name="accountType" ng-checked="accountType=='customer'"  ng-disabled="id" ng-click="setAccountType('customer')" /> Customer
							</div>
						</div>
					</label>
					<div class="input-group">
						<input type="text" ng-model="customer.name" placeholder="Account Name" class="form-control"  ng-disabled="true"/>
						 <div class="input-group-btn"  ng-class="{open : DropdownOpen}">
							<ul class="dropdown-menu pull-right">
							  <li class="dropdown-header"><i class="glyphicon glyphicon-save-file"></i> Export As</li>
							  <li><a ng-click="exportData(name,'csv')"> CSV</a></li>
							  <li><a ng-click="exportData(name,'pdf')"> PDF</a></li>
							</ul>
							<button type="button" class="btn btn-default" ng-disabled="!id" ng-click="DropdownOpen=!DropdownOpen" ><span class="glyphicon glyphicon-save-file" aria-hidden="true"></span>SOA</button>
						 </div>
					</div>
					
				</div></div>

				<div class="col-md-12 col-xs-12"><div class="form-group">
					<label for="">
						Account
						<div class="pull-right">
							<div class="checkbox-inline">
							  <input type="radio" value="supplier" name="accountType" ng-checked="accountType=='supplier'" ng-model="transacteeSupplier" ng-click="setTransacteeType('supplier')"/> Supplier
							</div>
							<div class="checkbox-inline">
							  <input type="radio" value="customer" name="accountType" ng-checked="accountType=='customer'"  ng-model="transacteeCustomer"  ng-click="setTransacteeType('customer')" /> Customer
							</div>
						</div>
					</label>
					<div class="input-group">
						<div class="typeahead-wrapper" ng-show="accountType=='customer'"><input type="text" class="form-control" ng-readonly="id!=undefined"  options="typeAheadOptions" datasets="typeAheadCustomerData"  sf-typeahead  ng-model="$parent.CustomerObjectTypeAhead"  placeholder="Select Customer"/></div>
						<div class="typeahead-wrapper" ng-show="accountType=='supplier'"><input type="text" class="form-control"  ng-readonly="VendorObject.id!=undefined"  ng-model="VendorObjectTypeAhead" class="form-control"  options="typeAheadOptions" datasets="typeAheadVendorData"  sf-typeahead placeholder="Select Supplier"/></div>
						<div class="input-group-btn">
							<button type="button" class="btn btn-default" ng-click="resetTransactee()" ng-disabled="!$parent.CustomerObject"><span class="glyphicon glyphicon-remove" aria-hidden="true"></span></button>
						</div>
					</div>
				</div></div>
			</div>
		</form>
	  </div>
	  <div class="modal-footer">
		<div class="pull-left" ng-if="id">
			<button type="button" class="btn btn-danger" ng-show="status=='open'" ng-click="closeAccount(id)">Close Account</button>
			<button type="button" class="btn btn-success" ng-show="status=='close'"  ng-click="openAccount(id)">Open Account</button>
			<button type="button" class="btn btn-warning" ng-disabled="!allow_posting" ng-click="postAccount(id)">Post Account</button>
		</div>
		<button type="button" class="btn btn-default" ng-click="cancel()">Cancel</button>
		<button type="button" class="btn btn-primary" ng-click="confirm(id)">Confirm</button>
	  </div>
	</div>

</script>