<?php echo '<script type="text/ng-template" id="billingModalContent.html">';?>
<div ng-controller="billingModalController" ng-init="initializeController()">
  <div class="modal-header">
    <h4 class="modal-title">Create Billing (SOA)</h4>
  </div>
  <div class="modal-body">
    <form action="" class="form-vertical">
      <div class="row">
        <div class="col-md-12 col-xs-12">
          <div class="form-group" ng-if="!id">
            <label for="">Customer</label>
            <div class="input-group">
              <div class="typeahead-wrapper">
                <input type="text" class="form-control" ng-readonly="CustomerObject.id!=undefined"
                  options="typeAheadOptions" datasets="typeAheadCustomerData" sf-typeahead
                  ng-model="CustomerObjectTypeAhead" placeholder="Select Customer"/>
              </div>
              <div class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="resetCustomer()"
                  ng-disabled="!CustomerObject">
                  <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                </button>
              </div>
            </div>
          </div>
          <div class="form-group" ng-if="id">
            <label for="">Customer</label>
              <input type="text" disabled="disabled" ng-model="entity.name" class="form-control">
          </div>
        </div>

         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Prepared Date</label>
            <input type="date" auto-select ng-required="true" ng-model="prepared_date" class="form-control" />
          </div>
        </div>

        <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Due Date</label>
            <input type="date" auto-select ng-required="true" ng-model="billing_date" class="form-control" />
          </div>
        </div>
        
         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Terms</label>
            <input type="text" auto-select ng-required="true" ng-model="terms" class="form-control" />
          </div>
        </div>
        <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">SOA No.</label>
            <input type="text" auto-select ng-required="true" ng-model="soa_no" class="form-control" placeholder="SOA-####" ng-disabled="!!id" />
          </div>
        </div>
         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Previous Balance </label>
            <input type="number" auto-select ng-required="true"  ng-disabled="true" ng-model="previous_balance" class="form-control" />
          </div>
        </div>
         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Selected Invoice</label>
            <input type="number" auto-select ng-required="true"  ng-disabled="true" ng-model="selected_total" class="form-control" />
          </div>
        </div>
        <div class="col-md-8 col-xs-8">
           <ul class="nav nav-tabs" role="tablist" style="margin-top:25px;">

              <li role="presentation" ng-class="{'active': isActiveTab('new-invoices')}">
                <a  ng-click="switchTab('new-invoices')" role="tab">
                  For Billing Invoices
                  <span class="label label-primary" ng-show="selected_count > 0">{{selected_count}}</span>
                  
                </a>
              </li>
              <li role="presentation" ng-class="{'active': isActiveTab('unpaid-invoices')}">
                <a  ng-click="switchTab('unpaid-invoices')" role="tab">
                  Billed Invoices
                  <span class="label label-danger" ng-show="UnpaidInvoices.length > 0">{{UnpaidInvoices.length}}</span>
                  
                </a>
              </li>
            </ul> 
          </div>
        <div class="col-md-4 col-xs-4">
            <div class="form-group">
            <label for="">Oustanding balance</label>
            <input type="number" auto-select ng-required="true"  ng-disabled="true" ng-model="outstanding_balance" class="form-control" />
          </div>
        </div>
      </div>

     

      <!-- No invoices message -->
      <div class="row" ng-show="!LoadingInvoices && (!Invoices || Invoices.length === 0) && CustomerObject">
        <div class="col-md-12 text-center">
          <p>No invoices found for this customer.</p>
        </div>
      </div>

      <!-- Tabbed Interface for Invoices -->
      <div class="row" ng-show="CustomerObject">
        <div class="col-md-12">
         
          <!-- Tab panes -->
          <div class="tab-content" style="margin-top: 15px;">
            <!-- Previous Unpaid Invoices Tab -->
            <div role="tabpanel" ng-show="isActiveTab('unpaid-invoices')" id="unpaid-invoices">
              <div ng-show="UnpaidInvoices && UnpaidInvoices.length > 0">
                <table class="table table-hover table-bordered table-striped">
                  <thead>
                    <tr>
                      <th>Invoice Date</th>
                      <th>Invoice No.</th>
                      <th>SOA No.</th>
                      <th>Amount</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="unpaidInvoice in UnpaidInvoices">
                      <td>{{unpaidInvoice.invoice_date | date: "mediumDate"}}</td>
                      <td>{{unpaidInvoice.ref_no}}</td>
                      <td>{{unpaidInvoice.billing_id}}</td>
                      <td class="numeric">{{unpaidInvoice.invoice_amount | currency:""}}</td>
                      <td>
                        <span class="label" ng-class="{'label-warning': unpaidInvoice.status === 'partial', 'label-danger': unpaidInvoice.status === 'billed'}">
                          {{unpaidInvoice.status | uppercase}}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr class="info">
                      <td colspan="3" class="text-right"><strong>Total Unpaid:</strong></td>
                      <td class="numeric"><strong>{{total_unpaid | currency:""}}</strong></td>
                      <td colspan="3"></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              <div ng-show="!UnpaidInvoices || UnpaidInvoices.length === 0" class="text-center text-muted">
                <p><i class="glyphicon glyphicon-ok-circle"></i> No unpaid invoices found for this customer.</p>
              </div>
            </div>

            <!-- Select New Invoices Tab -->
            <div role="tabpanel" ng-show="isActiveTab('new-invoices')" id="new-invoices">
              <!-- Loading indicator -->
              <div ng-show="LoadingInvoices" class="text-center">
                <p><i class="glyphicon glyphicon-refresh spin"></i> Loading invoices...</p>
              </div>

              <!-- No invoices message -->
              <div ng-show="!LoadingInvoices && (!Invoices || Invoices.length === 0)" class="text-center text-muted">
                <p><i class="glyphicon glyphicon-info-sign"></i> No new invoices found for this customer.</p>
              </div>

              <!-- Invoices table -->
              <div ng-show="Invoices && Invoices.length > 0">
                <table class="table table-hover table-bordered">
                  <thead>
                    <tr>
                      <th width="40px">
                        <input type="checkbox" ng-model="selectAll" ng-change="toggleSelectAll()">
                      </th>
                      <th>Invoice Date</th>
                      <th>Invoice No.</th>
                      <th>PO No.</th>
                      <th>Amount</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="invoice in Invoices">
                      <td>
                        <input type="checkbox" ng-model="invoice.selected">
                      </td>
                      <td>{{invoice.date | date: "mediumDate"}}</td>
                      <td>{{invoice.invoice_no}}</td>
                      <td>{{invoice.po_no}}</td>
                      <td class="numeric">{{invoice.amount | currency:""}}</td>
                      <td>
                            <span class="label label-warning" >
                          {{invoice.status | uppercase}}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr ng-show="hasMoreInvoices()">
                      <td  colspan="6" class="text-center">
                       <button class="btn btn-default" ng-click="loadMoreInvoices()" ng-disabled="LoadingInvoices">
                          <span ng-show="!LoadingInvoices">
                            <i class="glyphicon glyphicon-refresh"></i> Load More Invoices
                          </span>
                          <span ng-show="LoadingInvoices">
                            <i class="glyphicon glyphicon-refresh spin"></i> Loading...
                          </span>
                        </button>
                        </td>
                    </tr>
                    <tr class="success">
                      <td colspan="4" class="text-right"><strong>Total Selected:</strong></td>
                      <td class="numeric"><strong>{{calculateTotal() | currency:""}}</strong></td>
                      <td></td>
                    </tr>
                  </tfoot>
                </table>

                <!-- Load More Button -->
                

                <!-- Invoice Count Info -->
                <div class="row" ng-show="Invoices && Invoices.length > 0">
                  <div class="col-sm-12 text-center text-muted" style="margin-top: 10px;">
                    <small>
                      Showing {{Invoices.length}} of {{totalItems}} invoices
                      <span ng-show="!hasMoreInvoices()"> - All invoices loaded</span>
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" ng-click="cancel()">Cancel</button>
    <button type="button" class="btn btn-primary" ng-click="preview()" ng-hide="!id">Preview</button>
    <button type="button" class="btn btn-primary" ng-click="confirm()" ng-hide="!!id"
      ng-disabled="!CustomerObject || !soa_no || !billing_date || !hasSelectedInvoices()">
      Confirm
    </button>
  </div>
</div>
</script>
