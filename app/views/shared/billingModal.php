<?php echo '<script type="text/ng-template" id="billingModalContent.html">';?>
<div ng-controller="billingModalController" ng-init="initializeController()">
  <div class="modal-header">
    <h4 class="modal-title">Create Billing (SOA)</h4>
  </div>
  <div class="modal-body">
    <form action="" class="form-vertical">
      <div class="row">
        <div class="col-md-12 col-xs-12">
          <div class="form-group" ng-if="!id">
            <label for="">Customer</label>
            <div class="input-group">
              <div class="typeahead-wrapper">
                <input type="text" class="form-control" ng-readonly="CustomerObject.id!=undefined"
                  options="typeAheadOptions" datasets="typeAheadCustomerData" sf-typeahead
                  ng-model="CustomerObjectTypeAhead" placeholder="Select Customer"/>
              </div>
              <div class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="resetCustomer()"
                  ng-disabled="!CustomerObject">
                  <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                </button>
              </div>
            </div>
          </div>
          <div class="form-group" ng-if="id">
            <label for="">Customer</label>
              <input type="text" disabled="disabled" ng-model="entity.name" class="form-control">
          </div>
        </div>

         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Prepared Date</label>
            <input type="date" auto-select ng-required="true" ng-model="prepared_date" class="form-control" />
          </div>
        </div>

        <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Due Date</label>
            <input type="date" auto-select ng-required="true" ng-model="billing_date" class="form-control" />
          </div>
        </div>
        
         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Terms</label>
            <input type="text" auto-select ng-required="true" ng-model="terms" class="form-control" />
          </div>
        </div>
        <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">SOA No.</label>
            <input type="text" auto-select ng-required="true" ng-model="soa_no" class="form-control" placeholder="SOA-####" ng-disabled="!!id" />
          </div>
        </div>
         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Previous Balance </label>
            <input type="number" auto-select ng-required="true"  ng-disabled="true" ng-model="previous_balance" class="form-control" />
          </div>
        </div>
         <div class="col-md-4 col-xs-4">
          <div class="form-group">
            <label for="">Oustanding Balance</label>
            <input type="number" auto-select ng-required="true"  ng-disabled="true" ng-model="outstanding_balance" class="form-control" />
          </div>
        </div>
      </div>

      <!-- Loading indicator -->
      <div class="row" ng-show="LoadingInvoices">
        <div class="col-md-12 text-center">
          <p><i class="glyphicon glyphicon-refresh spin"></i> Loading invoices...</p>
        </div>
      </div>

      <!-- No invoices message -->
      <div class="row" ng-show="!LoadingInvoices && (!Invoices || Invoices.length === 0) && CustomerObject">
        <div class="col-md-12 text-center">
          <p>No invoices found for this customer.</p>
        </div>
      </div>

      <!-- Unpaid Invoices table -->
      <div class="row" ng-show="UnpaidInvoices && UnpaidInvoices.length > 0">
        <div class="col-md-12">
          <h5>Previous Unpaid Invoices</h5>
          <table class="table table-hover table-bordered table-striped">
            <thead>
              <tr>
                <th>Invoice Date</th>
                <th>Invoice No.</th>
                <th>SOA No.</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Due Date</th>
                <th>Terms</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="unpaidInvoice in UnpaidInvoices">
                <td>{{unpaidInvoice.invoice_date | date: "mediumDate"}}</td>
                <td>{{unpaidInvoice.ref_no}}</td>
                <td>{{unpaidInvoice.billing_id}}</td>
                <td class="numeric">{{unpaidInvoice.invoice_amount | currency:""}}</td>
                <td>
                  <span class="label" ng-class="{'label-warning': unpaidInvoice.status === 'partial', 'label-danger': unpaidInvoice.status === 'billed'}">
                    {{unpaidInvoice.status | uppercase}}
                  </span>
                </td>
                <td>{{unpaidInvoice.due_date | date: "mediumDate"}}</td>
                <td>{{unpaidInvoice.terms}}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3" class="text-right"><strong>Total Unpaid:</strong></td>
                <td class="numeric"><strong>{{total_unpaid | currency:""}}</strong></td>
                <td colspan="3"></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      <!-- Invoices table -->
      <div class="row" ng-show=" Invoices!=undefined">
        <div class="col-md-12">
          <h5>Select New Invoices for Billing</h5>
          <table class="table table-hover table-bordered">
            <thead>
              <tr>
                <th width="40px">
                  <input type="checkbox" ng-model="selectAll" ng-change="toggleSelectAll()">
                </th>
                <th>Invoice Date</th>
                <th>Invoice No.</th>
                <th>PO No.</th>
                <th>Amount</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="invoice in Invoices">
                <td>
                  <input type="checkbox" ng-model="invoice.selected">
                </td>
                <td>{{invoice.date | date: "mediumDate"}}</td>
                <td>{{invoice.invoice_no}}</td>
                <td>{{invoice.po_no}}</td>

                <td class="numeric">{{invoice.amount | currency:""}}</td>
                <td>{{invoice.status}}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="4" class="text-right"><strong>Total Selected:</strong></td>
                <td class="numeric"><strong>{{calculateTotal() | currency:""}}</strong></td>
                <td></td>
              </tr>
            </tfoot>
          </table>

          <!-- Pagination Controls -->
          <div class="row" ng-show="LastPage > 1">
            <div class="col-sm-12 text-right">
              <div class="input-group">
                <div class="input-group-btn">
                  <button class="btn btn-default" ng-disabled="LoadingInvoices || CurrentPage==1" ng-click="prevPage()">&laquo;</button>

                  <!-- Use ActivePages array for pagination -->
                  <button class="btn btn-default" ng-disabled="LoadingInvoices"
                    ng-repeat="page in ActivePages"
                    ng-class="{'btn-primary': page===CurrentPage}"
                    ng-click="movePage(page)">{{page}}</button>

                  <button class="btn btn-default" ng-disabled="LoadingInvoices || CurrentPage==LastPage" ng-click="nextPage()">&raquo;</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" ng-click="cancel()">Cancel</button>
    <button type="button" class="btn btn-primary" ng-click="preview()" ng-hide="!id">Preview</button>
    <button type="button" class="btn btn-primary" ng-click="confirm()" ng-hide="!!id"
      ng-disabled="!CustomerObject || !soa_no || !billing_date || !hasSelectedInvoices()">
      Confirm
    </button>
  </div>
</div>
</script>
