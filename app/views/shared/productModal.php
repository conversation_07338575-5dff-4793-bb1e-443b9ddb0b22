<?php echo '<script type="text/ng-template" id="productModalContent.html">'; ?>


    <div ng-controller="productModalController" ng-init="initializeController()">
	
	  <div class="modal-header">
		<h4 class="modal-title">Product Information</h4>
	  </div>
	  <div class="modal-body">
		<form action="" class="form-verical">
			<ul class="nav nav-tabs">
			  <li role="presentation" ng-class="{active:ActiveProductTab=='specification'}">
			  	<a  ng-click="ActiveProductTab='specification'" >Specification</a>
			  </li>
			  <li role="presentation" ng-hide="StockRoomMode" ng-class="{active:ActiveProductTab=='pricing'}">
			  	<a  ng-click="ActiveProductTab='pricing'" >Pricing</a>
			  </li>
			  <li role="presentation" ng-class="{active:ActiveProductTab=='quantity'}">
			  	<a  ng-click="ActiveProductTab='quantity'" >Quantity</a>
			  </li>
			  <li role="presentation" ng-class="{active:ActiveProductTab=='stock'}">
			  	<a  ng-click="ActiveProductTab='stock'" ng-show="StockRoomMode" >Physical Stock</a>
			  </li>
			</ul>
			<section ng-show="ActiveProductTab=='specification'">
				<div class="row">
				<input type="hidden" ng-model="id"/>
				<div class="col-md-6 col-xs-6"><div class="form-group"><label for="">Type</label>
					<div ng-if="StockRoomMode && type"><p class="form-control-static" ng-repeat="type in Types|filter:{id:type}:true">{{type.name}}</p></div>
					<select ng-if="!StockRoomMode" class="form-control" ng-model="$parent.type" ng-required="true">
						<option>Select one</option>
						<option ng-selected="type.id==$parent.type"  ng-repeat="type in Types" value="{{type.id}}">{{type.name}}</option>
					</select>
				</div></div>
				<div class="col-md-6 col-xs-6"><div class="form-group"><label for="">Category</label>
					<div ng-if="StockRoomMode && category_id"><p class="form-control-static" ng-repeat="category in Categories|filter:{id:category_id}:true">{{category.name}}</p></div>
					<select ng-if="!StockRoomMode" class="form-control" ng-model="$parent.category_id" ng-required="true">
						<option>Select one</option>
						<option ng-selected="category.id==$parent.category_id"  ng-repeat="category in Categories" value="{{category.id}}">{{category.name}}</option>
					</select>
				</div></div>
				
				<div class="col-md-12 col-xs-12">
					<div class="form-group">
						<label for="">Search Alias</label><span><i> ( For Search )</i></span>
						<div ng-if="StockRoomMode"><p class="form-control-static">{{particular}}</p></div>
						<input ng-if="!StockRoomMode" type="text" ng-required="true"  ng-model="$parent.particular" class="form-control" />
					</div>
				</div>
				<div class="col-md-12 col-xs-12">
					<div class="form-group">
						<label for="">Print Description</label><span><i> ( For Printing JO SI PO )</i></span>
						<div ng-if="StockRoomMode"><p class="form-control-static">{{description}}</p></div>
						<input ng-if="!StockRoomMode" type="text"  ng-model="$parent.description" class="form-control" />
					</div>
				</div>
				<div class="col-md-3 col-xs-3">
					<div class="form-group"><label for="">Unit</label>
						<div ng-if="StockRoomMode"><p class="form-control-static">{{unit}}</p></div>
						<input ng-if="!StockRoomMode" type="text" ng_model="$parent.unit" class="form-control" />
					</div>
				</div>

				<div class="col-md-3 col-xs-3">
					<div class="form-group">
						<label for="">Thickness </label>
						<div ng-if="StockRoomMode">
							<p class="form-control-static">
								{{thickness}} {{unit}}
							</p>
						</div>
						<input  ng-if="!StockRoomMode" type="text" ng-model="$parent.thickness" class="form-control" />
					</div>
				</div>
				<div class="col-md-6 col-xs-6">
					<div class="form-group">
						<label for="">Cutting Allowance </label>
						<div ng-if="StockRoomMode">
							<p class="form-control-static">
								{{allowance}} {{unit}}
							</p>
						</div>
						<input  ng-if="!StockRoomMode" type="text" ng-model="$parent.allowance" class="form-control" />
					</div>
				</div>
				
				
				<div class="col-md-3 col-xs-3">
					<div class="form-group"><label for="">Length</label>
						<div ng-if="StockRoomMode">
							<p class="form-control-static">
							{{length}} {{unit}}
							</p>
						</div>
						<input  ng-if="!StockRoomMode" type="text" ng-model="$parent.length" class="form-control" />
					</div>
				</div>
				<div class="col-md-3 col-xs-3">
					<div class="form-group"><label for="">Width</label>
						<div ng-if="StockRoomMode">
							<p class="form-control-static">
								{{width}} {{unit}}
							</p>
						</div>
						<input ng-if="!StockRoomMode" type="text" ng-model="$parent.width" class="form-control" />
					</div>
				</div>
				<div class="col-md-6 col-xs-6">
					<div class="form-group"><label for="">Area</label>
						<div ng-if="StockRoomMode">
							<p class="form-control-static">
							{{length*width|number:3}}
							</p>
						</div>
						<div class="input-group">
							<input ng-if="!StockRoomMode" type="text" readonly ng-value="$parent.length*$parent.width|number:3" class="form-control" />
							<span class="input-group-btn">
								<button class="btn btn-default" ng-disabled="!adjustment" ng-click="copyToSOH(length*width)" type="button">Copy as SOH</button>
							</span>
						</div>
					</div>
				</div>
			</section>
			<section ng-show="ActiveProductTab=='pricing' && !StockRoomMode">
				<div class="row">
					<div class="col-md-6 col-xs-6"><div class="form-group"><label for="">Capital</label><input type="number" auto-select ng-required="true"  ng-model="capital" ng-change="updateMarkup()"  class="form-control" /></div></div>
					<div class="col-md-6 col-xs-6"><div class="form-group"><label for="">Markup</label><input type="number" auto-select ng-required="true"  ng-model="markup" ng-change="updateSrp()" class="form-control" /></div></div>
					<div class="col-md-6 col-xs-6"><div class="form-group"><label for="">Price</label><input type="number" auto-select ng-model="srp"  ng-change="updateMarkup()"   class="form-control" /></div></div>
					<div class="col-md-6 col-xs-6"><div class="form-group"><label for="">Discountable</label>
					<select class="form-control" ng-model="discountable">
						<option>Select one</option>
						<option ng-selected="flag.value==discountable" ng-repeat="flag in [{value:1,label:'Yes'},{value:0,label:'No'}]" value="{{flag.value}}">{{flag.label}}</option>
					</select>
				</div></div>
					<div class="col-md-12 col-xs-12" ng-show="!StockRoomMode"><div class="form-group"><label for="">Price Adjustment</label><div class="input-group"><input type="number" auto-select ng-readonly="!StockRoomMode"  ng-model="adjustment.tmp_srp" class="form-control" /> 
						<span class="input-group-btn">
							<button class="btn btn-default" ng-disabled="!adjustment" ng-click="applyAdjustment('price')" type="button">Apply</button>
							</span>
						</div>
					  </div>
					 </div>
					
				</div>
			</section>
			<section ng-show="ActiveProductTab=='quantity'">
				<div class="row">
					<div class="col-md-4 col-xs-4"><div class="form-group"><label for="">Stock On Hand (Area)</label>
						<input type="number" auto-select ng-readonly="StockRoomMode" ng-required="true"  ng-model="soh_quantity" class="form-control" /></div></div>
					<div class="col-md-4 col-xs-4"><div class="form-group"><label for="">Max</label><input type="number" auto-select ng-readonly="StockRoomMode"  ng-model="max_quantity" class="form-control" /></div></div>
					<div class="col-md-4 col-xs-4"><div class="form-group"><label for="">Min</label><input type="number" auto-select ng-readonly="StockRoomMode"  ng-model="min_quantity" class="form-control" /></div></div>
					<div class="col-md-4 col-xs-4" ng-show="!StockRoomMode"><div class="form-group"><label for="">Last Adjustment On</label><input type="text" ng-readonly="!StockRoomMode" ng-required="true"  ng-model="adjustment.adj_qty_timestamp" class="form-control" /></div></div>
					<div class="col-md-4 col-xs-4" ng-show="!StockRoomMode"><div class="form-group"><label for="">Initial Adjustment</label><input type="number" auto-select ng-readonly="!StockRoomMode" ng-required="true"  ng-model="adjustment.tmp_quantity" class="form-control" /></div></div>
					<div class="col-md-4 col-xs-4" ng-show="!StockRoomMode"><div class="form-group"><label for="">Current Adjustment</label><div class="input-group"><input type="number" auto-select ng-readonly="!StockRoomMode"  ng-model="adjustment.adj_quantity" class="form-control" /> 
						<span class="input-group-btn">
							<button class="btn btn-default" ng-disabled="!adjustment" ng-click="applyAdjustment('quantity')" type="button">Apply</button>
							</span>
						</div>
					  </div>
					 </div>
					<div class="col-md-12 col-xs-12" ng-show="StockRoomMode" ><div class="form-group"><label for="">Physical Count</label><input type="number" auto-select  ng-model="tmp_quantity" class="form-control input-lg" /></div></div>
				</div>
				<div class="row">
					<div class="col-md-4 col-xs-4" ng-show="!StockRoomMode">
						<div class="form-group">
							<label for="">Stock Room Quantity</label>
							<input type="number" auto-select ng-readonly="!StockRoomMode" ng-required="true"  ng-model="stock_quantity" class="form-control" />
						</div>
					</div>
					<div class="col-md-4 col-xs-4" ng-show="!StockRoomMode">
						<div class="form-group">
							<label for="">Stock Room Area</label>
							<input type="number" auto-select ng-readonly="!StockRoomMode" ng-required="true"  ng-model="stock_area" class="form-control" />
						</div>
					</div>
				</div>
			</section>
			<section ng-show="ActiveProductTab=='stock' && StockRoomMode">
				<div class="row">
					<div class="col-md-12 col-xs-12" ng-show="StockRoomMode" ><div class="form-group">
						<label for="">Item</label>
						<div class="input-group">
							<span class="input-group-addon">
								{{$parent.category_id=='PLT8'?'PLATE':$parent.category_id=='RODS'?'ROD':'OTHERS'}}
						</span>
						<input type="text" disabled  value="{{$parent.display_title}}" class="form-control input-lg" />
						</div>
					</div></div>
				</div>
				<div class="row">
					<div class="col-md-12 col-xs-12" ng-show="StockRoomMode" ><div class="form-group">
						<label for="">Total Area</label>
						<input type="number" disabled  ng-model="tmp_quantity" class="form-control input-lg" /></div></div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<table class="table table-condense table-bordered">
							<thead>
								<tr>
									<th class="col-md-2">Code</th>
									<th>Type</th>
									<th>Quantity</th>
									<th>Area</th>
									<th>Notes</th>
									<th>
										<span class="btn btn-sm">
											<span class="glyphicon glyphicon-info"></span>
										</span>
									</th>
								</tr>
								<tr class="success">
									<td>
										<input ng-model="stockCode" type="text" class="form-control" placeholder="Required" />
									</td>
									<td>
										<select ng-model="stockType" class="form-control">
											<option value="">Req. Type</option>
											<option value="LC">Loose Cut</option>
											<option value="FL">Full Sheet / Rod</option>
										</select>
									</td>
									<td>
										<input ng-model="stockQuantity" type="number" class="form-control" placeholder="Required">
									</td>
									<td>
										<input ng-model="stockArea" type="number" class="form-control" placeholder="Required {{$parent.category_id=='PLT8'?'Area':'Length'}}">
									</td>
									<td>
										<input ng-model="stockNotes" type="text" class="form-control" placeholder="W&nbsp;x&nbsp;L" />
									</td>
									<td>
										<button class="btn btn-success" ng-click="addStock()" ng-disabled="!stockCode && !stockType && !stockArea">
											<span class="glyphicon glyphicon-plus"></span>
										</button>
									</td>
								</tr>
							</thead>
							<tbody>
								<tr ng-repeat="stockItem  in stocks">
									<td>
										<input ng-model="stockItem.id" type="text" class="form-control" readonly/>
									</td>
									<td>
										<select ng-model="stockItem.type" class="form-control">
											<option value="">Select Type</option>
											<option value="LC">Loose Cut</option>
											<option value="FL">Full Sheet / Rod</option>
										</select>
									</td>
									<td>
										<input ng-show="stockItem.is_new"  ng-model="stockItem.quantity" type="number" class="form-control">

										<input  ng-hide="stockItem.is_new" value="{{stockItem.quantity_actual}}/{{stockItem.quantity}}" type="text" class="form-control text-right" readonly>
									</td>
									<td>
										<input ng-show="stockItem.is_new" ng-model="stockItem.area" type="number" class="form-control">

										<input ng-hide="stockItem.is_new" ng-model="stockItem.area_actual" type="number" class="form-control" readonly>
									</td>
									<td>
										<input ng-model="stockItem.notes" type="text" class="form-control" placeholder="W&nbsp;x&nbsp;L" />
									</td>
									<td>
										<button class="btn btn-danger" ng-click="removeStock($index)">
											<span class="glyphicon glyphicon-remove"></span>
										</button>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</section>
		</form>
	  </div>
	  <div class="modal-footer">
		<button type="button" class="btn btn-success pull-left" ng-click="activate(id)" ng-show="id && !DisableDelete && status =='archive'">Activate</button>
		<button type="button" class="btn btn-danger pull-left" ng-click="archive(id)" ng-show="id && !DisableDelete && status =='active'">Archive</button>
		<button type="button" class="btn btn-default" ng-click="cancel()">Cancel</button>
		<button type="button" class="btn btn-primary" ng-click="confirm(id)">Confirm</button>
	  </div>
	</div>

</script>