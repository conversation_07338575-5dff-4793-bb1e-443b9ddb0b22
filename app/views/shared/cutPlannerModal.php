
<?php date_default_timezone_set('Asia/Manila');
	echo "<script type='text/ng-template' id='cutPlannerModalContent.html'>";
?>

<div ng-controller="cutPlannerModalController" ng-init="initializeController()">
	
	  <div class="modal-header">
		<h4 class="modal-title">Cut Planner</h4>		
	  </div>
	  <div class="modal-body">
		<form action="" class="form-horizontal">
			<div class="row">
				<div class="col-md-12 col-xs-12">
					<div class="form-group">
						<div class="row">
							<div class="form-group">
								<label class="control-label col-sm-3 ">Material</label>
								<div class="col-sm-8">
									<select ng-model="ActiveMaterial" class="form-control">
										<option value="">Select one</option>
										<option ng-value="material.id" ng-repeat="material in Materials">
											{{material.description}}
										</option>
									</select>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="form-group">
								<label class="control-label col-sm-3 ">Sheet</label>
								<div class="col-md-3">
									<div class="input-group">
										<input type="number" class="form-control" ng-model="Length" placeholder="Length" />
										<span class="input-group-addon">mm</span>
									</div>
								</div>
								<div class="col-md-3">
									<div class="input-group">
										<input type="number" class="form-control" ng-model="Width" placeholder="Width" />
										<span class="input-group-addon">mm</span>
									</div>
								</div>
								<div class="col-md-3">
									<a class="btn btn-default" ng-click="calculateSheet()" ng-disabled="!ActiveMaterial && !Length && !Width">Calculate</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<ul class="nav nav-tabs">
			  			<li role="presentation" ng-class="{active:ActivePlanTab=='plan'}">
			  				<a  ng-click="ActivePlanTab='plan'" >Plan</a>
			  			</li>
			  			<li role="presentation" ng-class="{active:ActivePlanTab=='guide'}">
			  				<a  ng-click="ActivePlanTab='guide'" >Guide</a>
			  			</li>
			  		</ul>
					<section ng-show="ActivePlanTab=='plan'">
						<canvas id="cut-plan" style="width: {{displayWidth}}px;height: {{displayHeight}}px;border: 1px solid;">
								</canvas>
					</section>
					<section ng-show="ActivePlanTab=='guide'">
							<table class="table table-bordered">
								<thead>
									<tr>
										<th>Code</th>
										<th>Dimension</th>
										<th>Qty</th>
									</tr>
								</thead>
								<tbody>
									<tr ng-repeat="item in CutPlanItems">
										<td class="text-center">{{item.code}}</td>
										<td>{{item.w}}mm &times; {{item.h}}mm</td>
										<td  class="text-center">{{item.quantity}}</td>
									</tr>
								</tbody>
							</table>
					</section>
				</div>
			</div>
		</div>
		</form>
	  </div>
	  <div class="modal-footer">
		<button type="button" class="btn btn-default pull-left" ng-click="cancel()">Cancel</button>
		<button type="button" class="btn btn-primary" ng-click="confirm(CutPlanObj)" ng-disabled="false">Confirm</button>
	  </div>
	</div>
</script>