define(['config', 'api'], function (app, api) {
	app.register.service('invoiceService', ['$rootScope', function ($rootScope) {
		const ALLOW_CACHE = false;
		const CONFIG = {page:1,limit:10,keyword:null,fields:['si_no', 'customer_name', 'invoice_date'],filter:{type:'invoice',coverage:'all',status:'fulfilled'},sort:'oldest'};
		const EXPORT_FORMAT = 'csv';
		var __invoices;
		var __callback;
		var __successGetInvoices = function(response, status, $scope) {
			__invoices = response;
			if(__callback) __callback(response, status, $scope);
		};

		this.addInvoice = function(successAddInvoice, errorAddInvoice, $scope) {
			var invoice = {};
				invoice.si_no = $scope.si_no;
				invoice.invoice_date = $scope.invoice_date;
				invoice.customer_id = $scope.customer_id;
				invoice.po_no = $scope.po_no;
				invoice.po_date = $scope.po_date;
				invoice.purchase_order_id = $scope.purchase_order_id;
				invoice.jo_id = $scope.jo_id;
				invoice.dr_no = $scope.dr_no;
				invoice.cr_no = $scope.cr_no;
				invoice.amount = $scope.amount;
				invoice.discount = $scope.discount;
				invoice.discount_percent = $scope.discount_percent;
				invoice.tax_type = $scope.tax_type;
				invoice.vat_amount = $scope.vat_amount;
				invoice.status = $scope.status;
				invoice.payment_status = $scope.payment_status;
				invoice.payment_terms = $scope.payment_terms;
				invoice.details = $scope.details;
				invoice.forceLive = true;
			api.POST("/invoices", successAddInvoice, errorAddInvoice, $scope, invoice);
		};

		this.updateInvoice = function(successUpdateInvoice, errorUpdateInvoice, $scope) {
			var invoice = {};
				invoice.id = $scope.id;
				invoice.si_no = $scope.si_no;
				invoice.invoice_date = $scope.invoice_date;
				invoice.customer_id = $scope.customer_id;
				invoice.po_no = $scope.po_no;
				invoice.po_date = $scope.po_date;
				invoice.purchase_order_id = $scope.purchase_order_id;
				invoice.jo_id = $scope.jo_id;
				invoice.dr_no = $scope.dr_no;
				invoice.cr_no = $scope.cr_no;
				invoice.amount = $scope.amount;
				invoice.discount = $scope.discount;
				invoice.discount_percent = $scope.discount_percent;
				invoice.tax_type = $scope.tax_type;
				invoice.vat_amount = $scope.vat_amount;
				invoice.status = $scope.status;
				invoice.payment_status = $scope.payment_status;
				invoice.payment_terms = $scope.payment_terms;
				invoice.forceLive = true;
			api.POST("/invoices", successUpdateInvoice, errorUpdateInvoice, $scope, invoice);
		};

		this.getInvoice = function(successGetInvoice, errorGetInvoice, $scope, id) {
			var invoice = {};
				invoice.id = id;
				invoice.forceLive = true;
			api.GET("/invoices", successGetInvoice, errorGetInvoice, $scope, invoice);
		};

		this.archiveInvoice = function(successArchiveInvoice, errorArchiveInvoice, $scope, id) {
			var invoice = {};
				invoice.id = id;
				invoice.action = 'archive';
				invoice.forceLive = true;
			api.POST("/invoices", successArchiveInvoice, errorArchiveInvoice, $scope, invoice);
		};

		this.activateInvoice = function(successActivateInvoice, errorActivateInvoice, $scope, id) {
			var invoice = {};
				invoice.id = id;
				invoice.action = 'activate';
				invoice.forceLive = true;
			api.POST("/invoices", successActivateInvoice, errorActivateInvoice, $scope, invoice);
		};

		this.getInvoices = function(successGetInvoices, errorGetInvoices, $scope, config) {
			if(config == undefined || typeof config != "object") config = CONFIG;
			else {
				if(config.page == undefined) config.page = CONFIG.page;
				if(config.limit == undefined) config.limit = CONFIG.limit;
				if(config.sort == undefined) config.sort = CONFIG.sort;
				if(config.keyword) config.fields = CONFIG.fields.join(',');
				if(config.filter == undefined) config.filter = CONFIG.filter;
			}

			__callback = successGetInvoices;

			if(__invoices && ALLOW_CACHE) {
				__callback(__invoices, "00", $scope);
			} else {
				var config_str = [];
				for(var key in config) {
					if(key != 'page' && config[key]) {
						var value = config[key];
						if(typeof value == "object") value = JSON.stringify(value);
						config_str.push(key + '=' + value);
					}
				}

				api.GET("/invoices?" + config_str.join('&') + "&page=" + config.page, __successGetInvoices, errorGetInvoices, $scope, {forceLive: true});
			}
		};

		this.getCustomerInvoices = function(successGetInvoices, errorGetInvoices, $scope, customerName, config) {
			if(config == undefined || typeof config != "object") config = CONFIG;
			else {
				if(config.page == undefined) config.page = CONFIG.page;
				if(config.limit == undefined) config.limit = CONFIG.limit;
				if(config.sort == undefined) config.sort = CONFIG.sort;
			}

			var filter = {
				type: 'invoice',
				coverage: 'all',
				status: config.status || 'fulfilled'
			};

			var params = {
				page: config.page,
				limit: config.limit,
				sort: config.sort,
				filter: filter
			};

			if(customerName) {
				params.keyword = customerName;
				params.fields = 'entity_name';
			}

			var config_str = [];
			for(var key in params) {
				if(key != 'page' && params[key]) {
					var value = params[key];
					if(typeof value == "object") value = JSON.stringify(value);
					config_str.push(key + '=' + encodeURIComponent(value));
				}
			}

			api.GET("/transactions?" + config_str.join('&') + "&page=" + params.page, successGetInvoices, errorGetInvoices, $scope, {forceLive: true});
		};

		this.createBilling = function(successCreateBilling, errorCreateBilling, $scope,data) {
			var billing = {};
				billing.header = data.header;
				billing.details = data.details;
				billing.invoices = data.invoices;
				billing.forceLive = true;
				$rootScope.transactionInProgress = true;
				$rootScope.__Progress = 0;
			api.POST("/transactions", successCreateBilling, errorCreateBilling, $scope, billing);
		};

		

		this.exportData = function(config) {
			if(config == undefined || typeof config != "object") config = CONFIG;
			else {
				if(config.sort == undefined) config.sort = CONFIG.sort;
				if(config.format == undefined) config.format = EXPORT_FORMAT;
				if(config.keyword) config.fields = CONFIG.fields.join(',');
			}

			var config_str = [];
			for(var key in config) {
				if(key != 'page' && config[key]) {
					var value = config[key];
					if(typeof value == "object") value = JSON.stringify(value);
					config_str.push(key + '=' + value);
				}
			}

			window.open(api.getSettings().apiBaseUrl + "/invoices?" + config_str.join('&'));
		};
    }]);
});
