define(['config','api'], function (app,api) {
	app.register.service('billingService', ['$rootScope','$interval', function ($rootScope,$interval) {
		const ALLOW_CACHE = false;
		const CONFIG = {page:1,limit:2,keyword:null,fields:['entity_name'],'export':['ref_no','Customer.name','Billing.details','amount'],filter:['entity_name','ref_no'],sort:'latest'};
		const EXPORT_FORMAT = 'csv';
		
		this.getLatestOrder = function (successGetLatestOrder,errorGetLatestOrder,$scope){
			var order ={forceLive:true};
			api.GET("/orders?sort=latest&limit=1",successGetLatestOrder,errorGetLatestOrder,$scope,order);
		}

		this.generateSOANo =  function(successGenerateSOANo,errorGenerateSOANo,$scope){
			
		}

		this.getBillingInfo = function(successGetBillInfo, errorGetBillInfo, $scope){
			let billInfo =  {forceLive:true};
			let customerId =  $scope.CustomerId;
			api.GET('/../v2/billings/details.json?customer_id='+customerId,successGetBillInfo,errorGetBillInfo,$scope,billInfo);
		}
	}]);
});
