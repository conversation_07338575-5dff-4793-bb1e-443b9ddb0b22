define(['config','api'], function (app,api) {
	app.register.service('orderService', ['$rootScope','$interval', function ($rootScope,$interval) {
		const ALLOW_CACHE = false;
		const CONFIG = {page:1,limit:2,keyword:null,fields:['entity_name'],'export':['timestamp','type','status','user','amount','entity_type','entity_id','Supplier.name','Cashflow.name'],filter:null,sort:'latest'};
		const EXPORT_FORMAT = 'csv';
		
		this.getLatestOrder = function (successGetLatestOrder,errorGetLatestOrder,$scope){
			var order ={forceLive:true};
			api.GET("/orders?sort=latest&limit=1",successGetLatestOrder,errorGetLatestOrder,$scope,order);
		}
	}]);
});