define(['config','api'], function (app,api) {
	app.register.service('purchaseOrderService', ['$rootScope','$interval', function ($rootScope,$interval) {
		const ALLOW_CACHE = false;
		const CONFIG = {page:1,limit:2,keyword:null,fields:['entity_name'],'export':['timestamp','type','status','user','amount','entity_type','entity_id','Supplier.name','Cashflow.name'],filter:null,sort:'latest'};
		const EXPORT_FORMAT = 'csv';
		var __details;
		var __terms;
		var __callback ;
		var __successGetDetails =   function(response,status,$scope){
			__details = response;
			if(__callback) __callback(response,status,$scope);
		}

		var __successGetTerms =   function(response,status,$scope){
			__terms = response;
			if(__callback) __callback(response,status,$scope);
		}
		
		this.getDetails = function (successGetDetails,errorGetDetails,$scope,config){
			if(config==undefined || typeof config != "object")  config = CONFIG;
			else{
				if(config.page==undefined) config.page = CONFIG.page;
				if(config.limit==undefined) config.limit = CONFIG.limit;
				if(config.sort==undefined) config.sort = CONFIG.sort;
				if(config.keyword) config.fields = CONFIG.fields.join(',');
			}
			__callback = successGetDetails;
			if(__details&&ALLOW_CACHE){
				__callback(__details,"00",$scope);
			}else{
				var config_str = [];
				for(var key in config){
					if(key!='page' && config[key] ){
						var value = config[key];
						if(typeof value == "object") value = JSON.stringify(value);
						config_str.push(key+'='+value);
					}
				}
				api.GET("/purchase_order_details?"+config_str.join('&')+"&page="+config.page,__successGetDetails,errorGetDetails,$scope,{forceLive:true});
			}
		}
		this.getTerms = function (successGetTerms,errorGetTerms,$scope,config){
			if(config==undefined || typeof config != "object")  config = CONFIG;
			else{
				if(config.page==undefined) config.page = CONFIG.page;
				if(config.limit==undefined) config.limit = CONFIG.limit;
				if(config.sort==undefined) config.sort = CONFIG.sort;
				if(config.keyword) config.fields = CONFIG.fields.join(',');
			}
			__callback = successGetTerms;
			if(__terms&&ALLOW_CACHE){
				__callback(__terms,"00",$scope);
			}else{
				var config_str = [];
				for(var key in config){
					if(key!='page' && config[key] ){
						var value = config[key];
						if(typeof value == "object") value = JSON.stringify(value);
						config_str.push(key+'='+value);
					}
				}
				api.GET("/purchase_order_terms?"+config_str.join('&')+"&page="+config.page,__successGetTerms,errorGetTerms,$scope,{forceLive:true});
			}
		}
	}]);
});