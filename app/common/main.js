require.config({
    baseUrl:'',
    urlArgs :(function(){
        const metaVersion = document.querySelector('meta[name="version"]').getAttribute('content');
        const metaHash = document.querySelector('meta[name="hash"]').getAttribute('content');
        const cacheBreak =`${metaVersion}-${metaHash}` + (function(){
            if(window.location.hostname=="localhost")
                return'=__local-'+(new Date().valueOf().toString().substr(9))
            return '';
        })();
        return cacheBreak;

        }()),
	waitSeconds: 160,
    // alias libraries paths
    paths: {
        'api': 'common/api',       
        'responses': 'common/responses',       
        'messages': 'common/messages',       
        'settings': 'common/settings',       
        'test': 'common/test',       
        'config': 'common/config',       
        'ui': 'controllers/main/UIController',
        'jquery': 'libs/bower_components/jquery/dist/jquery.min',
        'typeahead': 'libs/bower_components/typeahead.js/dist/typeahead.bundle',
        'angular': 'libs/bower_components/angular/angular.min',
		'angular-typeahead': 'libs/bower_components/angular-typeahead/angular-typeahead',
        'angular-route': 'libs/bower_components/angular-route/angular-route.min',
		'angular-cookies': 'libs/bower_components/angular-cookies/angular-cookies.min',
        'angularAMD': 'libs/bower_components/angularAMD/angularAMD.min',
        'ui-bootstrap' : 'libs/bower_components/angular-bootstrap/ui-bootstrap-tpls.min',
        'blockUI': 'libs/bower_components/angular-block-ui/dist/angular-block-ui.min',
        'chart':'libs/bower_components/Chart.js/Chart',
        'angular-chart':'libs/bower_components/angular-chart.js/angular-chart',
        'ngload': 'libs/bower_components/angularAMD/ngload.min', 
		'angular-sanitize': 'libs/bower_components/angular-sanitize/angular-sanitize.min',
		'ajaxService':'services/AjaxServices',
		'productService':'services/ProductServices',
		'vendorService':'services/VendorServices',
		'customerService':'services/CustomerServices',
		'userService':'services/UserServices',
		'alertsService':'services/AlertsServices',
        'cashflowService':'services/cashflowServices',
        'purchaseOrderService':'services/PurchaseOrderServices',
		'productModalController':'controllers/shared/productModalController',
		'productTypeaheadController':'controllers/shared/productTypeaheadController',
		'transactionService':'services/TransactionServices',
		'transactionModalController':'controllers/shared/transactionModalController',
		'ledgerModalController':'controllers/shared/ledgerModalController',
		'accountModalController':'controllers/shared/accountModalController',
        'userModalController':'controllers/shared/userModalController',
        'cashflowModalController':'controllers/shared/cashflowModalController',
		'searchController':'controllers/shared/searchController',
        'cutPlannerModalController':'controllers/shared/cutPlannerModalController',
        'cutPacker':'vendors/cutPlanner/packer',
        'cutDrawer':'vendors/cutPlanner/drawer',
        'orderService':'services/OrderServices',
        'billingModalController':'controllers/shared/billingModalController',
        'invoiceService':'services/InvoiceService',
        'angular-ui-tree': 'libs/bower_components/angular-ui-tree/dist/angular-ui-tree',
    },
    // Add angular modules that does not support AMD out of the box, put it in a shim
    shim: {
		'jquery' : {exports : 'jQuery'},
        'angular' : {exports : 'angular'},
		'typeahead' : ['jquery'],
        'angular-typeahead': ['jquery','typeahead','angular'],
        'angularAMD': ['angular'],
        'angular-route': ['angular'],
        'blockUI': ['angular'],
        'angular-chart':['angular','chart'],
        'angular-sanitize': ['angular'],
        'ui-bootstrap': ['angular'],
		'angular-cookies': ['angular'], 
        'angular-ui-tree': ['angular'], 
         
    },
    // kick start application
    deps: ['config']
});

window.onerror = function(error, url, line) {
    alert(JSON.stringify({acc:'error', data:'ERR:'+error+' URL:'+url+' L:'+line}));
};