/******************************************************************************
 This is a demo page to experiment with binary tree based
 algorithms for packing blocks into a single 2 dimensional bin.
 See individual .js files for descriptions of each algorithm:
  * packer.js         - simple algorithm for a fixed width/height bin
  * packer.growing.js - complex algorithm that grows automatically
 TODO
 ====
  * step by step animated render to watch packing in action (and help debug)
  * optimization - mark branches as "full" to avoid walking them
  * optimization - dont bother with nodes that are less than some threshold w/h (2? 5?)
*******************************************************************************/

define(['config'], function (app) {
    app.register.service('drawerService', ['$rootScope', function ($rootScope) {
      var drawer = {
        init: function(canvas) {
          drawer.el={
            canvas:   canvas
          }
          drawer.el.draw = drawer.el.canvas.getContext("2d");
        },
        sort: {
          random  : function (a,b) { return Math.random() - 0.5; },
          w       : function (a,b) { return b.w - a.w; },
          h       : function (a,b) { return b.h - a.h; },
          a       : function (a,b) { return b.area - a.area; },
          max     : function (a,b) { return Math.max(b.w, b.h) - Math.max(a.w, a.h); },
          min     : function (a,b) { return Math.min(b.w, b.h) - Math.min(a.w, a.h); },

          height  : function (a,b) { return drawer.sort.msort(a, b, ['h', 'w']);               },
          width   : function (a,b) { return drawer.sort.msort(a, b, ['w', 'h']);               },
          area    : function (a,b) { return drawer.sort.msort(a, b, ['a', 'h', 'w']);          },
          maxside : function (a,b) { return drawer.sort.msort(a, b, ['max', 'min', 'h', 'w']); },

          msort: function(a, b, criteria) { /* sort by multiple criteria */
            var diff, n;
            for (n = 0 ; n < criteria.length ; n++) {
              diff = drawer.sort[criteria[n]](a,b);
              if (diff != 0)
                return diff;  
            }
            return 0;
          },

          now: function(blocks) {
            //var sort = drawer.el.sort.val();
            var sort = 'maxside';
            if (sort != 'none')
              blocks.sort(drawer.sort[sort]);
          }
        },

        //---------------------------------------------------------------------------
        run: function(root,blocks){
          drawer.canvas.reset(root.w,root.h);
          for(var i in blocks){
            var block =  blocks[i];
            if(block.fit){
               drawer.canvas.stroke(block.fit.x, block.fit.y, block.w, block.h);
               var tX = block.fit.x+(block.w/2);
               var tY = block.fit.y+(block.h/2);
               var txt =block.code;
               drawer.canvas.label(tX, tY, txt);
            }
          }
        },
        canvas: {

          reset: function(width, height) {
            drawer.el.canvas.width  = width  + 1; // add 1 because we draw boundaries offset by 0.5 in order to pixel align and get crisp boundaries
            drawer.el.canvas.height = height + 1; // (ditto)
            drawer.el.draw.clearRect(0, 0, drawer.el.canvas.width, drawer.el.canvas.height);
          },

          rect:  function(x, y, w, h, color='#fff') {
            drawer.el.draw.fillStyle = color;
            drawer.el.draw.fillRect(x + 0.5, y + 0.5, w, h);
          },

          stroke: function(x, y, w, h) {
            drawer.el.draw.strokeRect(x + 0.5, y + 0.5, w, h);
          },
          label:function(x,y,text){
            drawer.el.draw.fillText(text, x, y);
          },
          blocks: function(blocks) {
            var n, block;
            for (n = 0 ; n < blocks.length ; n++) {
              block = blocks[n];
              if (block.fit)
                drawer.canvas.rect(block.fit.x, block.fit.y, block.w, block.h, drawer.color(n));
            }
          },
          
          boundary: function(node) {
            if (node) {
              drawer.canvas.stroke(node.x, node.y, node.w, node.h);
              drawer.canvas.boundary(node.down);
              drawer.canvas.boundary(node.right);
            }
          }
        },


      }
      return drawer;
   }]);
});


