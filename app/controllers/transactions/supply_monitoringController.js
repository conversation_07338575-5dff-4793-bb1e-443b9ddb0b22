

define(['config','angular-chart','transactionService','transactionModalController','alertsService'], function (app) {

    app.register.controller('SupplyMonitoringController',['$modal','$scope',  '$rootScope','$timeout','transactionService', 'alertsService',function ($modal,$scope,$rootScope,$timeout,transactionService,alertsService) {
		const LIMIT = 10;
		const NAV_PAGE_COUNT = 10;
       $scope.initializeController = function () { 
			$scope.TransactionTypes = [
								{id:'orders',value:'Supply Order'},
							];
			$scope.Coverages = [
								{id:'today',value:'Today'},
								{id:'yesterday',value:'Yesterday'},
								{id:'7D',value:'Last 7 days'},
								//{id:'30D',value:'Last 30 days'},
								{id:'MON',value:'This Month'},
								{id:'custom',value:'Custom'},
							];
			initTransaction();
			initModal();
			$rootScope.$on('transactionCancelled',transactionCancelled);
			$rootScope.$on('getTransactions',function(){
				
				$scope.NewTransaction  = angular.copy($scope.FetchTransactionId);
				$scope.FetchTransactionId = null;
				$scope.movePage(1,$scope.SearchKeyword,$scope.FilterKeys);
				$timeout(function(){$scope.NewTransaction=null;},1500);
			});
			$scope.$watch('Transactions',function(newvalue){
				$scope.Fillers = [];
				if(newvalue.length<NAV_PAGE_COUNT){
					for(var i=1;i<=NAV_PAGE_COUNT - newvalue.length;i++){
						$scope.Fillers.push('');
					}
					if(newvalue.length==0) $scope.Fillers.splice(0,1);
				}
			});
	   }
	   $scope.setFilterKey = function(field,value){
			$scope.FilterKeys[field] = value;
		}
		$scope.cancelFilter = function(){
			$scope.FilterKeys = {type:'orders'};
			$scope.Transactions = [];
			$scope.Pages = [];
			$scope.movePage(1,null,$scope.FilterKeys);
			$scope.FilterEnabled = false;
			//$scope.toggleFilter();
		}
		$scope.confirmFilter = function(){
			$scope.Transactions = [];
			$scope.Pages = [];
			$scope.movePage(1,null,$scope.FilterKeys);
			$scope.FilterEnabled = false;
			//$scope.toggleFilter();
		}
		$scope.toggleFilter = function(){ 
			$scope.FilterEnabled = !$scope.FilterEnabled;
		}
		$scope.searchFor =function(keyword){
			$scope.Transactions = [];
			$scope.Pages = [];
			$scope.setFilterKey('coverage','ALL');
			$scope.SearchKeyword = angular.copy(keyword);
			$scope.movePage(1,$scope.SearchKeyword,$scope.FilterKeys);
		}
		$scope.resetSearch =function(){
			$scope.Pages = [];
			$scope.SearchEnabled = false;
			$scope.transactionSearchBox=null;
			$scope.SearchKeyword=null;
			$scope.Transactions = [];
			$scope.FetchProductId=null;
			$scope.movePage(1,$scope.SearchKeyword,$scope.FilterKeys);
		}
		$scope.transactionSearchFilter = function (transaction){
			var searchBox = $scope.transactionSearchBox;
			var keyword = new RegExp(searchBox, 'i');
			//console.log(transaction,searchBox,keyword);
			if(transaction.entity==undefined) transaction.entity = {name:null};
			transaction.Invoice = transaction.Invoice||{}; 
			transaction.PurchaseOrder = transaction.PurchaseOrder || {Invoice:{}}; 
			transaction.Customer = transaction.Customer || {}; 

			var test = keyword.test(transaction.entity.name);
				test =  test || keyword.test(transaction.PurchaseOrder.po_no);
				if(transaction.PurchaseOrder.Invoice)
					test =  test || keyword.test(transaction.PurchaseOrder.Invoice.si_no);
				test =  test || keyword.test(transaction.Invoice.si_no);

				if(transaction.Customer)
					test =  test || keyword.test(transaction.Customer.name);
			var type = transaction.type==$scope.FilterKeys.type;
			return (!searchBox || test) && type ; //Return NO FILTER or filter by patient_name
		}
	   $scope.movePage= function(__page,__keyword,__filter){
			$scope.SearchEnabled = __keyword? true:false;
			if(!__keyword) $scope.productSearchBox=null;
			if(__filter)
				__filter = customFilter(__filter);
			if(!__filter) $scope.FilterKeys={};
			if(__page>$scope.LastPage&&!__keyword && $scope.LastPage>0){
				$scope.GoToPage = $scope.LastPage;
				return alert('Oops! You can only go up to page '+$scope.LastPage);
			}
			$scope.CurrentPage = parseInt(__page);
			$scope.CurrentOffset = Math.ceil(__page/NAV_PAGE_COUNT) - 1;
			getTransactions(__page,__keyword,__filter,LIMIT);
			
		}
		$scope.exportData = function(__keyword,__filter){
			__filter = customFilter(__filter);

			$scope.ExportFilter = JSON.stringify(__filter);
			if($scope.FilterKeys.type=='orders'){
				var canvas = document.getElementById('bar');
				var imageData =  canvas.toDataURL('image/png');
				
				$scope.ExportImage = imageData;
				$scope.ExportTable =  JSON.stringify($scope.GraphDataTable);
			}
			$timeout(function(){
				var FORM = $scope.FilterKeys.type=='orders'?'POMonitoring':'SalesMonitoring';

				document.getElementById(FORM).submit();
			},100);
			
			//transactionService.exportData({keyword:__keyword,filter:__filter});
		}
		function initTransaction(){
			$scope.Pages = [];
			$scope.Transactions = [];
			$scope.FetchTransactionId=null;
			$scope.FilterKeys={};
			$scope.setFilterKey('type','orders');
			$scope.setFilterKey('coverage','today');
			$scope.confirmFilter();
			$scope.movePage(1,null,$scope.FilterKeys);
		}
		 function initModal(){
			var TransactionModalInstanceController = function ($scope, $modalInstance, transaction) {
				$scope.OptionalPayment=true;
				$scope.ReadOnly = true;
				//Confirm handler
				$scope.cancel = function (transactionId) {
					$rootScope.$broadcast('cancelTransaction');
					$modalInstance.close(transactionId);
				};
				$scope.serve = function (transactionId) {
					$scope.header.status = 'served';
					$rootScope.$broadcast('updateTransaction');
					$modalInstance.close(transactionId);
				}
				//Close handler
				$scope.close = function () {
				   $modalInstance.dismiss('cancel');
				};


				$scope.editPO = function(refno){
					window.location = '#/orders/purchase?po='+refno;
					$modalInstance.dismiss('cancel');
				}
				
				if(transaction){
					for(var $index in transaction){
						$scope[$index] = transaction[$index];
					}
				}
				
			};
			//openTransactionModal function
			$scope.openTransactionModal = function (__transaction) {

				$scope.FetchTransactionId  = __transaction.id;
				var transaction = {header:{},payments:[],details:[]};
				var today = new Date();
				var transac_date = __transaction.hasOwnProperty('timestamp')?__transaction.timestamp:today;
					today = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
					transac_date = transac_date.getFullYear()+'-'+(transac_date.getMonth()+1)+'-'+transac_date.getDate();
				
				var entity = __transaction.entity_type=='customer'?__transaction.Customer:__transaction.Supplier;
				transaction.header.id=__transaction.id;
				transaction.header.type=__transaction.type;
				transaction.header.ref_no=__transaction.ref_no;
				transaction.header.entity_type=__transaction.entity_type;
				transaction.header.entity=transaction.header[__transaction.entity_type] = entity;
				transaction.header.entity_id=__transaction.entity_id;
				transaction.header.date= transaction.header.transac_date = transac_date;
				transaction.header.amount=transaction.header.total = __transaction.amount;
				transaction.header.status =  __transaction.status;
				transaction.payments =  __transaction.TransactionPayment;
				transaction.details =  __transaction.TransactionDetail;
				transaction.cancellable = today == transac_date ||  __transaction.type =='po' ;
				transaction.orderObj =  __transaction.Order;
				transaction.servable =  __transaction.status=='invoiced' || __transaction.status=='served';
				if(transaction.servable){
					transaction.invoiceObj =  angular.copy(__transaction.PurchaseOrder.Invoice);
					transaction.invoiceObj.customer_id = transaction.header.entity_id;
					transaction.invoiceObj.old_ref_no = transaction.invoiceObj.si_no;
				}

				var modalInstance = $modal.open({
					templateUrl: 'createTransactionModalContent.html',
					controller: TransactionModalInstanceController,
					windowClass: 'app-modal-window',
					resolve:{
						 transaction: function() {return transaction;}
					}
				});
				modalInstance.result.then(function (data) {
					//console.log(data,'xx');
				}, function (data) {
					$scope.FetchTransactionId = null;
					//$log.info('Modal dismissed at: ' + new Date());
				});
			};
		}
	   function getTransactions(__page,__keyword,__filter,__limit){
		   $scope.LoadingTransactions = true;
		   __page = __page||$scope.CurrentPage;
		   console.log(__page);
		   
		   __filter.status='fulfilled';
		   if(__filter.type=='orders')
		   delete __filter.status;
			transactionService.getTransactions(successGetTransactions,errorGetTransactions,$rootScope,{sort:'oldest',page:__page,keyword:__keyword,limit:__limit,filter:__filter});
		}
		function transactionCancelled(evt,args){
			alertsService.RenderSuccessMessage('Transaction cancelled successfully!');
			var data = args.response.data;
			$scope.FetchTransactionId  = null;
			$scope.NewTransaction  = data.header.id;
			$.each($scope.Transactions,function(index,transaction){
				if(transaction.id==data.header.id){
					$scope.$apply(function(){
						$scope.Transactions[index]['status'] =  data.header.status;
						});
				}
			});
			$timeout(function(){$scope.NewTransaction=null;},1500);
		}
		function successGetTransactions($response, $status, $rootScope){
			$scope.Transactions = [];
			for(var index in $response.data){
				var transaction =  $response.data[index];
				switch(transaction.entity_type){
					case 'customer':
						transaction.entity = transaction.Customer;
					break;
					case 'supplier':
						transaction.entity = transaction.Supplier;
					break;
				}

				if($scope.FilterKeys.type=='po'){
					transaction.import =0;
					transaction.local =0;
					
					for(var j in transaction.TransactionDetail){
						var dtl = transaction.TransactionDetail[j];
						var amt =dtl.amount;
						if(transaction.discount>0)
							amt = amt * (100 - transaction.discount_percent) /100;
						if(dtl.Product.type=="LOC"){
							transaction.local +=amt;
						}
						if(dtl.Product.type=="IMP"){
							transaction.import +=amt;
						}
					}
					if(transaction.discount>0){
						var discountObj = {
							Discount:{
								percent: transaction.discount_percent,
								amount:transaction.discount
							}
						}
						transaction.amount = transaction.amount * (100 - transaction.discount_percent) /100
						transaction.TransactionDetail.push(discountObj);
					}
					
					
				}else if($scope.FilterKeys.type=='invoice'){
					transaction.cash=0;
					transaction.check=0;
					transaction.terms=0;
					transaction.cr='';
					for(var j in transaction.Invoice.InvoicePayment){
						var pay = transaction.Invoice.InvoicePayment[j];
						if(pay.payment_type=="CASH"){
							transaction.cash +=pay.amount;
						}
						
						if(pay.payment_type=="CHCK" ||pay.payment_type=="CHQE"){
							transaction.check +=pay.amount;
							transaction.cr =  pay.ref_no;
						}
						
						if(pay.payment_type=="CHRG")
							transaction.terms +=pay.amount;
						
					}
				}
				transaction.created = new Date(transaction.created);
				transaction.timestamp = new Date(transaction.timestamp);
				transaction.backlogged = +transaction.timestamp !== +transaction.created;
				
				$scope.Transactions.push(transaction);
			}
			console.log(transaction);
			$scope.LoadingTransactions = false; 
			$scope.GoToPage = null;
			paginate($response.meta.pages);
			console.log($response);
			
			if(!$response.meta.prev){
				$scope.GraphData = null;
				var prodFilter =  angular.copy($scope.FilterKeys);
					delete prodFilter.type;
					delete prodFilter.status;
				transactionService.getProductMovements(successGetProdMoves,errorGetProdMoves,$scope,{filter:prodFilter,dashboard:'month'});
			}
		}
		function errorGetTransactions($response, $status, $scope){ }
		function successGetProdMoves($response, $status, $scope){
			$scope.GraphData =  $response.data;
			$scope.GraphShow='T10';
			$scope.GraphTotalArea = 0;
		   	card = buildGraph($scope.GraphData ,10);
		   	$scope.Graph = card;
		}
		$scope.toggleGraph = function(){
			$scope.GraphShow = $scope.GraphShow=='ALL'?'T10':'ALL'; 
			$scope.GraphTotalArea = 0;
			if($scope.GraphShow=='ALL')
				card = buildGraph($scope.GraphData);
		   	else
		   		card = buildGraph($scope.GraphData,10);
		   	$scope.Graph = card;

		}
		function buildGraph($data,limit){
			var card,__labels,__data,__total, __items;
		   	card = {};
		   	__labels = [];
		   	__items = [];
		   	__data =[];
		   	__total = 0;
		   	$scope.GraphDataTable = [];
			if($data.length) {
				if( $data[0].code){
				   	for(var i in $data){
				   		if(i>limit-1 && limit) break;
				   		var datum =  $data[i];
				   		__labels.push(datum.code);
				   		__items.push(datum.item);
				   		__data.push(datum.amount);
				   		__total+=datum.amount;

				   		tblObj = {};
				   		tblObj.code =  datum.code;
				   		tblObj.item =  datum.item;
				   		tblObj.amount =  datum.amount;
				   		$scope.GraphDataTable.push(tblObj);

				   	}
			    }
		   	}
			if(__data.length){
		   		card.labels =  __labels;
		   		card.items =  __items;
		   		card.data =  [__data];
		   		card.total = __total;
		   		card.average = __total/__data.length;
		   	}else{
		   		card.labels =  ["NO DATA"];
		   		card.data =  [[0]];
		   		card.average = 0;
		   	}
		   	$scope.GraphTotalArea = __total;

		 
		   	return card;
		}
		function errorGetProdMoves($response, $status, $scope){ }
		function paginate(__pages){
			$scope.EnablePagination = true;
			if($scope.Pages.length==0){
				for(var i=1;i<=__pages;i++){
					$scope.Pages.push(i);
				}
				$scope.LastPage = $scope.Pages.length;
			}
			updatePages();
		}
		function updatePages(){
			$scope.ActivePages=[];
			for(var i=$scope.CurrentOffset*NAV_PAGE_COUNT,ctr=1;i<$scope.Pages.length&&ctr<=NAV_PAGE_COUNT;i++,ctr++){
				$scope.ActivePages.push($scope.Pages[i]);
			}
		}
		function customFilter(__filter){
			if(__filter.coverage=='custom'){
				var __f =  angular.copy(__filter);
				delete __f.coverage;
				var Datefrom =  new Date($scope.CustomDate.from);
				var Dateto =  new Date($scope.CustomDate.to);
				__f.from =  Datefrom.getFullYear()+'-'+(Datefrom.getMonth()+1)+'-'+Datefrom.getDate();
				__f.to =  Dateto.getFullYear()+'-'+(Dateto.getMonth()+1)+'-'+Dateto.getDate();
				__filter = __f;
			}
			return __filter;
		}
    }]);
});


