

define(['config','transactionModalController','cutPlannerModalController','transactionService','customerService','purchaseOrderService'], function (app,jquery) {

    app.register.controller('JobOrderController',['$modal','$scope', '$timeout', '$rootScope','transactionService','customerService','purchaseOrderService', function ($modal,$scope,$timeout,$rootScope,transactionService,customerService,purchaseOrderService) {
		const DISCOUNT_LIMIT = 0.50;
		var __buffer;
		var __page;
		var __filter;
		var __limit;
       $scope.initializeController = function () {
			initJO();
			$rootScope.moduleName = 'JO';
			$scope.TransactionsLKUP = {};
			$rootScope.AllowAddTemporaryProduct = false;
			$scope.typeAheadOptions = {
				highlight: true
			 };
			 $scope.typeAheadData = {
				displayKey: 'name',
				source: {},
			  };
			__buffer = [];
			__page = 1;
			__limit = 250;
			__filter = {"type":"po","status":"created"};
			transactionService.getTransactions(successGetTransactions,errorGetTransactions,$rootScope,{sort:'oldest',page:__page,limit:__limit,filter:__filter});
			initModal();
			$scope.$on('productAdded',productAdded);
	   }

	   $scope.$watch('ActivePO',function(trnxId){
	   		var trnx = angular.copy($scope.TransactionsLKUP[trnxId]||{items:[]});
	   		$scope.JOItems =  trnx.items;
	   		$scope.CustomerObject =  trnx.customer;
	   		$scope.POObj = {
	   				po_id:trnx.purchase_order_id,
	   				po_no:trnx.po_no,
	   				po_date:trnx.po_date,
	   		}; 
	   		$scope.toggleEdit('save');
	   });
	   $scope.resetCustomerObject = function(){
		   $scope.CustomerObject = null;
		   $scope.CustomerObjectTypeAhead = null;
	   }
	   function productAdded(evt,args){
			var data = args.response.data;
			console.log(data);
		}
	   $scope.$on('addItem',function(event,data){
			$scope.JOTotalItems += data.quantity;
			$scope.JOItems.push(data);
	   });
	   $scope.resetJO = function(){
		  if(confirm('Are you sure you want to cancel this transaction?')){
			  initJO();
		  }
	   }

	  
	   $scope.validateCustomer = function(customer){
		   if(customer){
			if(customer.id==undefined){
				alert('Invalid customer');
				$scope.resetCustomerObject();
			}
		   }else{
			   $scope.FocusTypeahead = true;
			   $scope.resetCustomerObject();
		   }
	   }
	  
	   $scope.$on('resetTransaction',function(){
		   initJO();
		   transactionService.getTransactions(successGetTransactions,errorGetTransactions,$rootScope,{page:__page,limit:__limit,filter:__filter});
	   });
	   $scope.toggleEdit = function($mode){
		   switch($mode){
				case 'edit': $scope.editMode =  true; break;
				case 'save': 
					$scope.editMode =  false; 
					var totalItems = 0;
					var items = $scope.JOItems;
					for(var index in items){
						var item = items[index];

						totalItems += item.quantity_actual;
						item.quantity_area_actual = item.length_actual * item.width_actual * item.quantity_actual;
						if(item.code_actual) 
							item.code_actual =  item.code_actual.toUpperCase();						items[index] = item;
					}
					$scope.JOItems = items;
					$scope.JOTotalItems = totalItems;
				break;
		   }
	   }
	   $scope.toggleDelete = function($mode){
		   switch($mode){
				case 'delete': $scope.deleteMode =  true; break;
				case 'save': 
					$scope.deleteMode =  false; 
					var totalItems = 0;
					var items = $scope.JOItems;
					var cache =[];
					for(var index in items){
						var item = items[index];
						if(!item.is_delete){
							cache.push(item);
							totalItems += item.quantity_actual;
						}
					}
					$scope.JOItems = cache;
					$scope.JOTotalItems = totalItems;
				break;
		   }
	   }
	   function initJO(){
			$timeout(function(){
				$scope.CustomerObject = '';
				$scope.CustomerObjectTypeahead  = '';
				$scope.JOItems = [];
				$scope.JOTotalItems = 0;
				$scope.POObj = {};
				$scope.CutCalculated = false;
				}, 100);
	   }
	   function initModal(){
			var TransactionModalInstanceController = function ($scope, $modalInstance, transaction) {
				$scope.OptionalPayment = true;
				$scope.OptionalReceipt = true;
				//Confirm handler
				$scope.confirm = function (transactionId) {
					$rootScope.$broadcast(transactionId?'updateTransaction':'addTransaction');
					$modalInstance.close(transactionId);
				};
				//Cancel handler
				$scope.cancel = function () {
				   $modalInstance.dismiss('cancel');
				};
				
				if(transaction){
					for(var $index in transaction){
						$scope[$index] = transaction[$index];
					}
				}
				
			};

			var CutPlannerModalInstanceController = function ($scope, $modalInstance, jobOrder) {
				$scope.jobOrder =  jobOrder;
				//Confirm handler
				$scope.confirm = function (cutPlan) {
					
					$modalInstance.close(cutPlan);
				};
				//Cancel handler
				$scope.cancel = function () {
				   $modalInstance.dismiss('cancel');
				};
				
				
				
			};
			//openTransactionModal function
			$scope.openTransactionModal = function () {
				
				var transaction = {header:{},details:[]};
				var job_date = new Date();
					job_date = job_date.getFullYear()+'-'+(job_date.getMonth()+1)+'-'+job_date.getDate();
				transaction.header.type='jo';
				transaction.header.entity_type='customer';
				transaction.header.entity=transaction.header.customer=$scope.CustomerObject;
				transaction.header.entity_id=$scope.CustomerObject.id;
				transaction.header.po_no=$scope.POObj.po_no;
				transaction.header.po_date=$scope.POObj.po_date;
				transaction.header.purchase_order_id=$scope.POObj.po_id;
				transaction.header.po_trnx_id=$scope.ActivePO;
				transaction.header.date=transaction.header.jo_date=job_date;
				transaction.header.amount=transaction.header.total=$scope.JOTotalItems;
				transaction.header.status = 'created';
				transaction.header.total_allowed_discount = 0;
				var JOItems =  angular.copy($scope.JOItems);
				$.each(JOItems,function(i,o){
					o.quantity = o.quantity_area_actual;
					var detail = {product_id:o.id,
									length:o.length,
									width:o.width,
									quantity:o.quantity,
									quantity_area:o.quantity_area,

									length_actual:o.length_actual,
									width_actual:o.width_actual,
									quantity_actual:o.quantity_actual,
									quantity_area_actual:o.quantity_area_actual,
									code_actual:o.code_actual,

									po_price:o.po_price
								};
					
					if(o.discountable){
						transaction.header.total_allowed_discount  += (o.markup*DISCOUNT_LIMIT);
					}
					transaction.details.push(detail);
				});
				var modalInstance = $modal.open({
					templateUrl: 'createTransactionModalContent.html',
					controller: TransactionModalInstanceController,
					windowClass: 'app-modal-window',
					resolve:{
						 transaction: function() {return transaction;}
					}
				});
				modalInstance.result.then(function (data) {
					//console.log(data,'xx');
				}, function (data) {
					console.log(data);
					//$log.info('Modal dismissed at: ' + new Date());
				});
			};

			//openCalculateModal
			$scope.calculateCut = function(){
				var jobOrder =  angular.copy($scope.JOItems);
				var modalInstance = $modal.open({
						templateUrl: 'cutPlannerModalContent.html',
						controller: CutPlannerModalInstanceController,
						windowClass: 'app-modal-window',
						resolve:{
							 jobOrder: function() {return jobOrder;}
						}
					});
					modalInstance.result.then(function (cutPlan) {
						console.log(cutPlan);
						$scope.CutCalculated = true;
					}, function (data) {
						console.log(data);
						//$log.info('Modal dismissed at: ' + new Date());
					});
			}
		}
		function successGetTransactions($response, $status, $rootScope){
			$scope.Transactions = [];
			$scope.TransactionsLKUP = {};
			for(var i in $response.data){
				var tObj =  $response.data[i];
				var trnx = {};
					trnx.id = tObj.id;
					trnx.po_no =  tObj.PurchaseOrder.po_no; 
					trnx.po_date =  tObj.PurchaseOrder.po_date; 
					trnx.purchase_order_id =  tObj.PurchaseOrder.id; 
					trnx.customer =  tObj.Customer; 
					trnx.items = [];
				// Map items POD vs TRD
					var POD  =tObj.PurchaseOrder.PurchaseOrderDetail;
					var TRD  =tObj.TransactionDetail;
					var CUS  =tObj.Customer;
					
					for(var j in POD){
						var PRD = TRD[j].Product;
						var ORD = POD[j];
						var item = {};
							item.customer = CUS;
							item.id =  PRD.id;
							item.description =  PRD.display_title;
							item.particular =  PRD.particular;
							item.category_id =  PRD.category_id;
							item.thickness =  PRD.thickness;
							item.allowance =  PRD.allowance;
							item.unit =  PRD.unit;

							//Purchase Order
							item.length =  ORD.length;
							item.width =  ORD.width;
							item.quantity =  ORD.quantity;
							item.quantity_area =  ORD.quantity_area;
							
							if(ORD.stocks.length)
								item.stocks = ORD.stocks;
								
							//Actual
							item.length_actual =  ORD.length;
							item.width_actual =  ORD.width;
							item.quantity_actual =  ORD.quantity;
							item.quantity_area_actual =  ORD.quantity_area;
							item.po_price =  ORD.price;

						trnx.items.push(item);
					}


				$scope.Transactions.push(trnx);
				$scope.TransactionsLKUP[trnx.id] =  trnx;
			}
		}
		function errorGetTransactions($response, $status, $scope){ }


    }]);
});


