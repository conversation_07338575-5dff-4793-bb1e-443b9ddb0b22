

define(['config','productModalController','productTypeaheadController','transactionModalController','transactionService','customerService','alertsService'], function (app,jquery) {

    app.register.controller('PurchaseOrderController',['$modal','$scope', '$timeout', '$rootScope','$location','transactionService','customerService','alertsService', function ($modal,$scope,$timeout,$rootScope,$location,transactionService,customerService,alertsService) {
		const DISCOUNT_LIMIT = 0.50;
		var __buffer;
		var __page;
       $scope.initializeController = function () {
			initPO();
			$rootScope.moduleName = 'PO';
			$rootScope.AllowAddTemporaryProduct = false;
			$scope.typeAheadOptions = {
				highlight: true
			 };
			 $scope.typeAheadData = {
				displayKey: 'name',
				source: {},
			  };
			__buffer = [];
			__page = 1;
			customerService.getCustomers(successGetCustomers,errorGetCustomers,$scope,{page:__page});
			initModal();
			$scope.$on('productAdded',productAdded);
			var link = $location.$$search;
			if(link.po){
				loadPOObj(link.po);
			}

	   }
	   $scope.resetCustomerObject = function(){
		   $scope.CustomerObject = null;
		   $scope.CustomerObjectTypeAhead = null;
	   }
	   function productAdded(evt,args){
			var data = args.response.data;
			console.log(data);
		}
	   $scope.$on('addItem',function(event,data){
			data.amount = data.price * data.quantity;
			$scope.POAmount += data.amount;
			$scope.POItems.push(data);
	   });
	   $scope.resetPO = function(){
		  if(confirm('Are you sure you want to cancel this transaction?')){
			  initPO();
		  }
	   }

	   $scope.confirmPOEntry = function(){
	   		var PONO =  angular.copy($scope.PONo);
	   		$scope.PONumber =  PONO;
	   		$scope.PONo = null;
	   		$scope.openPODD = false;
	   }
	   $scope.cancelPOEntry =  function(){
	   		$scope.PONo = null;
	   		$scope.openPODD = false;
	   }
	   $scope.validateCustomer = function(customer){
		   if(customer){
			if(customer.id==undefined){
				alert('Invalid customer');
				$scope.resetCustomerObject();
			}
		   }else{
			   $scope.FocusTypeahead = true;
			   $scope.resetCustomerObject();
		   }
	   }
	   $scope.$on('typeahead:selected',function(event,data){
		   if(data.hasOwnProperty('last_bill'))
				$scope.CustomerObject = data;
	   });
	   $scope.$on('resetTransaction',function(evt,$response){
		   initPO();
		   if($response.data){
		   		$scope.TrnxID = $response.data.id;
		   		$scope.TrnxToken = $response.data.token;

		   		if($response.data.header.ref_no)
		   		$timeout(function(){
		   			document.getElementById('POReports').submit();	

		   			var link = $location.$$search;
		   			if(link.po)
		   				window.location = '#/orders/purchase';
		   		},500);
		   }
	   });
	   $scope.toggleEdit = function($mode){
		   switch($mode){
				case 'edit': $scope.editMode =  true; break;
				case 'save': 
					$scope.editMode =  false; 
					var totalAmount = 0;
					var items = $scope.POItems;
					for(var index in items){
						var item = items[index];
						item.amount = item.price * item.quantity ;
						totalAmount += item.amount;
						items[index] = item;
					}
					$scope.POItems = items;
					$scope.POAmount = totalAmount;
				break;
		   }
	   }

	   $scope.toggleSort = function($mode){
		   switch($mode){
				case 'sort': 
					$scope.sortMode =  true; 
					$scope.POSortItems =  angular.copy($scope.POItems);
				break;
				case 'save': 
					$scope.sortMode =  false; 
					$scope.POItems =  angular.copy($scope.POSortItems);
				break;
		   }
	   }
	   $scope.toggleDelete = function($mode){
		   switch($mode){
				case 'delete': $scope.deleteMode =  true; break;
				case 'save': 
					$scope.deleteMode =  false; 
					var totalAmount = 0;
					var items = $scope.POItems;
					var cache =[];
					for(var index in items){
						var item = items[index];
						if(!item.is_delete){
							cache.push(item);
							totalAmount += item.amount;
						}
					}
					$scope.POItems = cache;
					$scope.POAmount = totalAmount;
				break;
		   }
	   }
	   function initPO(){
			$timeout(function(){
				$scope.POToken = null;
				$scope.CustomerObject = '';
				$scope.CustomerObjectTypeahead  = '';
				$scope.POItems = [];
				$scope.POSortItems = [];
				$scope.POAmount = 0;
				$scope.PONumber = '';
				}, 100);
	   }
	   function initModal(){
			var TransactionModalInstanceController = function ($scope, $modalInstance, transaction) {
				$scope.SetupModal=true;
   				$scope.payCash = false;
   				$scope.payCharge = true;
   				$scope.ActiveTransactionTab = 'payment';
   				$scope.OptionalReceipt = true;
				//Confirm handler
				$scope.confirm = function (transactionId) {
					$rootScope.$broadcast(transactionId?'updateTransaction':'addTransaction');
					$modalInstance.close(transactionId);
				};
				//Cancel handler
				$scope.cancel = function () {
				   $modalInstance.dismiss('cancel');
				};
				
				if(transaction){
					for(var $index in transaction){
						$scope[$index] = transaction[$index];
					}
				}
				
			};
			//openTransactionModal function
			$scope.openTransactionModal = function () {
				var transaction = {header:{},details:[]};
				var purchase_date = new Date();
					purchase_date = purchase_date.getFullYear()+'-'+(purchase_date.getMonth()+1)+'-'+purchase_date.getDate();
				if($scope.POToken)
					transaction.header.token =  $scope.POToken;
				transaction.header.type='po';
				transaction.header.entity_type='customer';
				transaction.header.entity=transaction.header.customer=$scope.CustomerObject;
				transaction.header.entity_id=$scope.CustomerObject.id;
				transaction.header.po_no=$scope.PONumber;
				transaction.header.date=transaction.header.po_date=purchase_date;
				transaction.header.date=transaction.header.po_date=purchase_date;
				transaction.header.amount=transaction.header.total=$scope.POAmount;
				transaction.header.status = 'created';
				transaction.header.total_allowed_discount = 0;
				$.each($scope.POItems,function(i,o){
					o.quantity_area = o.quantity*o.area;
					var detail = {product_id:o.id,length:o.length,width:o.width,thickness:o.thickness,quantity:o.quantity,quantity_area:o.quantity_area,price:o.price,amount:o.amount};
					
					if(o.discountable){
						transaction.header.total_allowed_discount  += (o.markup*DISCOUNT_LIMIT);
					}
					transaction.details.push(detail);
				});
				var modalInstance = $modal.open({
					templateUrl: 'createTransactionModalContent.html',
					controller: TransactionModalInstanceController,
					windowClass: 'app-modal-window',
					resolve:{
						 transaction: function() {return transaction;}
					}
				});
				modalInstance.result.then(function (data) {
					//console.log(data,'xx');
				}, function (data) {
					console.log(data);
					//$log.info('Modal dismissed at: ' + new Date());
				});
			};
		}
		function successGetCustomers($response,$status,$scope){
				var customers = [];
				$response.data.map(function(cObj){
					if(cObj.status=='open')
						customers.push(cObj);
				});
			 __buffer =  __buffer.concat(customers);
				$rootScope.__Progress = (__page / $response.meta.pages) *100;
				if($response.meta.next){
					__page =  __page + 1;
					return customerService.getCustomers(successGetCustomers,errorGetCustomers,$scope,{page:__page});
				}else{
					$timeout(function(){
					//$rootScope.__Progress = 0;
					},1500);
				}
		   var customers = new Bloodhound({
				datumTokenizer: function(d) { return Bloodhound.tokenizers.whitespace(d.name); },
				queryTokenizer: Bloodhound.tokenizers.whitespace,
				local: __buffer,
			  });

			  // initialize the bloodhound suggestion engine
			  customers.initialize();

			  // Allows the addition of local datum
			  // values to a pre-existing bloodhound engine.
			  $scope.addValue = function () {
				customers.add({
				  name: 'twenty'
				});
			  };

			  // Typeahead options object
			  $scope.typeAheadOptions = {
				highlight: true
			  };

			  // Single dataset example
			  $scope.typeAheadData = {
				displayKey: 'name',
				source: customers.ttAdapter()
			  };
			  if(!$scope.POToken)
			  $scope.CustomerObject = null;
	   }
	   function errorGetCustomers($response,$status,$scope){}

	   function loadPOObj(refNo){

	   	var __filter = {type:"po",id:refNo};
		var successGetPO = function($response,$status,$rS){
			var data = $response.data[0];
			var CObj =  data.Customer;
			var PObj =  data.PurchaseOrder;
			var TObj =  data.TransactionDetail;
			$scope.POToken =  refNo;
			$scope.CustomerObject =  CObj;
			$scope.CustomerObjectTypeAhead =  CObj.name;
			$scope.PONumber = PObj.po_no;
			$scope.PONo = PObj.po_no;
			var POItems = [];
			PObj.PurchaseOrderDetail.map(function(item,index){
				console.log(item);
				if(TObj[index].product_id==item.product_id){
					var P = angular.copy(TObj[index].Product);
					P.length =  item.length;
					P.width =  item.width;
					P.quantity =  item.quantity;
					P.price =  item.price;
					P.amount =  item.amount;
					POItems.push(P);
				}
			});
			$scope.POItems =  POItems;
			$scope.toggleEdit('save');
		}
		var errorGetPO = function ($response,$status,$scope){}
		alertsService.RenderSuccessMessage('PO ready for edit. Please wait to load');
		transactionService.getTransactions(successGetPO,errorGetPO,$rootScope,{limit:1,filter:__filter});
	   }
    }]);
});


