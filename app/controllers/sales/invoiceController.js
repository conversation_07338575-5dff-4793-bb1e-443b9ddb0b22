

define(['config','productModalController','productTypeaheadController','transactionModalController','transactionService','customerService','purchaseOrderService'], function (app,jquery) {

    app.register.controller('SalesInvoiceInvoiceController',['$modal','$scope', '$timeout', '$rootScope','transactionService','customerService', 'purchaseOrderService',function ($modal,$scope,$timeout,$rootScope,transactionService,customerService,purchaseOrderService) {
		const DISCOUNT_LIMIT = 0.50;
		var __buffer;
		var __page;
		var __filter;
		var __limit;
       $scope.initializeController = function () {
			initSalesInvoice();
			$rootScope.moduleName = 'Sales Invoice';
			$scope.TransactionsLKUP = {};
			$rootScope.AllowAddTemporaryProduct = false;
			$scope.typeAheadOptions = {
				highlight: true
			 };
			 $scope.typeAheadData = {
				displayKey: 'name',
				source: {},
			  };
			__buffer = [];
			__page = 1;
			__limit = 999;
			__filter = {"type":"jo","status":"created"};
			$scope.CustomersLK = {};
			transactionService.getTransactions(successGetTransactions,errorGetTransactions,$rootScope,{sort:'oldest',page:__page,limit:__limit,filter:__filter});
			customerService.getCustomers(successGetCustomers,errorGetCustomers,$scope,{page:__page});
			initModal();
			$scope.$on('productAdded',productAdded);
	   }
	   $scope.$watch('ActivePO',function(trnxId){
	   		var trnx = angular.copy($scope.TransactionsLKUP[trnxId]||{items:[]});
	   		console.log(trnx,'ACTRI');
	   		$scope.SalesInvoiceItems =  trnx.items;
	   		$scope.CustomerObject =  trnx.customer;
	   		$scope.POObj = {
	   				po_id:trnx.purchase_order_id,
	   				po_no:trnx.po_no,
	   				po_date:trnx.po_date,
	   				discount:trnx.discount,
	   				discount_percent:trnx.discount_percent,
	   		}; 
	   		$scope.toggleEdit('save');
	   });
	   $scope.resetCustomerObject = function(){
		   $scope.CustomerObject = null;
		   $scope.CustomerObjectTypeAhead = null;
	   }
	   function productAdded(evt,args){
			var data = args.response.data;
			console.log(data);
		}
	   $scope.$on('addItem',function(event,data){
			data.amount = data.price * data.quantity;
			$scope.SalesInvoiceAmount += data.amount;
			$scope.SalesInvoiceItems.push(data);
	   });
	   $scope.resetSalesInvoice = function(){
		  if(confirm('Are you sure you want to cancel this transaction?')){
			  initSalesInvoice();
		  }
	   }
	   $scope.validateCustomer = function(customer){
		   if(customer){
			if(customer.id==undefined){
				alert('Invalid customer');
				$scope.resetCustomerObject();
			}
		   }else{
			   $scope.FocusTypeahead = true;
			   $scope.resetCustomerObject();
		   }
	   }
	   $scope.$on('typeahead:selected',function(event,data){
		   if(data.hasOwnProperty('last_bill'))
				$scope.CustomerObject = data;
	   });
	   $scope.$on('resetTransaction',function(evt,$response){
		   initSalesInvoice();
		   transactionService.getTransactions(successGetTransactions,errorGetTransactions,$rootScope,{sort:'oldest',page:__page,limit:__limit,filter:__filter});
		  	if($response.data){
		  		console.log($response.data);
		   		$scope.TrnxID = $response.data.ids;
		   		$scope.TrnxToken = $response.data.token;


		   		// Print SI
		   		if($response.data.header.si_no)
		   		$timeout(function(){
		   			document.getElementById('InvoiceReports').submit();	
		   		},500);
		   		
		   		// Print DR
		   		if($response.data.header.dr_no)
		   		$timeout(function(){
		   			document.getElementById('DeliveryReports').submit();	
		   		},1000);

		   		// Print CR
		   		if($response.data.header.cr_no)
		   		$timeout(function(){
		   			document.getElementById('CollectReports').submit();	
		   		},1500);

				$timeout(function(){
		   			$scope.TrnxID = null;
		   			$scope.TrnxToken = null;
		   		},3000);		   	
			}
	   });
	   $scope.toggleEdit = function($mode){
		   switch($mode){
				case 'edit': $scope.editMode =  true; break;
				case 'save': 
					$scope.editMode =  false; 
					var totalAmount = 0;
					var items = $scope.SalesInvoiceItems;
					for(var index in items){
						var item = items[index];
						item.amount = item.price * item.quantity ;
						totalAmount += item.amount;
						items[index] = item;
					}
					$scope.SalesInvoiceItems = items;
					$scope.SalesInvoiceAmount = totalAmount;
				break;
		   }
	   }
	   $scope.toggleDelete = function($mode){
		   switch($mode){
				case 'delete': $scope.deleteMode =  true; break;
				case 'save': 
					$scope.deleteMode =  false; 
					var totalAmount = 0;
					var items = $scope.SalesInvoiceItems;
					var cache =[];
					for(var index in items){
						var item = items[index];
						if(!item.is_delete){
							cache.push(item);
							totalAmount += item.amount;
						}
					}
					$scope.SalesInvoiceItems = cache;
					$scope.SalesInvoiceAmount = totalAmount;
				break;
		   }
	   }
	   function initSalesInvoice(){
			$timeout(function(){
				$scope.CustomerObject = '';
				$scope.CustomerObjectTypeahead  = '';
				$scope.SalesInvoiceItems = [];
				$scope.SalesInvoiceAmount = 0;
				$scope.POObj = {};
				}, 100);
	   }
	   function initModal(){

			
			
			var TransactionModalInstanceController = function ($scope, $modalInstance, transaction) {
				$scope.SetupModal=true;
				$scope.payCash = transaction.payCash || false;
				$scope.payCharge = transaction.payCharge || false;
				$scope.ChargeDetails = transaction.ChargeDetails || null;
				$scope.Tax = transaction.header.tax || 0;
				
				$scope.DiscountPercent =  transaction.header.discount_percent ||0;
				$scope.Discount =  transaction.header.discount || 0;
				
				$scope.OptionalReceipt =false;
				
				$scope.ActiveTransactionTab = 'payment';
				if($scope.payCash || $scope.payCharge){
					$scope.ActiveTransactionTab =  'receipt';
				}
				//Confirm handler
				$scope.confirm = function (transactionId) {
					$rootScope.$broadcast(transactionId?'updateTransaction':'addTransaction');
					$modalInstance.close(transactionId);
				};
				//Cancel handler
				$scope.cancel = function () {
				   $modalInstance.dismiss('cancel');
				};
				
				if(transaction){
					for(var $index in transaction){
						$scope[$index] = transaction[$index];
					}
				}
				
			};
			//openTransactionModal function
			$scope.openTransactionModal = function () {

				var transaction = {header:{},details:[]};
				var invoice_date = new Date();
					invoice_date = invoice_date.getFullYear()+'-'+(invoice_date.getMonth()+1)+'-'+invoice_date.getDate();
				transaction.header.type='invoice';
				transaction.header.entity_type='customer';
				transaction.header.entity=transaction.header.customer=$scope.CustomerObject;
				transaction.header.entity_id=$scope.CustomerObject.id;
				transaction.header.po_no=$scope.POObj.po_no;
				transaction.header.po_date=$scope.POObj.po_date;
				transaction.header.purchase_order_id=$scope.POObj.po_id;
				transaction.header.jo_trnx_id=$scope.ActivePO;
				transaction.header.date=transaction.header.invoice_date=invoice_date;
				transaction.header.amount=transaction.header.total=$scope.SalesInvoiceAmount;
				transaction.header.status = 'fulfilled';
				transaction.header.total_allowed_discount = 0;
				transaction.header.discount = $scope.POObj.discount;
				transaction.header.discount_percent = $scope.POObj.discount_percent;
				
				var SalesInvoiceItems = angular.copy($scope.SalesInvoiceItems);
				$.each(SalesInvoiceItems,function(i,o){
					var detail = {product_id:o.id,description:o.description,quantity:o.quantity,price:o.price,amount:o.amount};
					
					if(o.discountable){
						transaction.header.total_allowed_discount  += (o.markup*DISCOUNT_LIMIT);
					}
					transaction.details.push(detail);
				});


				var __po_filter = {purchase_order_id:$scope.POId};
				// Implement TAX

				if(cid =$scope.CustomerObject.id){
					var customerInfo = $scope.CustomersLK[cid];
					var tax_type = customerInfo.tax_type;
					transaction.header.tax_type =  customerInfo.tax_type;
					
				}

				purchaseOrderService.getTerms(successGetTerms,errorGetTerms,$rootScope,{page:1,limit:1,filter:__po_filter});
				
				function successGetTerms($response,$status,$scope){
					if($response.data){
						var terms =  $response.data;
						for(var j in terms){
							var t =  terms[j];
							switch(t.payment_type){
								case 'CHRG':
									transaction.payCharge = true;
									transaction.ChargeDetails = t.detail;
								break;
								case 'CASH':
									transaction.payCash = false;
								break;
							}
						}
					}
					var modalInstance = $modal.open({
						templateUrl: 'createTransactionModalContent.html',
						controller: TransactionModalInstanceController,
						windowClass: 'app-modal-window',
						resolve:{
							 transaction: function() {return transaction;}
						}
					});
					modalInstance.result.then(function (data) {
						//console.log(data,'xx');
					}, function (data) {
						console.log(data);
						//$log.info('Modal dismissed at: ' + new Date());
					});
				}

				function errorGetTerms($response,$status,$scope){
					var modalInstance = $modal.open({
						templateUrl: 'createTransactionModalContent.html',
						controller: TransactionModalInstanceController,
						windowClass: 'app-modal-window',
						resolve:{
							 transaction: function() {return transaction;}
						}
					});
					modalInstance.result.then(function (data) {
						//console.log(data,'xx');
					}, function (data) {
						console.log(data);
						//$log.info('Modal dismissed at: ' + new Date());
					});
				}
				
			};
		}
		function successGetCustomers($response,$status,$scope){
			 __buffer =  __buffer.concat($response.data);
				$rootScope.__Progress = (__page / $response.meta.pages) *100;
				if($response.meta.next){
					__page =  __page + 1;
					return customerService.getCustomers(successGetCustomers,errorGetCustomers,$scope,{page:__page});
				}else{
					$timeout(function(){
					//$rootScope.__Progress = 0;
					console.log(__buffer);
					for(var j in __buffer){
						var customer =  __buffer[j];
						$scope.CustomersLK[customer.id] = customer;
					}
					},1500);
				}
		  
			  $scope.CustomerObject = null;
	   }
	   function errorGetCustomers($response,$status,$scope){}

	   function successGetTransactions($response, $status, $rootScope){
			$scope.Transactions = [];
			$scope.TransactionsLKUP = {};
			for(var i in $response.data){
				var tObj =  $response.data[i];
				var trnx = {};
					trnx.id = tObj.id;
					trnx.po_no =  tObj.JobOrder.po_no; 
					trnx.po_date =  tObj.JobOrder.po_date; 
					trnx.purchase_order_id =  tObj.JobOrder.purchase_order_id; 
					trnx.discount =  tObj.discount; 
					trnx.discount_percent =  tObj.discount_percent; 
					trnx.customer =  tObj.Customer; 
					trnx.items = [];
				// Map items JOD vs TRD
					var JOD  =tObj.JobOrder.JobOrderDetail;
					var TRD  =tObj.TransactionDetail;
					var CUS  =tObj.Customer;
					
					for(var j in JOD){
						var PRD = TRD[j].Product;
						var ORD = JOD[j];
						var item = {};
							item.customer = CUS;
							item.id =  PRD.id;
							item.particular =  PRD.particular;
							item.description =  PRD.description;
							item.category_id =  PRD.category_id;
							item.thickness =  PRD.thickness;
							item.allowance =  PRD.allowance;
							item.unit =  PRD.unit;

							//Purchase Order
							item.length =  ORD.length;
							item.width =  ORD.width;
							item.quantity =  ORD.quantity;
							item.quantity_area =  ORD.quantity_area;
							item.price =  ORD.po_price;
							
							//Actual
							item.length_actual =  ORD.length_actual;
							item.width_actual =  ORD.width_actual;
							item.quantity_actual =  ORD.quantity_actual;
							item.quantity_area_actual =  ORD.quantity_area_actual;

							item.description = item.description;
							if(item.part_no)
								item.description += ' ' +item.part_no;
							item.description += ' ' +item.thickness+item.unit+' × ' ;
							if(item.category_id=='PLT8')
								item.description += item.width+item.unit+' × ' ;

							item.description += item.length+item.unit;
							
							
							
						trnx.items.push(item);
					}


				$scope.Transactions.push(trnx);
				$scope.TransactionsLKUP[trnx.id] =  trnx;
			}
		}
		function errorGetTransactions($response, $status, $scope){ }

    }]);
});


