

define(['config','productService'], function (app) {

    app.register.controller('productModalController',['$scope',  '$rootScope','productService', function ($scope,  $rootScope, productService) {
       $scope.initializeController = function () { 
			$scope.ActiveProductTab = $scope.ActiveProductTab||'specification';
			$scope.Categories = [{id:'PART',name:'Parts'},{id:'OILS',name:'Oils'},{id:'BATT',name:'Batteries'},{id:'TIRE',name:'Tires'},{id:'OTHR',name:'Others'}]
			$scope.Categories = [
									{id:'PLT8',name:'Plates'},
									{id:'RODS',name:'Rod<PERSON>'},
									{id:'OTHR',name:'Others'}
								];

			$scope.Types = [
									{id:'LOC',name:'Local'},
									{id:'IMP',name:'Import'}
								];
								
			$scope.$on('addProduct',addProduct);
			$scope.$on('updateProduct',updateProduct);
			$scope.$on('updateProductQuantity',updateProductQuantity);
			$scope.$on('updateProductStocks',updateProductStocks);
			$scope.$on('archiveProduct',archiveProduct);
			$scope.$on('activateProduct',activateProduct);
			$scope.$on('updateMarkup',updateMarkup);
			$scope.$watch('capital',updateSRP);
			$scope.updateSrp = updateSRP;
			$scope.updateMarkup = updateMarkup;
			$scope.copyToSOH = function(area){
				var soh_qty = parseFloat(area.toFixed(3));
				$scope.soh_quantity = soh_qty;
				$scope.ActiveProductTab = 'quantity';
			}
			$scope.$watch('stocks',computeTotalArea,true);

			$scope.addStock = function(){
				$scope.stockCode =  $scope.stockCode.toUpperCase();
				var stock = {
					id:$scope.stockCode,
					type:$scope.stockType,
					quantity:$scope.stockQuantity,
					area:$scope.stockArea,
					quantity_actual:$scope.stockQuantity,
					area_actual:$scope.stockQuantity*$scope.stockArea,
					notes:$scope.stockNotes,
					is_new:true,
				}
				stock =  angular.copy(stock);
				$scope.stocks.push(stock);
				$scope.stockCode = null;
				$scope.stockType = null;
				$scope.stockArea = null;
				$scope.stockQuantity = 1;
				$scope.stockNotes = null;
			}
			$scope.removeStock = function(index){

				if($scope.stocks[index].is_new){
					$scope.stocks.splice(index,1);
				}
				else{
					var stkObj = $scope.stocks[index];
					var codeIn  =prompt("Enter code to confirm deletion");
					if(codeIn.toString().toUpperCase()!==stkObj.id.toString()){
						return alert("Invalid code. Try again.");
					}else{
						productService.unlinkProductStock(successUnlinkProduct,errorUnlinkProduct,$scope,stkObj.id);
					}
				}

			}
	   }

	   function successUnlinkProduct($response,$status,$scope){
	   	
	   	$scope.stocks.map(function(elem,index){
	   		if(elem.id ==  $response.data.id){
	   			return $scope.stocks.splice(index,1);
	   		}
	   	})

	   }
	   function errorUnlinkProduct($response){
			console.log($response);	   	
	   }
	  
		function updateSRP(){
			if($scope.adjustment){
				if($scope.adjustment.tmp_srp) return;
			}
			$scope.srp = $scope.capital + $scope.markup;
		}
		function updateMarkup(){
			$scope.markup = $scope.srp - $scope.capital;
		}

		function computeTotalArea(){
			var totalArea = 0;
			var totalQty = 0;
			console.log($scope.stocks)
			for(var i in $scope.stocks){
				var area =  $scope.stocks[i].area;
				var qty =  $scope.stocks[i].quantity;
				//$scope.stocks[i].quantity_actual =  qty;
				//$scope.stocks[i].area_actual =  qty*area;
				totalArea+=$scope.stocks[i].area_actual;
				totalQty+=qty;
			}
			$scope.tmp_quantity =  totalArea;
			$scope.stock_quantity = totalQty;
		}
		function addProduct(){
			productService.addProduct(successAddProduct,errorAddProduct,$scope);
		}
		function updateProduct(){
			productService.updateProduct(successUpdateProduct,errorUpdateProduct,$scope);
		}
		function updateProductQuantity(){
			productService.updateProductQuantity(successUpdateProductQuantity,errorUpdateProductQuantity,$scope);
		}

		function updateProductStocks(){
			productService.updateProductStocks(successUpdateProductStocks,errorUpdateProductStocks,$scope);
		}

		

		function archiveProduct(evts,args){
			productService.archiveProduct(args.success,args.error,args.scope,args.id);
		}
		function activateProduct(evts,args){
			productService.activateProduct(args.success,args.error,args.scope,args.id);
		}
		function successAddProduct($response, $status, $scope){
			$rootScope.$broadcast('productAdded',{response:$response});
		}
		function errorAddProduct($response, $status, $scope){ }
		function successUpdateProduct($response, $status, $scope){
			$rootScope.$broadcast('productUpdated',{response:$response});
		}
		function errorUpdateProduct($response, $status, $scope){ }
		function successUpdateProductQuantity($response, $status, $scope){
			$rootScope.$broadcast('productQuantityUpdated',{response:$response});
		}
		function successUpdateProductStocks($response, $status, $scope){
			//$rootScope.$broadcast('productStocksUpdated',{response:$response});
		}
		function errorUpdateProductQuantity($response, $status, $scope){ }
		function errorUpdateProductStocks($response, $status, $scope){ }
    }]);
});


