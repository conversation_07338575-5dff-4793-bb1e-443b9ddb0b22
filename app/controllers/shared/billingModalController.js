define(['config', 'customerService', 'invoiceService','billingService', 'alertsService'], function (app) {

    // Add unique filter for pagination
    app.register.filter('unique', function() {
        return function(collection) {
            var output = [];
            var keys = {};

            angular.forEach(collection, function(item) {
                var key = item;
                if (!keys[key]) {
                    keys[key] = true;
                    output.push(item);
                }
            });

            return output;
        };
    });

    app.register.controller('billingModalController', ['$scope', '$rootScope', '$timeout', 'customerService', 'invoiceService','billingService', 'alertsService',
    function ($scope, $rootScope, $timeout, customerService, invoiceService, billingService,alertsService) {
        var __customer_buffer, __customer_page;

        $scope.initializeController = function () {
            // Initialize variables
            $scope.Invoices = undefined;
            $scope.LoadingInvoices = false;


            $scope.selectAll = false;
            if($scope.$parent.Ledger){
                $scope.billing_date = angular.copy($scope.$parent.Ledger.billing_date);
                $scope.terms = angular.copy($scope.$parent.Ledger.terms);
                $scope.soa_no = angular.copy($scope.$parent.Ledger.ref_no);
                return;
            }
            $scope.billing_date = new Date();
            $scope.prepared_date = new Date();
            $scope.previous_balance = 0;
            $scope.terms = '30 days';
            $scope.CustomerObject = null;
            $scope.CustomerObjectTypeAhead = null;

            // Initialize billing-related variables
            $scope.UnpaidInvoices = [];
            $scope.total_unpaid = 0;
            $scope.selected_total = 0;
            $scope.outstanding_balance = 0;
            $scope.selected_count = 0;

            // Initialize tab state
            $scope.activeTab = 'new-invoices';

            // Pagination variables
            $scope.CurrentPage = 1;
            $scope.LastPage = 1;
            $scope.itemsPerPage = 10;
            $scope.totalItems = 0;
            $scope.ActivePages = [1];

            // Make Math available in the template
            $scope.Math = window.Math;

            // Set default due date (30 days from today)
            $scope.due_date = new Date(new Date().getTime() + 30 * 24 * 60 * 60 * 1000);

            // Initialize typeahead for customer search
            initTypeAhead();
            initSOANo();
            // Listen for customer selection
            $scope.$on('typeahead:selected', handleCustomerSelection);
        };

        // Reset customer selection
        $scope.resetCustomer = function() {
            $scope.CustomerObject = null;
            $scope.CustomerObjectTypeAhead = null;
            $scope.Invoices = [];

            // Reset pagination
            $scope.CurrentPage = 1;
            $scope.LastPage = 1;
            $scope.totalItems = 0;
            $scope.ActivePages = [1];

            // Reset billing info
            $scope.previous_balance = 0;
            $scope.UnpaidInvoices = [];
            $scope.total_unpaid = 0;
            $scope.selected_total = 0;
            $scope.outstanding_balance = 0;
            $scope.calculateTotal();

            // Reset tab to default
            $scope.activeTab = 'unpaid-invoices';
        };

        // Update active pages array for pagination
        function updateActivePages() {
            $scope.ActivePages = [];
            var start = Math.max(1, $scope.CurrentPage - 2);
            var end = Math.min($scope.LastPage, $scope.CurrentPage + 2);

            for (var i = start; i <= end; i++) {
                $scope.ActivePages.push(i);
            }
        }

        // Toggle select all invoices
        $scope.toggleSelectAll = function() {
            angular.forEach($scope.Invoices, function(invoice) {
                invoice.selected = $scope.selectAll;
            });
        };

        // Calculate total amount of selected invoices
        $scope.$watch('Invoices', function() {
            $scope.calculateTotal();
        }, true);
        $scope.calculateTotal = function() {
            var total = 0;
            var selectedCount = 0;
            angular.forEach($scope.Invoices, function(invoice) {
                if (invoice.selected) {
                    total += parseFloat(invoice.amount);
                    selectedCount++;
                }
            });
            // Format to 2 decimal places
            let selectedTotal = parseFloat(total.toFixed(2));
            $scope.selected_total = selectedTotal;
            $scope.selected_count = selectedCount;

            // Calculate outstanding balance (selected invoices + unpaid invoices)
            $scope.calculateOutstandingBalance();

            return selectedTotal;
        };

        // Calculate outstanding balance (selected invoices + total unpaid invoices)
        $scope.calculateOutstandingBalance = function() {
            var selectedTotal = $scope.selected_total || 0;
            var unpaidTotal = $scope.total_unpaid || 0;
            $scope.outstanding_balance = parseFloat((selectedTotal + unpaidTotal).toFixed(2));
            return $scope.outstanding_balance;
        };

        // Tab switching function
        $scope.switchTab = function(tabId) {
            $scope.activeTab = tabId;
        };

        // Check if tab is active
        $scope.isActiveTab = function(tabId) {
            return $scope.activeTab === tabId;
        };

        // Check if any invoices are selected
        $scope.hasSelectedInvoices = function() {
            var hasSelected = false;
            angular.forEach($scope.Invoices, function(invoice) {
                if (invoice.selected) {
                    hasSelected = true;
                }
            });
            return hasSelected;
        };

        // Cancel modal
        $scope.cancel = function() {
            $scope.$dismiss('cancel');
        };

        // Confirm and submit billing
        $scope.confirm = function() {
            if (!$scope.CustomerObject || !$scope.billing_date || !$scope.due_date) {
                alertsService.RenderErrorMessage('Please fill in all required fields');
                return;
            }

            if (!$scope.hasSelectedInvoices()) {
                alertsService.RenderErrorMessage('Please select at least one invoice');
                return;
            }

            // Show progress indicator
            $rootScope.Progress = 50;

            // Prepare selected invoices and calculate total amount
            var selectedInvoices = [];
            var totalAmount = 0;
            var previousBal = $scope.previous_balance;
            var billingDetails = [];
            var invoiceNos = [];
            totalAmount+=previousBal;
            angular.forEach($scope.Invoices, function(invoice) {
                if (invoice.selected) {
                    selectedInvoices.push(invoice);
                    totalAmount += parseFloat(invoice.amount);

                    // Create billing detail for each selected invoice
                    billingDetails.push({
                        ref_no: invoice.invoice_no,
                        invoice_date: formatDate(invoice.date),
                        invoice_amount: invoice.amount,
                        invoice_id: invoice.id
                    });


                    invoiceNos.push(invoice.invoice_no);
                }
            });

            
            // Get SOA number from scope
            var soaNo = $scope.soa_no;

            // Format billing date
            var billingDate = formatDate($scope.billing_date);
            // Get terms from scope
            var terms = $scope.terms;


            // Create the billing data structure
            var billing = {
                header: {
                    type: 'bill',
                    status: 'billed',
                    entity_type: 'customer',
                    soa_no: soaNo,
                    due_date: billingDate,
                    previous_balance: previousBal,
                    outstanding_balance: totalAmount,
                    amount:totalAmount,
                    date: new Date().toISOString().slice(0, 10),
                    details: invoiceNos.join(','),
                    terms:terms
                },
                details: billingDetails
            };
            billing.header.entity =  billing.header.customer = angular.copy($scope.CustomerObject);
            billing.header.entity_id = billing.header.customer_id =  $scope.CustomerObject.id;

            // Call invoiceService to create billing
            invoiceService.createBilling(successCreateBilling, errorCreateBilling, $scope,billing);
        };

        function handleCustomerSelection(_, data) {
            if (data.hasOwnProperty('id')) {
                let customerId = data.id;
                $scope.CustomerObject = data;
                // Load invoices for the selected customer
                loadCustomerInvoices(customerId);
                $scope.Invoice = undefined;
                getBillInfo(customerId);
            }
        }



        $scope.preview = function(){
            // Set values for SOA print form and submit it
            $rootScope.SOA_No = $scope.soa_no;
            $rootScope.Trnx_ID = $scope.id;

            // Submit the form to generate PDF
            $timeout(function() {
                document.getElementById('SOAPrintOut').submit();
            }, 500);
        }
        function loadCustomerInvoices(customerId, loadMore = false) {
            $scope.LoadingInvoices = true;
            $scope.itemsPerPage = 30;
            // Only clear invoices if it's not a "load more" request
            if (!loadMore) {
                $scope.Invoices = [];
                $scope.CurrentPage = 1;
                $scope.itemsPerPage = 15;
            }

            // Get customer name from the CustomerObject
            var customerName = $scope.CustomerObject.name;

            // Set customerId on the actual $scope
            $scope.customerId = customerId;

            // Call invoiceService to get invoices for the customer
            invoiceService.getCustomerInvoices(
                loadMore ? successLoadMoreInvoices : successGetInvoices,
                errorGetInvoices,
                $scope,
                customerName,
                {
                    status: 'fulfilled',
                    sort: 'latest',
                    page: $scope.CurrentPage,
                    limit: $scope.itemsPerPage
                }
            );
        }

        // Load more invoices function
        $scope.loadMoreInvoices = function() {
            if ($scope.LoadingInvoices || !$scope.hasMoreInvoices()) {
                return;
            }
            $scope.CurrentPage++;
            loadCustomerInvoices($scope.customerId, true);
        };

        // Check if there are more invoices to load
        $scope.hasMoreInvoices = function() {
            // Check if we have meta information and if there are more pages
            var hasMore = $scope.CurrentPage < $scope.LastPage;

            // Additional check: if we have loaded fewer invoices than total items
            var loadedCount = $scope.Invoices ? $scope.Invoices.length : 0;
            var totalCount = $scope.totalItems || 0;

            return hasMore && (loadedCount < totalCount);
        };

        // Get loading progress information
        $scope.getLoadingProgress = function() {
            var loaded = $scope.Invoices ? $scope.Invoices.length : 0;
            var total = $scope.totalItems || 0;
            return {
                loaded: loaded,
                total: total,
                percentage: total > 0 ? Math.round((loaded / total) * 100) : 0
            };
        };

        function initTypeAhead() {
            __customer_buffer = [];
            __customer_page = 1;

            // Typeahead options
            $scope.typeAheadOptions = {
                highlight: true
            };

            $scope.typeAheadCustomerData = {
                displayKey: 'name',
                source: {}
            };

            // Load customers
            customerService.getCustomers(successGetCustomers, errorGetCustomers, $scope, {page: __customer_page});
        }

        function initSOANo(){
            customerService.generateSOANo(successGenerateSOANo,errorGenerateSOANo,$scope);
        }

        function successCreateBilling(response) {
            $rootScope.Progress = 100;
            alertsService.RenderSuccessMessage('Billing created successfully!');
                console.log(response);
                var trnxId = response.data.id;
                var soaNo = response.data.header.soa_no;
                var totalAmount = response.data.header.due_amount;

                // Set values for SOA print form and submit it
                $rootScope.SOA_No = soaNo;
                $rootScope.Trnx_ID = trnxId;

                // Submit the form to generate PDF
                $timeout(function() {
                    document.getElementById('SOAPrintOut').submit();
                }, 500);

                // Close modal and notify parent controller
                $scope.$close({
                    success: true,
                    id: trnxId,
                    soa_no: soaNo,
                    total_amount: totalAmount
                });
        }

        function errorCreateBilling(error) {
            $rootScope.Progress = 0;
            alertsService.RenderErrorMessage('Failed to create billing: ' + (error && error.message ? error.message : 'Unknown error'));
        }

        function successGenerateSOANo(response){
            $scope.soa_no = response.data.id;

        }

        function errorGenerateSOANo(error) {
            $rootScope.Progress = 0;
            alertsService.RenderErrorMessage('Generating SOA No: ' + (error && error.message ? error.message : 'Unknown error'));
        }
        function successGetInvoices(response) {
            $scope.LoadingInvoices = false;

            // Process invoices from the transactions response
            if (response && response.data) {
                var transactions = response.data;
                var customerId = $scope.CustomerObject.id;

                angular.forEach(transactions, function(transaction) {
                    // Only include transactions for the selected customer
                    if (transaction.entity_id == customerId) {
                        // Make sure we have the invoice data
                        if (transaction.Invoice && transaction.Invoice.si_no) {
                            let status = transaction.status;
                                if(transaction.status=='fulfilled')
                                    status =  'unbilled';
                            var invoice = {
                                id: transaction.id,
                                invoice_no: transaction.Invoice.si_no,
                                jo_no: transaction.jo_no || transaction.jo_trnx_id || 'N/A',
                                date: new Date(transaction.timestamp),
                                amount: transaction.amount,
                                status: status,
                                selected: false
                            };

                            $scope.Invoices.push(invoice);
                        }
                    }
                });

                // Sort invoices by date (oldest first)
                $scope.Invoices.sort(function(a, b) {
                    return a.date - b.date;
                });

                // Update pagination data if available
                if (response.meta) {
                    $scope.totalItems = response.meta.items || $scope.Invoices.length;
                    $scope.LastPage = response.meta.pages || Math.ceil($scope.totalItems / $scope.itemsPerPage);
                    $scope.CurrentPage = response.meta.page || $scope.CurrentPage;
                } else {
                    // If no meta data, calculate based on current results
                    $scope.totalItems = $scope.Invoices.length;
                    $scope.LastPage = Math.ceil($scope.totalItems / $scope.itemsPerPage);
                }

                // Update ActivePages array for pagination
                updateActivePages();
            }
        }

        function errorGetInvoices(error) {
            $scope.LoadingInvoices = false;
            alertsService.RenderErrorMessage('Failed to load invoices: ' + (error && error.message ? error.message : 'Unknown error'));
        }

        function successLoadMoreInvoices(response) {
            $scope.LoadingInvoices = false;

            // Process and append new invoices from the transactions response
            if (response && response.data) {
                var transactions = response.data;
                var customerId = $scope.CustomerObject.id;
                var newInvoices = [];

                angular.forEach(transactions, function(transaction) {
                    // Only include transactions for the selected customer
                    if (transaction.entity_id == customerId) {
                        // Make sure we have the invoice data
                        if (transaction.Invoice && transaction.Invoice.si_no) {
                            let status = transaction.status;
                            if(transaction.status=='fulfilled')
                                status = 'unbilled';
                            var invoice = {
                                id: transaction.id,
                                invoice_no: transaction.Invoice.si_no,
                                jo_no: transaction.jo_no || transaction.jo_trnx_id || 'N/A',
                                date: new Date(transaction.timestamp),
                                amount: transaction.amount,
                                status: status,
                                selected: false
                            };

                            newInvoices.push(invoice);
                        }
                    }
                });

                // Sort new invoices by date (oldest first) before appending
                newInvoices.sort(function(a, b) {
                    return a.date - b.date;
                });

                // Append new invoices to the end of existing array (preserving selections)
                $scope.Invoices = $scope.Invoices.concat(newInvoices);

                // Update pagination data using correct meta fields
                if (response.meta) {
                    $scope.totalItems = response.meta.items || $scope.totalItems;
                    $scope.LastPage = response.meta.pages || $scope.LastPage;
                    $scope.CurrentPage = response.meta.page || $scope.CurrentPage;

                    // Store meta info for debugging
                    $scope.metaInfo = response.meta;
                }
            }
        }

        function successGetCustomers($response) {
            __customer_buffer = __customer_buffer.concat($response.data);
            $rootScope.__Progress = (__customer_page / $response.meta.pages) * 100;

            if ($response.meta.next) {
                __customer_page = __customer_page + 1;
                return customerService.getCustomers(successGetCustomers, errorGetCustomers, $scope, {page: __customer_page});
            } else {
                $timeout(function() {
                    // Reset progress
                }, 1500);
            }

            // Initialize Bloodhound for typeahead
            var customers = new Bloodhound({
                datumTokenizer: function(d) { return Bloodhound.tokenizers.whitespace(d.name); },
                queryTokenizer: Bloodhound.tokenizers.whitespace,
                local: __customer_buffer
            });

            // Initialize the bloodhound suggestion engine
            customers.initialize();

            // Set up typeahead data
            $scope.typeAheadCustomerData = {
                displayKey: 'name',
                source: customers.ttAdapter()
            };

            $scope.CustomerObject = null;
        }

        function errorGetCustomers() {
            alertsService.RenderErrorMessage('Failed to load customers');
        }

        function formatDate(date) {
            if (!date) return null;
            var d = new Date(date);
            var month = '' + (d.getMonth() + 1);
            var day = '' + d.getDate();
            var year = d.getFullYear();

            if (month.length < 2) month = '0' + month;
            if (day.length < 2) day = '0' + day;

            return [year, month, day].join('-');
        }

        function getBillInfo(customer_id){
            $scope.CustomerId =  customer_id;
            billingService.getBillingInfo(successGetBillInfo, errorGetBillInfo, $scope);
        }

        function successGetBillInfo(response){
            console.log(response);

            if (response && response.data) {
                // Set customer billing information
                $scope.CustomerBilling = response.data;

                // Set previous balance from the response
                $scope.previous_balance = parseFloat(response.data.previous_balance || 0);

                // Set unpaid invoices
                $scope.UnpaidInvoices = response.data.unpaid_invoices || [];

                // Calculate total unpaid amount
                $scope.total_unpaid = 0;
                angular.forEach($scope.UnpaidInvoices, function(invoice) {
                    $scope.total_unpaid += parseFloat(invoice.invoice_amount || 0);
                });

                // Update outstanding balance calculation
                $scope.calculateOutstandingBalance();
            }
        }

        function errorGetBillInfo(response){
            console.log('Error getting billing info:', response);
            // Set defaults if error occurs
            $scope.previous_balance = 0;
            $scope.UnpaidInvoices = [];
            $scope.total_unpaid = 0;
        }
    }]);
});
