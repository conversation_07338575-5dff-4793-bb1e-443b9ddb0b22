

define(['config','transactionService','alertsService'], function (app) {

    app.register.controller('transactionModalController',['$scope',  '$rootScope','$timeout','transactionService','alertsService', function ($scope,  $rootScope, $timeout, transactionService,alertsService) {
		const CARD_INTEREST = 0.05;
		const MAX_ITEMS_PER_RECEIPT = 10;
       $scope.initializeController = function () { 
			$scope.BackLogEnable = false;
			$scope.SINewVersion =  angular.copy($rootScope.SINewVersion);
			if(!$scope.OptionalPayment&&!$scope.SetupModal){
				$scope.payCash = true;
				$scope.ActiveTransactionTab = 'payment';
				$scope.OptionalReceipt =true;
			}
			$scope.TotalCharges = $scope.GrossCommission =  $scope.Tax = $scope.Commission =   $scope.Interest =  0;
			$scope.TotalPayments = $scope.CashChange = $scope.CashReceived = $scope.ChequeReceived = $scope.CardReceived = $scope.ChargeReceived = 0;
			$scope.SINo = $scope.CRNo =  $scope.DRNo = null;

			$scope.IsInvoiceCOD = 0;
			//$scope.DiscountPercent = 0;
			$scope.DiscountPercent = $scope.DiscountPercent||0; 
			$scope.Discount = $scope.Discount||0; 
			
			$scope.ChequeDetails = $scope.CardDetails  = $scope.CashDetails = null;
			if(!$scope.ChargeDetails) $scope.ChargeDetails = null;
			if($scope.header.tax_type) $scope.TaxType = $scope.header.tax_type;
			if($scope.header.with_holding_tax) $scope.WithHoldingTax = $scope.header.with_holding_tax;
			if($scope.header){
				if($scope.header.type=='invoice'){
					if($scope.TaxType!='VAT'&&$scope.TaxType!='ZRO')
						alertsService.RenderErrorMessage('Invalid tax type found. Please update Customer Info in Accounts');
				}
			}

			if($scope.header) $scope.TotalCharges = $scope.header.amount;
			if($scope.payCash) $scope.CashReceived = $scope.TotalCharges;
			else if($scope.payCharge) $scope.ChargeReceived = $scope.TotalCharges;

			//Items per Page 
			$scope.TotalItems  =  $scope.details.length;
			$scope.PaperCount =1;
			$scope.MaxIPR =MAX_ITEMS_PER_RECEIPT;
			
			if($scope.TotalItems>$scope.MaxIPR){
				$scope.PaperCount = Math.ceil($scope.TotalItems/$scope.MaxIPR);
				console.log($scope.PaperCount);
			}

			$scope.$on('addTransaction',addTransaction);
			$scope.$on('updateTransaction',updateTransaction);
			$scope.$on('cancelTransaction',cancelTransaction);
			$scope.AllowDel = false;
			console.log($scope.header,$scope.details);
			if($scope.header.type=='po'  && $scope.header.status == 'created'){
				$scope.AllowDel  =true;

			}
			initBackLog();
			
	   }
	   $scope.$watch('payCheque',function(value){ 	if(!value){ $scope.ChequeReceived = $scope.ChequeDetails = null; $scope.updatePayments();}  });
	   $scope.$watch('payCard',function(value){ 	if(!value){ $scope.CardReceived = $scope.CardDetails = null;  $scope.updateInterest(false); $scope.updatePayments();}else{ $scope.updateInterest(true);}  });
	   $scope.$watch('payCharge',function(value){ 	if(!value){ $scope.ChargeReceived = $scope.ChargeDetails = null; $scope.updatePayments();}  });
	   $scope.$watch('payCash',function(value){ 	if(!value){ $scope.CashReceived   = $scope.CashAmount = $scope.CashChange = null; $scope.CashDetails = null; $scope.updatePayments();} });
	   $scope.enableBackLog = function(flag){
		$scope.BackLogEnable = flag;
	   }
		
		$scope.$watchGroup(['TotalPayments','TotalPayments','CardDetails','ChequeDetails','ChargeDetails'],function(value){ 
			
			verifyPaymentDetails();
		});
		$scope.$watch('SINo',function(si_no){
			if(!si_no) return;
			var si_arr = si_no.split('-');
			if(si_arr.length){
				var si_num= parseInt(si_arr[1]);
				if(!isNaN(si_num)){
					var si_ctr =(si_num-1+ $scope.PaperCount)+''; 
					var si_end = si_arr[0]+'-'+ si_ctr.padStart(4,'0');
					$scope.SINoEnd = si_end;
				}
			}
		});

		$scope.$watch('DRNo',function(dr_no){
			if(!dr_no) return;
			var dr_arr = dr_no.split('-');
			if(dr_arr.length){
				var dr_num= parseInt(dr_arr[1]);
				if(!isNaN(dr_num)){
					var dr_ctr  =(dr_num-1+ $scope.PaperCount)+'';
					var dr_end = dr_arr[0]+'-'+  dr_ctr.padStart(4,'0');
					$scope.DRNoEnd = dr_end ;
				}
			}
		});
		$scope.$watchGroup(['SINo','SINoEnd','DRNo','DRNoEnd','CRNo'],function(){
			verifyReceipts();
		});

		function verifyReceipts(){
			$scope.ReceiptValid = !!$scope.SINo;
			
			// Allow DR even if VAT
			//$scope.ReceiptValid = $scope.ReceiptValid && (( $scope.header.tax_type=='ZRO' && !!$scope.DRNo )|| ( $scope.header.tax_type=='VAT' &&!$scope.DRNo)) ;
			
			if($scope.PaperCount>1){
				$scope.ReceiptValid = $scope.ReceiptValid  && !!$scope.SINoEnd;
				$scope.ReceiptValid = $scope.ReceiptValid && ( !!$scope.DRNo == !!$scope.DRNoEnd );
			}
			//$scope.ReceiptValid = $scope.ReceiptValid && (($scope.IsInvoiceCOD==1 && !!$scope.CRNo) || ($scope.IsInvoiceCOD==0 && !$scope.CRNo));
			/*
			if($scope.IsInvoiceCOD==1 && $scope.ActiveTransactionTab!='payment'){
				if(!$scope.payCash && !$scope.payCheque && !$scope.payCard){
					$scope.ActiveTransactionTab = 'payment';
					$scope.ReceiptValid = false;
					$scope.payCash = true;
					$scope.payCharge= false;
					return alert("Please provide payment details for COD");
				}
			}
			*/
			console.log($scope.ReceiptValid);
			if($scope.ReceiptValid){
				var invoice_date =  angular.copy($scope.header.invoice_date);
				$scope.header.si_no =  $scope.SINo;
				if($scope.PaperCount>1){
					$scope.header.si_end =  $scope.SINoEnd;
					$scope.header.item_per_page = MAX_ITEMS_PER_RECEIPT;
				}

				$scope.header.si_date =  invoice_date;
				$scope.header.cr_no =  $scope.CRNo;
				$scope.header.cr_date = invoice_date;

				if($scope.header.tax_type=='ZRO'||true){
					$scope.header.dr_no =  $scope.DRNo;
					if($scope.PaperCount>1)
					$scope.header.dr_end =  $scope.DRNoEnd;
					$scope.header.dr_date =  invoice_date;

				}
				//if($scope.IsInvoiceCOD==1){
					
				//}
			}else{
				$scope.header.si_no = null;
				$scope.header.si_date = null;
				$scope.header.dr_no = null;
				$scope.header.dr_date = null;
				$scope.header.cr_no = null;
				$scope.header.cr_date = null;
			}
			
		}
		function verifyPaymentDetails(){
			$scope.PaymentValid = $scope.TotalPayments>=$scope.TotalCharges;
			var paymentValid =  $scope.PaymentValid;
			if($scope.payCard){
				paymentValid = paymentValid && !!$scope.CardDetails;
			}
			if($scope.payCheque){
				paymentValid = paymentValid && !!$scope.ChequeDetails;
			}

			if($scope.payCharge){
				paymentValid = paymentValid && !!$scope.ChargeDetails;
			}

			$scope.PaymentValid =  paymentValid;
		}
		$scope.updateTax = function(){
			switch($scope.header.tax_type){
				case 'VAT':
					$scope.Tax =Math.round((($scope.TotalCharges*100)  / 112)*12)/100;
					$scope.NetOfVat = $scope.TotalCharges -  $scope.Tax;
					if($scope.WithHoldingTax!=undefined && !$scope.header.id)
						$scope.WithHoldingTax = Math.round($scope.NetOfVat)/100;
				break;
				case 'ZRO':
					$scope.Tax = 0;
					$scope.NetOfVat = $scope.TotalCharges;
					$scope.WithHoldingTax = 0;
				break;
				default:
					$scope.Tax = roundUp($scope.GrossCommission * 0.12);
					$scope.NetOfVat = $scope.TotalCharges;
					$scope.WithHoldingTax = 0;
				break;
			}
			
		}
		$scope.updateCommission = function(){
			$timeout(function(){
				if($scope.header.source!='delivery') 
					$scope.Commission = $scope.GrossCommission -  $scope.Tax;
			},300);
		}
		$scope.$watch('GrossCommission',function(value){ 
			$scope.updateTax();
		});
		$scope.$watch('Tax',function(value){ 
			$scope.updateCommission();
		});
		$scope.$watch('Commission',function(value){ 
			$scope.updateCharges();
		});

		$scope.$watch('SINewVersion',function(value){ 
			$rootScope.SINewVersion = value;
		});
		$scope.checkDiscount = function(usePercent){
			var validateDiscount = false;

			var discPerc = !$scope.DiscountPercent? 0:parseFloat($scope.DiscountPercent);
			
			if(!usePercent)
				$scope.DiscountPercent =(Math.round(($scope.Discount/$scope.header.amount)*10000)/10000)*100;
			else
				$scope.Discount =  ($scope.header.amount * discPerc)/100;
			var discount = !$scope.Discount? 0:parseFloat($scope.Discount);	
			if($scope.header.source!='delivery') {
				if(validateDiscount){
					var total_allowed_discount = Math.round($scope.header.total_allowed_discount);
					if(discount>total_allowed_discount){
						if(!confirm('Add discount? Allowed discount is up to P'+ total_allowed_discount+ ' only.')){
							discount = $scope.Discount = 0;
						}
					}
				}
			}else{
				$scope.ChargeReceived -= discount;
			}

			$scope.TotalCharges=$scope.header.amount - discount;

			$scope.updateTax();
			$scope.updateCharges();
		}
		$scope.updateInterest = function(applyInterest){
			var interest =  applyInterest?$scope.header.amount * CARD_INTEREST:0;
			$scope.Interest = interest;
			$scope.updateCharges();
		}
		$scope.updateCharges = function(){
			var gross_commission = !$scope.GrossCommission? 0:parseFloat($scope.GrossCommission);
			var tax = !$scope.Tax? 0:parseFloat($scope.Tax);
			var netOfVat = !$scope.NetOfVat? 0:parseFloat($scope.NetOfVat);
			var withHoldTax = !$scope.WithHoldingTax? 0:parseFloat($scope.WithHoldingTax);
			var interest = !$scope.Interest? 0:parseFloat($scope.Interest);
			var commission = !$scope.Commission? 0:parseFloat($scope.Commission);
			var discount = !$scope.Discount? 0:parseFloat($scope.Discount);
			var discount_percent = !$scope.DiscountPercent? 0:parseFloat($scope.DiscountPercent);
			if($scope.header == undefined) $scope.header={};
			$scope.header.commission = commission;
			$scope.header.tax = tax;
			$scope.header.net_of_vat = netOfVat;
			$scope.header.with_holding_tax = withHoldTax;
			$scope.header.interest = interest;
			$scope.header.discount = discount;
			$scope.header.discount_percent = discount_percent;
			
			
			$scope.TotalCharges =$scope.header.amount- discount;
			//$scope.TotalCharges +=commission;
			//$scope.TotalCharges -=tax;
			$scope.TotalCharges +=interest;
			//$scope.TotalCharges -=discount;
			
			if($scope.payCash)
				return $scope.updatePayments('cash');
			if($scope.payCharge){
				$scope.ChargeReceived = angular.copy($scope.TotalCharges);
				return $scope.updatePayments('charge');
			}
		}
		$scope.updatePayments = function (source){
			var cash = !$scope.CashReceived? 0:parseFloat($scope.CashReceived);
			var cheque = !$scope.ChequeReceived? 0:parseFloat($scope.ChequeReceived);
			var card = !$scope.CardReceived? 0:parseFloat($scope.CardReceived);
			var charge = !$scope.ChargeReceived? 0:parseFloat($scope.ChargeReceived);
			var totalAmount = $scope.TotalCharges;
			var nonCashBalance = totalAmount - cash;
			var nonCashPayment  = cheque+card+charge;
			if(source=='cheque'){
				if(cheque){
					$scope.TransactionAddForm.ChequeDetail.$setDirty();
					$scope.TransactionAddForm.ChequeDetail.$validate();
				}
				if(nonCashPayment>nonCashBalance) {
					alert('Check payment greater than balance');
					cheque = 0;
					$scope.ChequeReceived = cheque;
				}
			}else if(source=='card'){
				if(card){
					$scope.TransactionAddForm.CardDetail.$setDirty();
					$scope.TransactionAddForm.CardDetail.$validate();
				}
				if(nonCashPayment>nonCashBalance) {
					alert('Card payment greater than balance');
					card = 0;
					$scope.CardReceived = card;
				}
			}else if(source=='charge'){
				if(charge){
					$scope.TransactionAddForm.ChargeDetail.$setDirty();
					$scope.TransactionAddForm.ChargeDetail.$validate();
				}
				if(nonCashPayment>nonCashBalance) {
					alert('Charge payment greater than balance');
					charge = 0;
					$scope.ChargeReceived = charge;
				}
				$scope.IsInvoiceCOD = 0;
			}else if(source=='cash'){
				$scope.IsInvoiceCOD = 1;
			}
			var nonCashPayments = cheque+card+charge;
			var balance = totalAmount - nonCashPayments;
			
			
			if(cash) $scope.CashChange =  cash - balance;
			else $scope.CashChange = 0;
			
			$scope.CashAmount = cash;
			if(cash>balance) $scope.CashAmount  = balance;
			
			$scope.TotalPayments = 0;
			$scope.TotalPayments += cheque;
			$scope.TotalPayments += card;
			$scope.TotalPayments += charge;
			$scope.TotalPayments += cash;
		}

		$scope.printReceipt =  function(type,refNo){
			$scope.disableReprint = true;
			if(type=='spo'){
				$timeout(function(){
					document.getElementById('OrderReprint').submit();	
					$scope.disableReprint = false;
				},1500);
			}else if(type=='si'|| type=='dr'){
				$timeout(function(){
					var totalCharges  =$scope.header.amount;
					$scope.TotalCharges =  totalCharges
					switch($scope.header.customer.tax_type){

						case 'VAT':
							if($scope.header.tax==0)
								$scope.TaxType = 'VAT';
						break;
						case 'ZRO':
							if($scope.header.tax>0)
								$scope.TaxType = 'ZRO';
						break;
					}
					$scope.header.tax_type =  angular.copy($scope.TaxType);
					$scope.updateTax();

					
					trnxUpdateTax($scope.header.id,$scope.Tax, $scope.invoiceObj.id);
					$timeout(function(){
						switch(type){
							case 'si':
								document.getElementById('InvoiceReprint').submit();	
							break;
							case 'dr':
								document.getElementById('DeliveryReprint').submit();
							break;
						}
						$scope.disableReprint = false;
					},1500);
				},500);
			}
		}
		$scope.editRefNos = function(){
			$scope.editInvObj = true;
			$scope.copyInvoiceObj =  angular.copy($scope.invoiceObj);
		}
		$scope.confirmRefNos = function(){
			$scope.editInvObj =  false;
			var data = angular.copy($scope.invoiceObj);
			transactionService.updateInvoice(successUpdateInvoice,errorUpdateInvoice,$scope,data);

		}
		$scope.cancelRefNos = function(){
			$scope.editInvObj =  false;
			$scope.invoiceObj =  angular.copy($scope.copyInvoiceObj);
		}
		function initBackLog(){
			var initDate = new Date(angular.copy($scope.header.date));
			$scope.header.display_date =  initDate;
			$scope.backLogDate = initDate;
			$scope.$watch('BackLogEnable',function(flag){
				if(!flag){
					var backlog_date = angular.copy($scope.backLogDate);
					backlog_date = backlog_date.getFullYear()+'-'+(backlog_date.getMonth()+1)+'-'+backlog_date.getDate();
					$scope.header.date = backlog_date;
					var dates  = ['invoice','delivery','order','si','cr','dr'];
					for(var i in dates){
						var dateKey = dates[i]+'_date';
						if($scope.header.hasOwnProperty(dateKey))
							$scope.header[dateKey] = backlog_date;
					}
				}else{
					$scope.backLogDate = new Date(angular.copy($scope.header.date));
				}
				$scope.header.display_date = new Date(angular.copy($scope.header.date));
			});
			$scope.updateBackLog = function(value){
				$scope.backLogDate  = value;
			}
		}
		function roundUp(i){return(parseInt(i/10, 10)+(i%10==0?0:1))*10;}
		function addTransaction(){
			var data = {};
			data.header = $scope.$parent.header;
			data.header.commission += data.header.tax; 
			data.details = $scope.$parent.details;
			data.payments = buildPayments();
			
			transactionService.addTransaction(successAddTransaction,errorAddTransaction,$scope.$parent,data);
		}
		function buildPayments(){
			var payments = [];
			if($scope.payCash){
				payments.push({payment_type:"CASH",detail:$scope.CashDetails,amount:$scope.CashAmount});
			}
			if($scope.payCard){
				payments.push({payment_type:"CARD",detail:$scope.CardDetails,amount:$scope.CardReceived});
			}
			if($scope.payCheque){
				payments.push({payment_type:"CHCK",detail:$scope.ChequeDetails,amount:$scope.ChequeReceived});
			}
			if($scope.payCharge){
				payments.push({payment_type:"CHRG",detail:$scope.ChargeDetails,amount:$scope.ChargeReceived});
			}
			return payments;
		}
		function updateTransaction(){
			var data = {};
			data.header = $scope.$parent.header;
			data.payments = $scope.$parent.payments;
			data.details = $scope.$parent.details;
			if(data.header.token){
				data.header.id =  data.header.token;
				data.payments = buildPayments();
			}
			transactionService.updateTransaction(successUpdateTransaction,errorUpdateTransaction,$scope,data);
		}
		function cancelTransaction(){
			var data = {};
			data.header = $scope.$parent.header;
			data.payments = $scope.$parent.payments;
			data.details = $scope.$parent.details;
			transactionService.cancelTransaction(successCancelTransaction,errorCancelTransaction,$scope,data);
		}

		function successAddTransaction($response, $status, $scope){
			$rootScope.$broadcast('resetTransaction',$response);
			alertsService.RenderSuccessMessage('Transaction saved successfully!');
		}
		function errorAddTransaction($response, $status, $scope){ 
			alertsService.RenderErrorMessage('Error saving transaction.');
		}
		function successUpdateTransaction($response, $status, $scope){
			$rootScope.$broadcast('resetTransaction',$response);
			$rootScope.$broadcast('getTransactions');
		}
		function errorUpdateTransaction($response, $status, $scope){ 
			alertsService.RenderErrorMessage('Error updating transaction.');
		}
		function successCancelTransaction($response, $status, $scope){
			$rootScope.$broadcast('transactionCancelled',{response:$response});
		}
		function errorCancelTransaction($response, $status, $scope){ 
			alertsService.RenderErrorMessage('Error cancelling transaction.');
		}

		function successUpdateInvoice($response, $status, $scope){
			$rootScope.$broadcast('getTransactions');
			alertsService.RenderSuccessMessage('Invoice saved successfully!');
		}
		function errorUpdateInvoice($response, $status, $scope){ 
			alertsService.RenderErrorMessage('Error udpating invoice.');
		}

		function trnxUpdateTax(id,tax,invId){
			var trnx =  {id:id, tax:tax,invoice_id:invId};
			transactionService.updateTax(successUpdateTax,errorUpdateTax,$scope.$parent,trnx);
		}
		function successUpdateTax($response, $status, $scope){ };
		function errorUpdateTax($response, $status, $scope){ };
		

    }]);
});


