
define(['config','cutPacker','cutDrawer'], function (app) {
    app.register.controller('cutPlannerModalController',['$scope',  '$rootScope','$timeout','packerService','drawerService', function ($scope,  $rootScope,$timeout,packerService,drawerService) {
		var materials ;
		 $scope.initializeController = function () { 
		 	materials={};
		 	for(var i in $scope.jobOrder){
		 		var item =  $scope.jobOrder[i];
		 		var IID = item.id;
		 		if(!materials[IID]){
		 			materials[IID] = {id:IID,description:item.description,specs:[]};
		 		}
		 		var spec = {h:item.length_actual, w:item.width_actual, n:item.quantity_actual};
		 		materials[IID].specs.push(spec);
		 	}
		 	$scope.Materials = materials;
		 }
		 $scope.$watch('ActiveMaterial',function(mId){
		 	if($scope.Materials[mId])
		 		$scope.PreviewMaterial =  $scope.Materials[mId].specs||[];
		 });

		 $scope.calculateSheet = function(){
		 	
		 	
		 	
		 	var W =  $scope.Width;
		 	var H =  $scope.Length;
		 	var maxWidth = 500;
		 	var scale =  Math.round( (maxWidth/W)*1000) / 1000;
		 	var H2 =  H * scale;
		 	var W2 =  W * scale;

		 	$scope.displayHeight = H2;
		 	$scope.displayWidth = W2;
		 	var blocks = [];
		 	var displayblocks = [];
		 	var code = 1;
		 	$scope.CutPlanItems= [];
		 	for(var j in $scope.PreviewMaterial){
		 		var item =  $scope.PreviewMaterial[j];
		 		var cItem = {w:item.w,h:item.h,code:code,quantity:item.n};
		 		$scope.CutPlanItems.push(cItem);
		 		for(var i=1;i<=item.n;i++){
		 			var spec= {w:item.w,h:item.h,code:code};
		 			var dspec= {w:item.w*scale,h:item.h*scale,code:code};
		 			blocks.push(spec);
		 			displayblocks.push(dspec);
		 		}
		 		code++;
		 	}

		 	packerService.init(W, H);
		 	packerService.fit(blocks);

		 	packerService.init(W2, H2);
		 	packerService.fit(displayblocks);
		 	var canvas =  document.getElementById('cut-plan');
		 	drawerService.init(canvas);
		 	var root = packerService.root;
		 	console.log(root);
			drawerService.run(root, displayblocks);
			$scope.ActivePlanTab = 'plan';
		 }

		 
	 }]);
});