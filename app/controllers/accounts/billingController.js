

define(['config','ledgerModalController','billingModalController','customerService','vendorService','alertsService','transactionService'], function (app) {

    app.register.controller('BillingController',['$modal','$scope',  '$rootScope','customerService','vendorService','alertsService','transactionService', function ($modal,$scope,$rootScope,customerService,vendorService,alertsService,transactionService) {
		const LIMIT = 10;
		const NAV_PAGE_COUNT = 10;
       $scope.initializeController = function () {
		   $scope.FilterKeys = {entity_type:'customer'};
		   $scope.Entities = [
								{id:'customer',value:'Customers'},
								{id:'supplier',value:'Suppliers'},
							];
			$scope.TransactionTypes = [
								{id:'sales',value:'Sales'},
								{id:'tradein',value:'Trade In\'s'},
								{id:'return',value:'Returns'},
								{id:'deliveries',value:'Deliveries'},
								{id:'orders',value:'Orders'},
							];
			$scope.Coverages = [
								{id:'today',value:'Today'},
								{id:'yesterday',value:'Yesterday'},
								{id:'7D',value:'Last 7 days'},
								{id:'MON',value:'This Month'},
								//{id:'30D',value:'Last 30 days'},
								{id:'custom',value:'Custom'},
							];
			initLedger();
			initModal();
			$scope.$on('ledgerSaved',ledgerSaved);
			$scope.$watch('Ledgers',function(newvalue){
				$scope.Fillers = [];
				if(newvalue.length<NAV_PAGE_COUNT){
					for(var i=1;i<=NAV_PAGE_COUNT - newvalue.length;i++){
						$scope.Fillers.push('');
					}
					if(newvalue.length==0) $scope.Fillers.splice(0,1);
				}
			});
	   }
	   $scope.setFilterKey = function(field,value){
			$scope.FilterKeys[field] = value;
		}
		$scope.cancelFilter = function(){
			$scope.FilterKeys = {entity_type:angular.copy($scope.FilterKeys.entity_type)};
			$scope.Ledgers = [];
			$scope.Pages = [];
			$scope.movePage(1,null,$scope.FilterKeys);
			$scope.FilterEnabled = false;
			//$scope.toggleFilter();
		}
		$scope.confirmFilter = function(){
			$scope.Ledgers = [];
			$scope.Pages = [];
			$scope.movePage(1,null,$scope.FilterKeys);
			$scope.FilterEnabled = false;
			//$scope.toggleFilter();
		}
		$scope.toggleFilter = function(){
			$scope.FilterEnabled = !$scope.FilterEnabled;
		}
		$scope.searchFor =function(keyword){
			$scope.Ledgers = [];
			$scope.Pages = [];
			keyword = encodeURIComponent(keyword);
			$scope.SearchKeyword = angular.copy(keyword);
			$scope.movePage(1,$scope.SearchKeyword,$scope.FilterKeys);
		}
		$scope.resetSearch =function(){
			$scope.Pages = [];
			$scope.SearchEnabled = false;
			$scope.ledgerSearchBox=null;
			$scope.SearchKeyword=null;
			$scope.Ledgers = [];
			$scope.FetchProductId=null;
			$scope.movePage(1);
		}
		$scope.ledgerSearchFilter = function (ledger){
			var searchBox = $scope.ledgerSearchBox;
			var keyword = new RegExp(searchBox, 'i');
			var test = keyword.test(ledger.particulars)||keyword.test(ledger.entity.name)||keyword.test(ledger.ref_no);
			return !searchBox || test ; //Return NO FILTER or filter by patient_name
		}
	   $scope.movePage= function(__page,__keyword,__filter){
			$scope.NewLedger = null;
			$scope.SearchEnabled = __keyword? true:false;
			if(!__keyword) $scope.ledgerSearchBox=null;
			__filter =  __filter||{entity_type:'customer'};

			__filter = customFilter(__filter);

			if(__page>$scope.LastPage&&!__keyword && $scope.LastPage>0){
				$scope.GoToPage = $scope.LastPage;
				return alert('Oops! You can only go up to page '+$scope.LastPage);
			}
			$scope.CurrentPage = parseInt(__page);
			$scope.CurrentOffset = Math.ceil(__page/NAV_PAGE_COUNT) - 1;
			getLedgers(__page,__keyword,__filter,LIMIT);
		}
		$scope.exportData = function(__keyword,__filter,__format){
		   __filter = customFilter(__filter);
			$scope.ExportFilter = JSON.stringify(__filter);
			var entity = __filter.entity_type||'customer';
			console.log(entity);
		   switch(entity){
			case 'customer':
			__keyword = encodeURIComponent(__keyword);
			 var __types ='sales,return,invoice,payment';
			 var SOA_NO = '';
			 	if(__format =='pdf'){
			 		SOA_NO = window.prompt("ENTER SOA NO. (N/A if not applicable)");
			 	}

				transactionService.exportData({soa_no:SOA_NO,keyword:__keyword,format:__format,sort:'oldest',soa:true,last_bill:$scope.last_bill,filter:{coverage:'SOA',type:__types}});
				//customerService.exportData({keyword:__keyword,format:'pdf',filter:__filter});
			break;
			case 'supplier':
				vendorService.exportData({keyword:__keyword,filter:__filter});
			break;

		   }
		}
		function initLedger(){
			$scope.Pages = [];
			$scope.Ledgers = [];
			$scope.FetchCustomerLedgerId=null;
			$scope.setFilterKey('coverage','ALL');
			$scope.confirmFilter();
			$scope.movePage(1,null,$scope.FilterKeys);
		}
		function initModal(){
			var LedgerModalInstanceController = function ($scope, $modalInstance, ledger) {
				//Confirm handler
				$scope.Ledger =  ledger;
				$scope.confirm = function (ledgerId) {
					$rootScope.Progress = 50;
					$rootScope.$broadcast(ledgerId?'updateLedger':'addLedger');
					$modalInstance.close(ledgerId);
				};
				//Cancel handler
				$scope.cancel = function () {
				   $modalInstance.dismiss('cancel');
				};
				$scope.transaction_date =  new Date();
				var d = new Date();
				d.setHours(0,0,0,0);
				$scope.transaction_time = d;
				$scope.transaction_type ='billing'
				if(ledger){
					for(var $index in ledger){
						$scope[$index] = ledger[$index];
					}
				}

				$scope.delete = function(id){
					if(confirm("Please click OK to confirm delete")){
						var ledger = {id:id,action:'delete'};
						customerService.updateCustomerLedger(successUpdateCustLedgers,errorUpdateCustLedgers,$scope,ledger);
					}
				}

				function successUpdateCustLedgers(){
					 $modalInstance.close('confirm');
					 console.log($scope);
				}
				function errorUpdateCustLedgers(){
					alert("Could not delete transaction");
				}

			};
			//openLedgerModal function
			$scope.openBillingModal = function (ledger) {
				console.log(ledger);
				if (ledger) {
					// View existing billing
					var modalInstance = $modal.open({
						templateUrl: 'billingModalContent.html',
						controller: LedgerModalInstanceController,
						windowClass: 'app-modal-window',
						resolve:{
							ledger: function() {return ledger;}
						}
					});
					modalInstance.result.then(function (data) {
						$scope.movePage($scope.CurrentPage,$scope.SearchKeyword,$scope.FilterKeys);
					}, function (data) {
						console.log(data);
					});
				} else {
					// Create new billing
					var modalInstance = $modal.open({
						templateUrl: 'billingModalContent.html',
						controller: 'billingModalController',
						windowClass: 'app-modal-window',
					});
					modalInstance.result.then(function (result) {
						if (result && result.success) {
							// Refresh the list after successful creation
							$scope.movePage($scope.CurrentPage,$scope.SearchKeyword,$scope.FilterKeys);
							// Set the newly created billing as the active one
							$scope.NewLedger = result.id;
						}
					}, function () {
						console.log('Modal dismissed');
					});
				}
			};
		}
		function ledgerSaved(evt,args){
			var data = args.response.data;
			alertsService.RenderSuccessMessage('New ledger transaction added successfully!');
			$rootScope.Progress = 100;
			$scope.confirmFilter();
			$scope.NewLedger = data.id;
		}
	   function getLedgers(__page,__keyword,__filter,__limit){
		   $scope.LoadingLedgers = true;
		   __filter =  angular.copy(__filter);
		   var entity = __filter.entity;

		   // Set transaction type to "bill"
		   __filter.type = "bill";

		   // Use transactionService to get transactions of type "bill"
		   transactionService.getTransactions(successGetLedgers,errorGetLedgers,$rootScope,{page:__page,keyword:__keyword,limit:__limit,filter:__filter});
		}
		function successGetLedgers($response, $status){
			$scope.Ledgers = [];
			
			for(var index in $response.data){
				var transaction = $response.data[index];
				var ledgerEntry = {
					id: transaction.id,
					ref_no: transaction.ref_no,
					particulars: transaction.Billing.details,
					billing_date:new Date(transaction.Billing.due_date),
					terms: transaction.Billing.terms,
					amount: transaction.amount,
					flag: transaction.flag || 'c', // Default to credit for bills
					entity: {}
				};

				// Set entity information based on entity_type
				switch(transaction.entity_type){
					case 'customer':
						ledgerEntry.entity.id = transaction.Customer.id;
						ledgerEntry.entity.name = transaction.Customer.name;
					break;
					case 'supplier':
						ledgerEntry.entity.id = transaction.Supplier.id;
						ledgerEntry.entity.name = transaction.Supplier.name;
					break;
				}

				ledgerEntry.created = new Date(transaction.created);
				ledgerEntry.timestamp = new Date(transaction.timestamp);

				$scope.Ledgers.push(ledgerEntry);
			}
			$scope.LoadingLedgers = false;
			$scope.GoToPage = null;
			paginate($response.meta.pages);
		}
		function errorGetLedgers($response, $status){
				console.error("Error fetching transactions:", $response);
				$scope.LoadingLedgers = false;
			}
		function paginate(__pages){
			$scope.EnablePagination = true;
			if($scope.Pages.length==0){
				for(var i=1;i<=__pages;i++){
					$scope.Pages.push(i);
				}
				$scope.LastPage = $scope.Pages.length;
			}
			updatePages();
		}
		function updatePages(){
			$scope.ActivePages=[];
			for(var i=$scope.CurrentOffset*NAV_PAGE_COUNT,ctr=1;i<$scope.Pages.length&&ctr<=NAV_PAGE_COUNT;i++,ctr++){
				$scope.ActivePages.push($scope.Pages[i]);
			}
		}

		function customFilter(__filter){
			if(__filter.coverage=='custom'){
				var __f =  angular.copy(__filter);
				delete __f.coverage;
				var Datefrom =  new Date($scope.CustomDate.from);
				var Dateto =  new Date($scope.CustomDate.to);
				__f.from =  Datefrom.getFullYear()+'-'+(Datefrom.getMonth()+1)+'-'+Datefrom.getDate();
				__f.to =  Dateto.getFullYear()+'-'+(Dateto.getMonth()+1)+'-'+Dateto.getDate();
				__filter = __f;
			}
			return __filter;
		}
    }]);
});


