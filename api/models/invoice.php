<?php
class Invoice extends AppModel {
	var $name = 'Invoice';
	//The Associations below have been created with all possible keys, those that are not needed can be removed

	var $hasMany = array(
		'InvoiceDetail' => array(
			'className' => 'InvoiceDetail',
			'foreignKey' => 'invoice_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'InvoicePayment' => array(
			'className' => 'InvoicePayment',
			'foreignKey' => 'invoice_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
	);
	function applyPayment($refNos=array(),$ref_no=null,$check_no=null,$paymentAmount=0,$custId = null){
		$Invoice =  $this;
		$Transaction =  &ClassRegistry::init('Transaction');
		$Customer =  &ClassRegistry::init('Customer');
		$CustObj =  $Customer->findById($custId);
		$cond = array('Invoice.customer'=>$CustObj['Customer']['name']);
		$Invoice->recursive =-1;
		foreach($refNos as $refNo):
			$cond[1] = array('Invoice.si_no'=>$refNo);
			$invObj = $Invoice->find('first',array('conditions'=>$cond));

			if($invObj && $paymentAmount>0):
				$invObj = $invObj['Invoice'];
				$IID = $invObj['id'];
				$dueAmt = $invObj['total'] - $invObj['total_paid'];
				if($paymentAmount>=$dueAmt):
					$paymentAmount-=$dueAmt;
					$invObj['total_paid'] =  $dueAmt;
					$invPay = array(
									'invoice_id'=>$IID,
									'ref_no'=>$ref_no,
									'payment_type'=>'CHQE',
									'detail'=>$check_no,
									'amount'=>$dueAmt,
								);
					// Fully Paid
					$Invoice->InvoicePayment->save($invPay);
					$trnxId =  $Transaction->findRelated('invoice',$IID);
					$Transaction->updateStatus ($trnxId, 'paid');
				elseif($paymentAmount<$dueAmt):
					$invObj['total_paid'] =  $paymentAmount;
					$invPay = array(
									'invoice_id'=>$IID,
									'ref_no'=>$ref_no,
									'payment_type'=>'CHQE',
									'detail'=>$check_no,
									'amount'=>$paymentAmount,
								);
					//Partial Payment
					$Invoice->InvoicePayment->save($invPay);
					$paymentAmount=0;
				endif;
				$Invoice->save($invObj);
			endif;
			if($paymentAmount==0)
				break;
		endforeach;
	}

	function getUnpaid($customerId){
		$Transaction =  &ClassRegistry::init('Transaction');
		$fld = array('id','ref_no');
		$cond = array('type'=>'invoice',
						'status'=>'fulfilled',
						'entity_id'=>$customerId);
		$trnxs = $Transaction->find('list',array('conditions'=>$cond,'fields'=>$fld));
		$iids = array_values($trnxs);
		$cond =  array('id'=>$iids);
		$flds = array('si_no','po_no');
		$order = array('Invoice.si_date'=>'asc');
		$invoices =  $this->find('list',array('conditions'=>$cond,'fields'=>$flds,'order'=>$order));
		$refNos =  array_keys($invoices);
		return $refNos;
	}
}
