<?php
class CustomerLedger extends AppModel {
	var $name = 'CustomerLedger';
	//The Associations below have been created with all possible keys, those that are not needed can be removed

	var $belongsTo = array(
		'Customer' => array(
			'className' => 'Customer',
			'foreignKey' => 'customer_id',
			'conditions' => '',
			'fields' => '',
			'order' => ''
		)
	);
	function getBalances($balance,$id,$month='previous'){
		$data = $cond = array();
		$foreign_key = 'customer_id';
		$firstDay = date('Y-m-d', strtotime("first day of $month month"));
		$lastDay = date('Y-m-d', strtotime("last day of $month  month"));
		$cutoff = date('F 1 - d', strtotime("last day of $month  month"));
		$this->recursive=-1;
		$conf = array('fields'=>array('SUM(amount) as amount'));
		$cond[$foreign_key.' ='] = $id;
		$cond['timestamp >='] = $firstDay. ' 00:00:00';
		$cond['timestamp <='] = $lastDay. ' 23:59:59';
		$conf['conditions'] = $cond;
		$conf['conditions']['flag ='] = 'c';
		$data['charges']=$this->find('all',$conf)[0][0]['amount'] + 0;
		$conf['conditions']['flag ='] = 'd';
		$data['payments']=$this->find('all',$conf)[0][0]['amount'] + 0;
		$data['current_balance'] = $balance + $data['charges'] - $data['payments'];
		$data['cutoff'] = $cutoff;
		return $data;
	}

	public function find($conditions = null, $fields = array(), $order = null, $recursive = null) {
		    // Ensure consistent cache key format
		    $cacheKey = $this->generateCacheKey($conditions, $fields, $order, $recursive);
		    
		    // Try to read from the 'inventta' cache
		    $results = Cache::read($cacheKey, 'inventta');
		    
		    if ($results === false) {
		        // If cache is empty, perform the query and write the result to cache
		        $results = parent::find($conditions, $fields, $order, $recursive);
		        Cache::write($cacheKey, $results, 'inventta');
		    }
		    
		    return $results;
		}
    
    private function generateCacheKey($type,$query) {
        return 'customer_ledger_' . md5(serialize($query));
    }

    public function afterSave($created, $options = array()) {
        parent::afterSave($created, $options);
        $this->_clearCache();
    }

    public function afterDelete() {
        parent::afterDelete();
        $this->_clearCache();
    }

    public function _clearCache($type=NULL) {
        // Clear the cache for this model
        Cache::clear(false, 'inventta');
    }
}
