<?php
class Customer extends AppModel {
	var $name = 'Customer';

	public function find($type=NULL,$query = array(),$fields = array(), $order = NULL, $recursive = NULL) {


        // Generate a unique cache key based on the query
        $cacheKey = $this->generateCacheKey($type,$query);

        // Try to read from the 'inventta' cache
        $results = Cache::read($cacheKey, 'inventta');
        if ($results === false) {
            // If cache is empty, perform the query and write the result to cache
            $results = parent::find($type,$query);
			Cache::write($cacheKey, $results, 'inventta');
        }

        return $results;
    }

    private function generateCacheKey($type,$query) {
        return 'customer_' . md5(serialize($query));
    }

    /**
     * Generate an account number for a new customer
     * Format: YYYY-0000 where YYYY is the year and 0000 is a sequential number
     */
    public function beforeSave($options = array()) {
        if (empty($this->data['Customer']['id']) && empty($this->data['Customer']['account_no'])) {
            // This is a new customer without an account number
            $year = date('Y');

            // Find the highest account number for the current year
            $latestCustomer = $this->find('first', array(
                'conditions' => array(
                    'account_no LIKE' => $year . '-%'
                ),
                'order' => array('account_no DESC')
            ));

            $nextNumber = 1;
            if (!empty($latestCustomer)) {
                // Extract the sequence number from the account number
                $parts = explode('-', $latestCustomer['Customer']['account_no']);
                if (count($parts) == 2) {
                    $nextNumber = intval($parts[1]) + 1;
                }
            }

            // Format the account number
            $this->data['Customer']['account_no'] = $year . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
        }

        return true;
    }

    public function afterSave($created, $options = array()) {
        parent::afterSave($created, $options);
        $this->_clearCache();
    }

    public function afterDelete() {
        parent::afterDelete();
        $this->_clearCache();
    }

    public function _clearCache($type=NULL) {
        // Clear the cache for this model
        Cache::clear(false, 'inventta');
    }

}
