<?php
class ProductStock extends AppModel {
	var $name = 'ProductStock';
	//The Associations below have been created with all possible keys, those that are not needed can be removed

	var $belongsTo = array(
		'Product' => array(
			'className' => 'Product',
			'foreignKey' => 'product_id',
			'conditions' => '',
			'fields' => '',
			'order' => ''
		)
	);

	function getStockdetails($id){
		$cond =  array('ProductStock.product_id'=>$id);
		$dtls = $this->find('all',array('conditions'=>$cond));
		$stocks = array();
		if($dtls)
		foreach($dtls as $dtl){
			unset($dtl['ProductStock']['product_id']);
			unset($dtl['ProductStock']['created']);
			unset($dtl['ProductStock']['modified']);
			array_push($stocks,$dtl['ProductStock']);
		}
		return $stocks;
	}

	function generateStockCode(){
		$this->recursive=-1;
		do{
			$code = substr(strtoupper(md5(rand())),0,5);
			$exists = $this->findById($code);
		}while($exists);
		$stock = array('id'=>$code);
		$this->save($stock);
		return $code;
		

	}

	function updateStocks($items,$flag){
		
		foreach($items as $item){
			$stockObj = $this->findById($item['id']);
			if($stockObj){
				$stock  = $stockObj['ProductStock'];
				$stock['area_actual'] += $item['area'] *$flag;
				$stock['quantity_actual'] = ceil($stock['area_actual']/$stock['area']);
				$this->save($stock);
			}
		}
	}

	public function find($conditions = null, $fields = array(), $order = null, $recursive = null) {
		    // Ensure consistent cache key format
		    $cacheKey = $this->generateCacheKey($conditions, $fields, $order, $recursive);
		    
		    // Try to read from the 'inventta' cache
		    $results = Cache::read($cacheKey, 'inventta');
		    
		    if ($results === false) {
		        // If cache is empty, perform the query and write the result to cache
		        $results = parent::find($conditions, $fields, $order, $recursive);
		        Cache::write($cacheKey, $results, 'inventta');
		    }
		    
		    return $results;
		}
    
    private function generateCacheKey($type,$query) {
        return 'product_stock_' . md5(serialize($query));
    }
    
    public function afterSave($created, $options = array()) {
        parent::afterSave($created, $options);
        $this->_clearCache();
    }
    
    public function afterDelete() {
        parent::afterDelete();
        $this->_clearCache();
    }
    
    public function _clearCache($type=NULL) {
        // Clear the cache for this model
        Cache::clear(false, 'inventta');
    }
}
