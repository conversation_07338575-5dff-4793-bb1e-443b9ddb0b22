<?php
class BillingDetail extends AppModel {
	var $name = 'BillingDetail';
	var $actsAs = array('Containable');
	/**
	 * Find the associated invoice based on the ref_no
	 *
	 * @param string $ref_no The reference number to search for
	 * @return array|null The invoice data if found, null otherwise
	 */
	function findInvoiceByRefNo($ref_no) {
		$Invoice = &ClassRegistry::init('Invoice');

		// Try to find the invoice where ref_no matches si_no, dr_no, or cr_no
		$invoice = $Invoice->find('first', array(
			'conditions' => array(
				'OR' => array(
					'Invoice.si_no' => $ref_no,
					'Invoice.dr_no' => $ref_no,
					'Invoice.cr_no' => $ref_no
				)
			),
			'recursive' => -1
		));

		return $invoice ? $invoice : null;
	}

	/**
	 * Get all invoice details for a specific billing
	 *
	 * @param string $billing_id The billing ID
	 * @return array An array of billing details with associated invoice data
	 */
	function getInvoiceDetailsForBilling($billing_id) {
		// Get all billing details for this billing
		$billingDetails = $this->find('all', array(
			'conditions' => array('BillingDetail.billing_id' => $billing_id),
			'order' => array('BillingDetail.invoice_date' => 'ASC'),
			'contain'=>array('Invoice')
		));
		$billDtls = array();
		// Enhance each billing detail with its associated invoice
		foreach ($billingDetails as $index=>$detail) {
			$bd_Id = $detail['BillingDetail']['id'];
			$ref_no = $detail['BillingDetail']['ref_no'];
			$invoice = $this->findInvoiceByRefNo($ref_no);

			if ($invoice) {
				$detail['Invoice'] = $invoice['Invoice'];

				// Determine which field matched (si_no, dr_no, or cr_no)
				if ($invoice['Invoice']['si_no'] == $ref_no) {
					$detail['BillingDetail']['matched_field'] = 'si_no';
				} elseif ($invoice['Invoice']['dr_no'] == $ref_no) {
					$detail['BillingDetail']['matched_field'] = 'dr_no';
				} elseif ($invoice['Invoice']['cr_no'] == $ref_no) {
					$detail['BillingDetail']['matched_field'] = 'cr_no';
				}
			} else {
				$detail['Invoice'] = null;
				$detail['BillingDetail']['matched_field'] = null;
			}
			$billDtls[$bd_Id] = $detail;
			
		}
		
		$billingDetails = array_values($billDtls);
		return $billingDetails;
	}


	var $belongsTo = array(
		'Billing' => array(
			'className' => 'Billing',
			'foreignKey' => 'billing_id',
			'conditions' => '',
			'fields' => '',
			'order' => ''
		),
		'Invoice'=> array(
			'className' => 'Invoice',
			'foreignKey' => false,
			'conditions' => array(
				'OR' => array(
					'Invoice.si_no = BillingDetail.ref_no',
					'Invoice.dr_no = BillingDetail.ref_no',
					'Invoice.cr_no = BillingDetail.ref_no'
				)
			),
			'fields' => '',
			'order' => ''
		)
	);


}
