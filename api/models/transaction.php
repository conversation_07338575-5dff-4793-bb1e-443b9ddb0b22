<?php
class Transaction extends AppModel {
	var $name = 'Transaction';
	//The Associations below have been created with all possible keys, those that are not needed can be removed

	var $hasMany = array(
		'TransactionDetail' => array(
			'className' => 'TransactionDetail',
			'foreignKey' => 'transaction_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		),
		'TransactionPayment' => array(
			'className' => 'TransactionPayment',
			'foreignKey' => 'transaction_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		)
	);
	var $belongsTo = array(
		'Customer' => array(
			'className' => 'Customer',
			'foreignKey' => false,
			'conditions' => array("Transaction.entity_id = Customer.id AND Transaction.entity_type = 'customer'"),
			'fields' => array('Customer.id','Customer.name','Customer.alias','Customer.tax_type'),
			'order' => ''
		),
		'Supplier' => array(
			'className' => 'Supplier',
			'foreignKey' => false,
			'conditions' => array("Transaction.entity_id = Supplier.id AND Transaction.entity_type = 'supplier'"),
			'fields' => array('Supplier.id','Supplier.name','Supplier.alias','Supplier.tax_type'),
			'order' => ''
		)
	);
	function updateStatus ($trnxId, $status){
		$trnx =  array('id'=>$trnxId,'status'=>$status);
		$this->save($trnx);
	}
	function findRelated($type,$ref_no){
		$trnxId = null;
		$cond =  compact('type','ref_no');
		$trnx = $this->find('first',array('conditions'=>$cond));
		if($trnx){
			$trnxId =  $trnx['Transaction']['id'];
		}
		return $trnxId;
	}

	function beforeFind($queryData){
		if($cond = $queryData['conditions']):
			$condArch =  array('Transaction.status !='=>'archived');
			array_push($cond,$condArch);
			$queryData['conditions'] =  $cond;
		endif;
		return $queryData;
	}
	
	public function find($conditions = null, $fields = array(), $order = null, $recursive = null) {
		    // Ensure consistent cache key format
		    $cacheKey = $this->generateCacheKey($conditions, $fields, $order, $recursive);
		    
		    // Try to read from the 'inventta' cache
		    $results = Cache::read($cacheKey, 'inventta');
		    
		    if ($results === false) {
		        // If cache is empty, perform the query and write the result to cache
		        $results = parent::find($conditions, $fields, $order, $recursive);
		        Cache::write($cacheKey, $results, 'inventta');
		    }
		    
		    return $results;
		}

    
    private function generateCacheKey($type,$query) {
        return 'transaction_' . md5(serialize($query));
    }
    
    public function afterSave($created, $options = array()) {
        parent::afterSave($created, $options);
        $this->_clearCache();
    }
    
    public function afterDelete() {
        parent::afterDelete();
        $this->_clearCache();
    }
    
    public function _clearCache($type=NULL) {
        // Clear the cache for this model
        Cache::clear(false, 'inventta');
    }
}
