<?php
class Billing extends AppModel {
	var $name = 'Billing';
	var $actsAs = array('Containable');
	var $hasMany = array(
		'BillingDetail' => array(
			'className' => 'BillingDetail',
			'foreignKey' => 'billing_id',
			'dependent' => true,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		)
	);

	var $belongsTo = array(
		'Customer' => array(
			'className' => 'Customer',
			'foreignKey' => 'customer_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => array('status','alias','name','tin','address','business_style','tax_type','unit_system','begin_balance','current_balance'),
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		)
	);

	/**
	 * Generate a unique billing ID in the format RIC-SOA-MMYY-NNNN
	 * where MM is month, YY is year, and NNNN is a sequential number
	 *
	 * @return string The generated billing ID
	 */
	public function generateBillingId() {
		// Get current month and year
		$monthYear = date('my'); // Format: 0125 for January 2025

		// Find the highest existing billing ID for this month/year
		$prefix = "RIC-SOA-{$monthYear}-";
		$conditions = array(
			'Billing.id LIKE' => $prefix . '%'
		);

		$lastBilling = $this->find('first', array(
			'conditions' => $conditions,
			'fields' => array('id'),
			'order' => array('id DESC')
		));

		if (!empty($lastBilling)) {
			// Extract the sequence number from the last billing ID
			$lastId = $lastBilling['Billing']['id'];
			$lastSequence = (int)substr($lastId, -4);
			$newSequence = $lastSequence + 1;
		} else {
			// No existing billing for this month/year, start with 0001
			$newSequence = 1;
		}

		// Format the new sequence number with leading zeros
		$formattedSequence = sprintf('%04d', $newSequence);

		// Combine to create the new billing ID
		$newBillingId = $prefix . $formattedSequence;

		return $newBillingId;
	}

}
