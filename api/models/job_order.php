<?php
class JobOrder extends AppModel {
	var $name = 'JobOrder';
	//The Associations below have been created with all possible keys, those that are not needed can be removed

	var $hasMany = array(
		'JobOrderDetail' => array(
			'className' => 'JobOrderDetail',
			'foreignKey' => 'job_order_id',
			'dependent' => false,
			'conditions' => '',
			'fields' => '',
			'order' => '',
			'limit' => '',
			'offset' => '',
			'exclusive' => '',
			'finderQuery' => '',
			'counterQuery' => ''
		)
	);

	function findByPOID($poid){
		$cond =  array('JobOrder.purchase_order_id'=>$poid);
		$jo = $this->find('first',array('conditions'=>$cond));
		if($jo)
			$jo =  $jo['JobOrder'];
		return $jo;
	}

}
