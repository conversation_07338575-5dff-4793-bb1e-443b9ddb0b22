# Job Order (JO) Process

## Overview
The Job Order (JO) is the second step in the order processing workflow, following the Purchase Order (PO). It represents the production instructions for fulfilling a customer's order. This document outlines the complete process of creating and managing Job Orders in the Inventta system.

## Available Options
* **Select PO to Process** - Choose a Purchase Order to convert into a Job Order
* **Edit Actual Dimensions** - Modify the actual dimensions of the products
* **Delete Items** - Remove items from a Job Order
* **Calculate Cut** - Optimize material usage through cut planning
* **Cancel JO** - Cancel an existing Job Order

## How It Works

### Creating a Job Order

1. Navigate to the Job Order screen (`app/views/orders/job.php`)
2. Select a Purchase Order to process from the dropdown list
3. The system loads the PO details including:
   - Customer information
   - Product list with dimensions and quantities
4. Enter the actual dimensions for each product:
   - Actual width
   - Actual length
   - Product code
5. Click the "Calculate" button to optimize material usage (optional)
6. Review the total number of items
7. Click the confirmation button to finalize the JO
8. In the transaction modal:
   - Review the JO details
   - Add any additional notes
9. Submit the JO

### Editing a Job Order

1. Click the edit button on the JO screen
2. Modify the actual dimensions as needed
3. Click save to update the JO

### Technical Implementation

The Job Order process is implemented using the following components:

#### Frontend (AngularJS)
- **Controller**: `app/controllers/orders/jobController.js`
- **View**: `app/views/orders/job.php`
- **Services**: 
  - `transactionService.js` - Handles API communication
  - `purchaseOrderService.js` - Manages PO data

#### Backend (CakePHP)
- **Model**: `api/models/job_order.php`
- **Controller**: `api/controllers/job_orders_controller.php`
- **Transaction Handling**: `api/controllers/api_controller.php`

## Data Model

### Job Order
```
JobOrder
├── id
├── po_no
├── jo_date
├── purchase_order_id
├── user
├── remarks
├── created
└── modified
```

### Job Order Detail
```
JobOrderDetail
├── id
├── job_order_id
├── product_id
├── length
├── length_actual
├── width
├── width_actual
├── thickness
├── thickness_actual
├── quantity
├── quantity_actual
├── quantity_area
├── quantity_area_actual
├── code_actual
├── created
└── modified
```

### Transaction
```
Transaction
├── id
├── type (value: 'jo')
├── ref_no
├── entity_type (value: 'customer')
├── entity_id
├── po_no
├── po_date
├── purchase_order_id
├── po_trnx_id
├── amount
├── status
├── timestamp
├── created
└── modified
```

## What If / Edge Cases

### What if the actual dimensions exceed the allowable limits?
The system will display a warning (highlighted in red) if the actual dimensions are outside the allowed range. The allowed range is from the original dimension up to the original dimension plus the allowance.

### What if a product code is not assigned?
The system will highlight the missing code in yellow. A product code is required for proper inventory tracking and production management.

### What if the cut calculation needs to be redone?
The cut calculation can be performed multiple times. Each calculation will update the optimization plan for material usage.

## Examples

### Sample Job Order Processing

| Material | Width (Actual) | Length (Actual) | Code |
|----------|---------------|----------------|------|
| Glass 36" x 48" x 1/4" | 36 | 48 | G001 |
| Glass 36" x 48" x 1/4" | 36 | 48 | G002 |
| Glass 36" x 48" x 1/4" | 36 | 48 | G003 |
| Glass 36" x 48" x 1/4" | 36 | 48 | G004 |
| Glass 36" x 48" x 1/4" | 36 | 48 | G005 |
| Aluminum 96" x 1" x 1" | N/A | 96 | A001 |
| Aluminum 96" x 1" x 1" | N/A | 96 | A002 |
| Aluminum 96" x 1" x 1" | N/A | 96 | A003 |
| Aluminum 96" x 1" x 1" | N/A | 96 | A004 |
| Aluminum 96" x 1" x 1" | N/A | 96 | A005 |

### API Request Example
```json
{
  "header": {
    "type": "jo",
    "entity_type": "customer",
    "entity_id": 123,
    "po_no": "PO-2023-001",
    "po_date": "2023-05-15",
    "purchase_order_id": 456,
    "po_trnx_id": 789,
    "jo_date": "2023-05-20",
    "amount": 10,
    "status": "created"
  },
  "details": [
    {
      "product_id": 456,
      "length": 36,
      "length_actual": 36,
      "width": 48,
      "width_actual": 48,
      "quantity": 5,
      "quantity_actual": 5,
      "quantity_area": 60,
      "quantity_area_actual": 60,
      "code_actual": "G001"
    },
    {
      "product_id": 789,
      "length": 96,
      "length_actual": 96,
      "width": 1,
      "width_actual": 1,
      "quantity": 10,
      "quantity_actual": 10,
      "quantity_area": 10,
      "quantity_area_actual": 10,
      "code_actual": "A001"
    }
  ]
}
