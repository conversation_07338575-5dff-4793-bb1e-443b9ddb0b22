# Process Flow

## Overview
This document outlines the complete process flow from Purchase Order (PO) to Job Order (JO) to Sales Invoice (SI) to Billing (SOA) in the Inventta system. This process represents the core business workflow for handling customer orders, production, billing, and payment collection.

## Process Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Purchase Order │     │    Job Order    │     │  Sales Invoice  │     │     Billing     │
│      (PO)       │────▶│      (JO)       │────▶│      (SI)       │────▶│      (SOA)      │
└─────────────────┘     └─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Detailed Process Flow

### 1. Purchase Order (PO) Creation
- Customer places an order for products
- System creates a Purchase Order record
- PO status is set to "created"
- PO details include customer information, product details, quantities, and pricing

### 2. Job Order (JO) Creation
- Production team receives the PO
- System creates a Job Order based on the PO
- JO status is set to "created"
- JO details include the actual dimensions, quantities, and codes for production
- Optional: Cut calculation is performed to optimize material usage

### 3. Sales Invoice (SI) Creation
- Accounting team receives the completed JO
- System creates a Sales Invoice based on the JO
- SI status is set to "created"
- SI details include the final pricing, payment terms, and delivery information
- Payment can be either Cash on Delivery (COD) or charged with terms (usually 30 days)

### 4. Billing (SOA) Creation
- Accounting team consolidates multiple invoices for a customer
- System creates a Statement of Account (SOA) with a unique ID (format: RIC-SOA-MMYY-NNNN)
- SOA includes customer information, list of invoices, due date, and payment terms
- SOA is sent to the customer for payment collection
- Customer makes payment based on the SOA rather than individual invoices

## Transaction Status Flow

Each transaction type (PO, JO, SI, SOA) follows a status flow:

1. **created** - Initial status when the record is created
2. **inprogress** - Status when the record is being processed
3. **completed** - Status when the record is fully processed
4. **cancelled** - Status when the record is cancelled
5. **archived** - Status when the record is archived (no longer active)

## Data Flow

The data flows through the system as follows:

1. **PO Data** → **JO Data**:
   - Product information
   - Customer information
   - Quantities and dimensions
   - Pricing information

2. **JO Data** → **SI Data**:
   - Actual production details
   - Final quantities
   - Product codes
   - Customer information

3. **SI Data** → **SOA Data**:
   - Invoice numbers and dates
   - Invoice amounts
   - Customer information
   - Payment terms

## System Components Involved

### Controllers
- `app/controllers/orders/purchaseController.js`
- `app/controllers/orders/jobController.js`
- `app/controllers/sales/invoiceController.js`
- `app/controllers/accounts/billingController.js`

### Views
- `app/views/orders/purchase.php`
- `app/views/orders/job.php`
- `app/views/sales/invoice.php`
- `app/views/accounts/billing.php`

### Models
- `api/models/purchase_order.php`
- `api/models/job_order.php`
- `api/models/invoice.php`
- `api/models/billing.php`
- `api/models/billing_detail.php`
- `api/models/transaction.php`

### Services
- `app/services/transactionService.js`
- `app/services/customerService.js`
- `app/services/purchaseOrderService.js`
- `app/services/billingService.js`

## Integration Points

The system integrates with:

1. **Customer Management** - For customer information and history
2. **Inventory Management** - For product availability and pricing
3. **Accounting System** - For financial transactions and reporting

## Reporting

The system generates the following reports:

1. **Purchase Order Report** - Details of the PO
2. **Job Order Report** - Details of the JO
3. **Sales Invoice Report** - Details of the SI
4. **Statement of Account (SOA)** - Consolidated billing for a customer
5. **Delivery Receipt** - For product delivery
6. **Collection Receipt** - For payment collection
