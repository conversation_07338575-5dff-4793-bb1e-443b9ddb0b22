# Data Models

## Overview
This document outlines the key data models used in the Inventta system and their relationships. Understanding these models is essential for developers working with the system's API and database.

## Core Transaction Models

### Transaction
The `Transaction` model is the central model that ties together all transaction types (PO, JO, SI) in the system.

```
Transaction
├── id
├── type (values: 'po', 'jo', 'invoice')
├── ref_no
├── entity_type (values: 'customer', 'supplier')
├── entity_id
├── po_no
├── po_date
├── purchase_order_id
├── jo_id
├── si_no
├── dr_no
├── cr_no
├── amount
├── discount
├── discount_percent
├── tax_type
├── vat_amount
├── status
├── payment_status
├── payment_terms
├── timestamp
├── created
└── modified
```

### TransactionDetail
The `TransactionDetail` model stores the line items for each transaction.

```
TransactionDetail
├── id
├── transaction_id
├── product_id
├── length
├── width
├── thickness
├── quantity
├── quantity_area
├── price
├── amount
├── created
└── modified
```

### TransactionPayment
The `TransactionPayment` model stores payment information for transactions.

```
TransactionPayment
├── id
├── transaction_id
├── payment_type
├── payment_amount
├── payment_date
├── reference_no
├── created
└── modified
```

## Purchase Order Models

### PurchaseOrder
The `PurchaseOrder` model stores the header information for purchase orders.

```
PurchaseOrder
├── id
├── po_no
├── po_date
├── customer_id
├── amount
├── status
├── created
└── modified
```

### PurchaseOrderDetail
The `PurchaseOrderDetail` model stores the line items for purchase orders.

```
PurchaseOrderDetail
├── id
├── purchase_order_id
├── product_id
├── length
├── width
├── thickness
├── quantity
├── quantity_area
├── price
├── amount
├── created
└── modified
```

### PurchaseOrderTerm
The `PurchaseOrderTerm` model stores the payment terms for purchase orders.

```
PurchaseOrderTerm
├── id
├── purchase_order_id
├── term_days
├── discount
├── discount_percent
├── created
└── modified
```

## Job Order Models

### JobOrder
The `JobOrder` model stores the header information for job orders.

```
JobOrder
├── id
├── po_no
├── jo_date
├── purchase_order_id
├── user
├── remarks
├── created
└── modified
```

### JobOrderDetail
The `JobOrderDetail` model stores the line items for job orders.

```
JobOrderDetail
├── id
├── job_order_id
├── product_id
├── length
├── length_actual
├── width
├── width_actual
├── thickness
├── thickness_actual
├── quantity
├── quantity_actual
├── quantity_area
├── quantity_area_actual
├── code_actual
├── created
└── modified
```

## Invoice Models

### Invoice
The `Invoice` model stores the header information for invoices.

```
Invoice
├── id
├── si_no
├── invoice_date
├── customer_id
├── po_no
├── po_date
├── purchase_order_id
├── jo_id
├── dr_no
├── cr_no
├── amount
├── discount
├── discount_percent
├── tax_type
├── vat_amount
├── status
├── payment_status
├── payment_terms
├── created
└── modified
```

### InvoiceDetail
The `InvoiceDetail` model stores the line items for invoices.

```
InvoiceDetail
├── id
├── invoice_id
├── product_id
├── description
├── quantity
├── price
├── amount
├── created
└── modified
```

## Supporting Models

### Customer
The `Customer` model stores customer information.

```
Customer
├── id
├── name
├── alias
├── address
├── contact_person
├── contact_number
├── email
├── tax_type
├── status
├── created
└── modified
```

### Product
The `Product` model stores product information.

```
Product
├── id
├── name
├── display_title
├── particular
├── part_no
├── category_id
├── thickness
├── allowance
├── unit
├── price
├── markup
├── discountable
├── status
├── created
└── modified
```

## Model Relationships

### Transaction Relationships
- `Transaction` has many `TransactionDetail`
- `Transaction` has many `TransactionPayment`
- `Transaction` belongs to `Customer` (when entity_type is 'customer')
- `Transaction` belongs to `Supplier` (when entity_type is 'supplier')

### Purchase Order Relationships
- `PurchaseOrder` has many `PurchaseOrderDetail`
- `PurchaseOrder` has many `PurchaseOrderTerm`
- `PurchaseOrder` belongs to `Customer`

### Job Order Relationships
- `JobOrder` has many `JobOrderDetail`
- `JobOrder` belongs to `PurchaseOrder`

### Invoice Relationships
- `Invoice` has many `InvoiceDetail`
- `Invoice` belongs to `Customer`
- `Invoice` belongs to `JobOrder`
- `Invoice` belongs to `PurchaseOrder`

## Database Schema Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│    Customer     │     │  PurchaseOrder  │     │     JobOrder    │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ id              │     │ id              │     │ id              │
│ name            │     │ po_no           │     │ po_no           │
│ ...             │◄────┤ customer_id     │◄────┤ purchase_order_id│
└─────────────────┘     │ ...             │     │ ...             │
                        └─────────────────┘     └─────────────────┘
                               ▲                        ▲
                               │                        │
                               │                        │
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│     Invoice     │     │   Transaction   │     │TransactionDetail│
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ id              │     │ id              │     │ id              │
│ si_no           │     │ type            │     │ transaction_id  │◄─┐
│ customer_id     │◄────┤ entity_id       │     │ product_id      │  │
│ purchase_order_id◄────┤ purchase_order_id     │ ...             │  │
│ jo_id           │◄────┤ jo_id           │     └─────────────────┘  │
│ ...             │     │ ...             │─────────────────────────┘
└─────────────────┘     └─────────────────┘
```
