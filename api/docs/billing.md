# Billing (Statement of Account) Process

## Overview
The Billing module manages Statement of Account (SOA) generation for customers. It represents the consolidated billing document that includes multiple invoices for a customer and is used for payment collection. This document outlines the complete process of creating and managing Billing/SOA in the Inventta system.

## Available Options
* **Create New SOA** - Create a new Statement of Account for a customer
* **View SOA Details** - View the details of an existing Statement of Account
* **Edit SOA** - Modify an existing Statement of Account
* **Delete SOA** - Remove a Statement of Account from the system
* **Generate SOA PDF** - Generate a PDF version of the Statement of Account

## How It Works

### Creating a Statement of Account

1. Navigate to the Billing screen (`app/views/accounts/billing.php`)
2. Select a customer from the dropdown list
3. The system loads the customer information including:
   - Customer name and address
   - Tax information
   - Current balance
4. Add invoice details to be included in the SOA:
   - Reference number (invoice number)
   - Invoice date
   - Invoice amount
5. The system calculates the total due amount
6. Specify payment terms (e.g., "30 days")
7. Set the due date for payment
8. Click the confirmation button to finalize the SOA
9. The system generates a unique SOA ID in the format RIC-SOA-MMYY-NNNN (e.g., RIC-SOA-0125-0001)

### Viewing a Statement of Account

1. Navigate to the Billing screen
2. Use the search function to find the desired SOA by:
   - SOA ID
   - Customer name
3. Click on the SOA to view its details
4. The system displays:
   - SOA header information (ID, customer, due date, amount)
   - List of invoices included in the SOA
   - Payment terms and status

### Editing a Statement of Account

1. Navigate to the Billing screen
2. Find and select the SOA to edit
3. Click the edit button
4. Modify the SOA details as needed:
   - Add or remove invoices
   - Update due date
   - Change payment terms
5. Click save to update the SOA

### Generating SOA PDF

1. Navigate to the Billing screen
2. Find and select the SOA
3. Click the "Generate PDF" button
4. The system generates a PDF document containing:
   - Company header and logo
   - Customer information
   - List of invoices with dates and amounts
   - Total amount due
   - Payment terms and due date

### Technical Implementation

The Billing process is implemented using the following components:

#### Frontend (AngularJS)
- **Controller**: `app/controllers/accounts/billingController.js`
- **View**: `app/views/accounts/billing.php`
- **Services**: 
  - `transactionService.js` - Handles API communication
  - `customerService.js` - Manages customer data

#### Backend (CakePHP)
- **Model**: `api/models/billing.php` and `api/models/billing_detail.php`
- **Controller**: `api/controllers/billings_controller.php`
- **Reports**: `api/views/billings/soa.ctp` (for PDF generation)

## Data Model

### Billing (Statement of Account)
```
Billing
├── id (Format: RIC-SOA-MMYY-NNNN)
├── customer_id
├── due_amount
├── due_date
├── details (Text field for additional information)
├── hash (SHA-256 hash for verification)
├── terms
├── created
├── created_by
└── status
```

### Billing Detail
```
BillingDetail
├── id
├── billing_id
├── ref_no (Reference to invoice number)
├── invoice_date
├── invoice_amount
├── status
├── created
└── modified
```

## API Endpoints

### List Billings
- **URL**: `/api/billings`
- **Method**: GET
- **Parameters**:
  - `keyword` (optional) - Search by SOA ID or customer name
  - `status` (optional) - Filter by status (default: 'active')
  - `limit` (optional) - Number of results per page (default: 10)
- **Response**: JSON array of billing objects with pagination metadata

### View Billing
- **URL**: `/api/billings/view/{id}`
- **Method**: GET
- **Parameters**: None
- **Response**: JSON object with billing details and related billing_details

### Create Billing
- **URL**: `/api/billings/add`
- **Method**: POST
- **Parameters**:
  - `Billing` - Object containing billing header information
  - `BillingDetail` - Array of objects containing invoice details
- **Response**: JSON object with the created billing

### Update Billing
- **URL**: `/api/billings/edit/{id}`
- **Method**: POST
- **Parameters**:
  - `Billing` - Object containing updated billing header information
  - `BillingDetail` - Array of objects containing updated invoice details
- **Response**: JSON object with the updated billing

### Delete Billing
- **URL**: `/api/billings/delete/{id}`
- **Method**: POST
- **Parameters**: None
- **Response**: JSON object with success status

### Generate SOA PDF
- **URL**: `/api/billings/soa/{id}`
- **Method**: GET
- **Parameters**: None
- **Response**: PDF document

## What If / Edge Cases

### What if a customer wants to modify an SOA after it's been created?
If the SOA has not yet been paid, it can be edited. If payment has been received, a new SOA must be created and the original one must be marked as paid.

### What if an invoice needs to be removed from an SOA?
The SOA can be edited to remove the invoice as long as no payment has been applied to the SOA.

### What if the customer disputes an invoice on the SOA?
The disputed invoice can be removed from the SOA, and a new SOA can be created with the corrected information.

### What if partial payment is made?
The system supports tracking partial payments. The SOA status remains "partial" until full payment is received.

## Examples

### Sample SOA Request
```json
{
  "Billing": {
    "customer_id": 123,
    "due_date": "2025-02-15",
    "due_amount": 25000.00,
    "terms": "30 days",
    "details": "Monthly billing for January 2025"
  },
  "BillingDetail": [
    {
      "ref_no": "SI-2025-001",
      "invoice_date": "2025-01-05",
      "invoice_amount": 15000.00
    },
    {
      "ref_no": "SI-2025-008",
      "invoice_date": "2025-01-15",
      "invoice_amount": 10000.00
    }
  ]
}
```

### Sample SOA Response
```json
{
  "status": 0,
  "response": {
    "data": {
      "id": "RIC-SOA-0125-0001",
      "customer_id": 123,
      "due_date": "2025-02-15",
      "due_amount": 25000.00,
      "terms": "30 days",
      "details": "Monthly billing for January 2025",
      "hash": "a1b2c3d4e5f6...",
      "created": "2025-01-20 10:15:30",
      "created_by": "admin",
      "status": "active",
      "details": [
        {
          "id": 1,
          "billing_id": "RIC-SOA-0125-0001",
          "ref_no": "SI-2025-001",
          "invoice_date": "2025-01-05",
          "invoice_amount": 15000.00,
          "status": "active",
          "created": "2025-01-20 10:15:30",
          "modified": "2025-01-20 10:15:30"
        },
        {
          "id": 2,
          "billing_id": "RIC-SOA-0125-0001",
          "ref_no": "SI-2025-008",
          "invoice_date": "2025-01-15",
          "invoice_amount": 10000.00,
          "status": "active",
          "created": "2025-01-20 10:15:30",
          "modified": "2025-01-20 10:15:30"
        }
      ],
      "customer": {
        "id": 123,
        "name": "ABC Corporation",
        "alias": "ABC Corp",
        "tin": "***********-000",
        "address": "123 Main St, Makati City",
        "business_style": "Corporation",
        "tax_type": "vat",
        "unit_system": "metric",
        "begin_balance": 0.00,
        "current_balance": 25000.00,
        "status": "open"
      }
    }
  }
}
```
