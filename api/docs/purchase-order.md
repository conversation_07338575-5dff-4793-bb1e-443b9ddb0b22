# Purchase Order (PO) Process

## Overview
The Purchase Order (PO) is the first step in the order processing workflow. It represents a customer's request to purchase products from the company. This document outlines the complete process of creating and managing Purchase Orders in the Inventta system.

## Available Options
* **Create New PO** - Create a new Purchase Order for a customer
* **Edit PO** - Modify an existing Purchase Order
* **Delete Items** - Remove items from a Purchase Order
* **Sort Items** - Reorder items in a Purchase Order
* **Cancel PO** - Cancel an existing Purchase Order

## How It Works

### Creating a Purchase Order

1. Navigate to the Purchase Order screen (`app/views/orders/purchase.php`)
2. Select a customer using the typeahead search
3. Add products to the PO by selecting them from the product list
4. For each product, specify:
   - Quantity
   - Dimensions (length, width, thickness)
   - Price
5. Review the total amount
6. Click the confirmation button to finalize the PO
7. In the transaction modal:
   - Enter the PO number
   - Specify payment terms (COD or charge)
   - Add any additional notes
8. Submit the PO

### Editing a Purchase Order

1. Click the edit button on the PO screen
2. Modify the product details as needed
3. Click save to update the PO

### Technical Implementation

The Purchase Order process is implemented using the following components:

#### Frontend (AngularJS)
- **Controller**: `app/controllers/orders/purchaseController.js`
- **View**: `app/views/orders/purchase.php`
- **Services**: 
  - `transactionService.js` - Handles API communication
  - `customerService.js` - Manages customer data
  - `productService.js` - Manages product data

#### Backend (CakePHP)
- **Model**: `api/models/purchase_order.php`
- **Controller**: `api/controllers/purchase_orders_controller.php`
- **Transaction Handling**: `api/controllers/api_controller.php`

## Data Model

### Purchase Order
```
PurchaseOrder
├── id
├── po_no
├── po_date
├── customer_id
├── amount
├── status
├── created
└── modified
```

### Purchase Order Detail
```
PurchaseOrderDetail
├── id
├── purchase_order_id
├── product_id
├── length
├── width
├── thickness
├── quantity
├── quantity_area
├── price
├── amount
├── created
└── modified
```

### Transaction
```
Transaction
├── id
├── type (value: 'po')
├── ref_no
├── entity_type (value: 'customer')
├── entity_id
├── po_no
├── po_date
├── amount
├── status
├── timestamp
├── created
└── modified
```

## What If / Edge Cases

### What if a customer wants to modify a PO after it's been created?
If the PO has not yet been processed into a Job Order, it can be edited. If a Job Order has already been created, a new PO must be created and the original one must be cancelled.

### What if a product is no longer available?
The system will display a warning when trying to add an unavailable product. The user can either choose a different product or proceed with a backorder.

### What if the customer wants a discount?
Discounts can be applied at the PO level. The system enforces a maximum discount limit (50% of markup) to ensure profitability.

## Examples

### Sample Purchase Order Computation

| Item | Dimensions | Quantity | Price | Amount |
|------|------------|----------|-------|--------|
| Glass | 36" x 48" x 1/4" | 5 | ₱1,200.00 | ₱6,000.00 |
| Aluminum | 96" x 1" x 1" | 10 | ₱500.00 | ₱5,000.00 |
| **Total** | | | | **₱11,000.00** |

### API Request Example
```json
{
  "header": {
    "type": "po",
    "entity_type": "customer",
    "entity_id": 123,
    "po_no": "PO-2023-001",
    "po_date": "2023-05-15",
    "amount": 11000,
    "status": "created"
  },
  "details": [
    {
      "product_id": 456,
      "length": 36,
      "width": 48,
      "thickness": 0.25,
      "quantity": 5,
      "quantity_area": 60,
      "price": 1200,
      "amount": 6000
    },
    {
      "product_id": 789,
      "length": 96,
      "width": 1,
      "thickness": 1,
      "quantity": 10,
      "quantity_area": 10,
      "price": 500,
      "amount": 5000
    }
  ]
}
