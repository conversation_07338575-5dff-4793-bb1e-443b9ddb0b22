# Inventta System Documentation

## Overview
This documentation outlines the core business processes and system architecture of the Inventta system. The system is built using CakePHP 1.3 for the backend API and AngularJS for the frontend.

## Process Flow
The main business process flow in the system follows these steps:

1. **Purchase Order (PO)** - Customer places an order for products
2. **Job Order (JO)** - Production team processes the order
3. **Sales Invoice (SI)** - Accounting generates an invoice for the completed job
4. **Billing (SOA)** - Accounting generates a Statement of Account for the customer

## Documentation Structure
This documentation is organized into the following sections:

- [Process Flow](process-flow.md) - Detailed explanation of the overall process flow
- [Purchase Order](purchase-order.md) - Documentation for the Purchase Order process
- [Job Order](job-order.md) - Documentation for the Job Order process
- [Invoice](invoice.md) - Documentation for the Invoice process
- [Billing](billing.md) - Documentation for the Billing/Statement of Account process
- [Data Models](data-models.md) - Documentation of the data models and relationships

## Technical Stack
- **Backend**: CakePHP 1.3
- **Frontend**: AngularJS
- **Database**: MySQL

## API Structure
The API is built using CakePHP 1.3 and follows RESTful principles. The main endpoints are:

- `/transactions` - Handles all transaction-related operations
- `/purchase_orders` - Manages purchase orders
- `/job_orders` - Manages job orders
- `/invoices` - Manages invoices
- `/billings` - Manages billing statements (SOA)

## Frontend Structure
The frontend is built using AngularJS and follows a modular approach:

- `app/controllers` - Contains all the controllers
- `app/views` - Contains all the views
- `app/services` - Contains all the services
