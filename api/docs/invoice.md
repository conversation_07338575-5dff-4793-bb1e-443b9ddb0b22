# Sales Invoice (SI) Process

## Overview
The Sales Invoice (SI) is the final step in the order processing workflow, following the Job Order (JO). It represents the billing document for the customer and is used for payment collection. This document outlines the complete process of creating and managing Sales Invoices in the Inventta system.

## Available Options
* **Select JO to Process** - Choose a Job Order to convert into a Sales Invoice
* **Edit Pricing** - Modify the pricing of the products
* **Delete Items** - Remove items from a Sales Invoice
* **Payment Terms** - Specify payment terms (COD or charge with terms)
* **Cancel SI** - Cancel an existing Sales Invoice

## How It Works

### Creating a Sales Invoice

1. Navigate to the Sales Invoice screen (`app/views/sales/invoice.php`)
2. Select a Job Order to process from the dropdown list
3. The system loads the JO details including:
   - Customer information
   - Product list with dimensions, quantities, and codes
   - Pricing information from the original PO
4. Review and optionally edit the pricing for each product
5. Review the total amount
6. Click the confirmation button to finalize the SI
7. In the transaction modal:
   - Specify payment terms (COD or charge with terms)
   - Enter the SI number (optional)
   - Enter the DR (Delivery Receipt) number (optional)
   - Enter the CR (Collection Receipt) number (if payment is COD)
   - Add any additional notes
8. Submit the SI

### Editing a Sales Invoice

1. Click the edit button on the SI screen
2. Modify the pricing as needed
3. Click save to update the SI

### Technical Implementation

The Sales Invoice process is implemented using the following components:

#### Frontend (AngularJS)
- **Controller**: `app/controllers/sales/invoiceController.js`
- **View**: `app/views/sales/invoice.php`
- **Services**: 
  - `transactionService.js` - Handles API communication
  - `customerService.js` - Manages customer data

#### Backend (CakePHP)
- **Model**: `api/models/invoice.php`
- **Controller**: `api/controllers/invoices_controller.php`
- **Transaction Handling**: `api/controllers/api_controller.php`
- **Reports**: `api/views/reports/invoice.ctp`

## Data Model

### Invoice
```
Invoice
├── id
├── si_no
├── invoice_date
├── customer_id
├── po_no
├── po_date
├── purchase_order_id
├── jo_id
├── dr_no
├── cr_no
├── amount
├── discount
├── discount_percent
├── tax_type
├── vat_amount
├── status
├── payment_status
├── payment_terms
├── created
└── modified
```

### Invoice Detail
```
InvoiceDetail
├── id
├── invoice_id
├── product_id
├── description
├── quantity
├── price
├── amount
├── created
└── modified
```

### Transaction
```
Transaction
├── id
├── type (value: 'invoice')
├── ref_no
├── entity_type (value: 'customer')
├── entity_id
├── po_no
├── po_date
├── purchase_order_id
├── jo_trnx_id
├── si_no
├── dr_no
├── cr_no
├── amount
├── discount
├── discount_percent
├── tax_type
├── vat_amount
├── status
├── payment_status
├── payment_terms
├── timestamp
├── created
└── modified
```

## What If / Edge Cases

### What if the customer wants a different payment term than usual?
The payment terms can be adjusted during the SI creation process. The default is typically 30 days for charge accounts, but this can be modified as needed.

### What if a partial payment is made?
The system supports partial payments. The payment status will be updated to "partial" and the remaining balance will be tracked.

### What if the invoice needs to be cancelled after creation?
An invoice can be cancelled if it has not been paid. If it has been partially or fully paid, a credit memo must be created to reverse the transaction.

### What if tax calculations are required?
The system supports different tax types (VAT, Non-VAT, etc.) based on the customer's tax status. The appropriate tax calculations are applied automatically.

## Examples

### Sample Sales Invoice Computation

| Material | Price | Quantity | Amount |
|----------|-------|----------|--------|
| Glass 36" x 48" x 1/4" | ₱1,200.00 | 5 | ₱6,000.00 |
| Aluminum 96" x 1" x 1" | ₱500.00 | 10 | ₱5,000.00 |
| **Subtotal** | | | **₱11,000.00** |
| **VAT (12%)** | | | **₱1,320.00** |
| **Total** | | | **₱12,320.00** |

### API Request Example
```json
{
  "header": {
    "type": "invoice",
    "entity_type": "customer",
    "entity_id": 123,
    "po_no": "PO-2023-001",
    "po_date": "2023-05-15",
    "purchase_order_id": 456,
    "jo_trnx_id": 789,
    "invoice_date": "2023-05-25",
    "si_no": "SI-2023-001",
    "dr_no": "DR-2023-001",
    "amount": 11000,
    "tax_type": "vat",
    "vat_amount": 1320,
    "payment_terms": 30,
    "payment_status": "unpaid",
    "status": "created"
  },
  "details": [
    {
      "product_id": 456,
      "description": "Glass 36\" x 48\" x 1/4\"",
      "quantity": 5,
      "price": 1200,
      "amount": 6000
    },
    {
      "product_id": 789,
      "description": "Aluminum 96\" x 1\" x 1\"",
      "quantity": 10,
      "price": 500,
      "amount": 5000
    }
  ]
}
