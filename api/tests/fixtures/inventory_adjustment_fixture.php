<?php
/* InventoryAdjustment Fixture generated on: 2015-06-14 23:56:35 : 1434326195 */
class InventoryAdjustmentFixture extends CakeTestFixture {
	var $name = 'InventoryAdjustment';

	var $fields = array(
		'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'primary'),
		'product_id' => array('type' => 'integer', 'null' => true, 'default' => NULL),
		'tmp_quantity' => array('type' => 'integer', 'null' => true, 'default' => NULL),
		'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
		'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1)),
		'tableParameters' => array('charset' => 'latin1', 'collate' => 'latin1_swedish_ci', 'engine' => 'InnoDB')
	);

	var $records = array(
		array(
			'id' => 1,
			'product_id' => 1,
			'tmp_quantity' => 1,
			'created' => '2015-06-14 23:56:35'
		),
	);
}
