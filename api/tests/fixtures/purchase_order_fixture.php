<?php
/* PurchaseOrder Fixture generated on: 2021-04-17 15:57:27 : 1618667847 */
class PurchaseOrderFixture extends CakeTestFixture {
	var $name = 'PurchaseOrder';

	var $fields = array(
		'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'primary'),
		'po_no' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 20, 'collate' => 'latin1_swedish_ci', 'charset' => 'latin1'),
		'po_date' => array('type' => 'date', 'null' => true, 'default' => NULL),
		'customer' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 80, 'collate' => 'latin1_swedish_ci', 'charset' => 'latin1'),
		'total' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => '10,2'),
		'commission' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => '10,2'),
		'discount' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => '10,2'),
		'tax' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => '10,2'),
		'interest' => array('type' => 'float', 'null' => true, 'default' => NULL, 'length' => '10,2'),
		'created' => array('type' => 'date', 'null' => true, 'default' => NULL),
		'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1)),
		'tableParameters' => array('charset' => 'latin1', 'collate' => 'latin1_swedish_ci', 'engine' => 'InnoDB')
	);

	var $records = array(
		array(
			'id' => 1,
			'po_no' => 'Lorem ipsum dolor ',
			'po_date' => '2021-04-17',
			'customer' => 'Lorem ipsum dolor sit amet',
			'total' => 1,
			'commission' => 1,
			'discount' => 1,
			'tax' => 1,
			'interest' => 1,
			'created' => '2021-04-17'
		),
	);
}
