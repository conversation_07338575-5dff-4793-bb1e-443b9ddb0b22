<?php
/* PurchaseOrderDetail Fixture generated on: 2021-04-17 15:58:11 : 1618667891 */
class PurchaseOrderDetailFixture extends CakeTestFixture {
	var $name = 'PurchaseOrderDetail';

	var $fields = array(
		'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'primary'),
		'purchase_order_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'product_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'quantity' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'price' => array('type' => 'float', 'null' => false, 'default' => NULL, 'length' => '8,2'),
		'amount' => array('type' => 'float', 'null' => false, 'default' => NULL, 'length' => '10,2'),
		'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1)),
		'tableParameters' => array('charset' => 'latin1', 'collate' => 'latin1_swedish_ci', 'engine' => 'InnoDB')
	);

	var $records = array(
		array(
			'id' => 1,
			'purchase_order_id' => 1,
			'product_id' => 1,
			'quantity' => 1,
			'price' => 1,
			'amount' => 1
		),
	);
}
