<?php
/* JobOrderDetail Fixture generated on: 2021-04-17 19:16:35 : 1618679795 */
class JobOrderDetailFixture extends CakeTestFixture {
	var $name = 'JobOrderDetail';

	var $fields = array(
		'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'primary'),
		'job_order_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'product_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'length' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'length_actual' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'width' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'width_actual' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'thickness' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'thickness_actual' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'quantity' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'quatity_actual' => array('type' => 'integer', 'null' => false, 'default' => NULL),
		'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1)),
		'tableParameters' => array('charset' => 'latin1', 'collate' => 'latin1_swedish_ci', 'engine' => 'InnoDB')
	);

	var $records = array(
		array(
			'id' => 1,
			'job_order_id' => 1,
			'product_id' => 1,
			'length' => 1,
			'length_actual' => 1,
			'width' => 1,
			'width_actual' => 1,
			'thickness' => 1,
			'thickness_actual' => 1,
			'quantity' => 1,
			'quatity_actual' => 1
		),
	);
}
