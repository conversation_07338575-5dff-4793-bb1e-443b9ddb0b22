<?php
/* JobOrderDetail Test cases generated on: 2021-04-17 19:16:35 : 1618679795*/
App::import('Model', 'JobOrderDetail');

class JobOrderDetailTestCase extends CakeTestCase {
	var $fixtures = array('app.job_order_detail', 'app.job_order', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->JobOrderDetail =& ClassRegistry::init('JobOrderDetail');
	}

	function endTest() {
		unset($this->JobOrderDetail);
		ClassRegistry::flush();
	}

}
