<?php
/* ProductSpec Test cases generated on: 2021-05-27 02:16:13 : 1622074573*/
App::import('Model', 'ProductSpec');

class ProductSpecTestCase extends CakeTestCase {
	var $fixtures = array('app.product_spec', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->ProductSpec =& ClassRegistry::init('ProductSpec');
	}

	function endTest() {
		unset($this->ProductSpec);
		ClassRegistry::flush();
	}

}
