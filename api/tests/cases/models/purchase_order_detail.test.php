<?php
/* PurchaseOrderDetail Test cases generated on: 2021-04-17 15:58:11 : 1618667891*/
App::import('Model', 'PurchaseOrderDetail');

class PurchaseOrderDetailTestCase extends CakeTestCase {
	var $fixtures = array('app.purchase_order_detail', 'app.purchase_order', 'app.purchase_order_term', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->PurchaseOrderDetail =& ClassRegistry::init('PurchaseOrderDetail');
	}

	function endTest() {
		unset($this->PurchaseOrderDetail);
		ClassRegistry::flush();
	}

}
