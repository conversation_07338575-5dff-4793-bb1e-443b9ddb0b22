<?php
/* ProductStock Test cases generated on: 2021-05-27 02:21:30 : 1622074890*/
App::import('Model', 'ProductStock');

class ProductStockTestCase extends CakeTestCase {
	var $fixtures = array('app.product_stock', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->ProductStock =& ClassRegistry::init('ProductStock');
	}

	function endTest() {
		unset($this->ProductStock);
		ClassRegistry::flush();
	}

}
