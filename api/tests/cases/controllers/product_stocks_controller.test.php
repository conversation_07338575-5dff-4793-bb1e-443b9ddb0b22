<?php
/* ProductStocks Test cases generated on: 2021-05-27 02:21:49 : 1622074909*/
App::import('Controller', 'ProductStocks');

class TestProductStocksController extends ProductStocksController {
	var $autoRender = false;

	function redirect($url, $status = null, $exit = true) {
		$this->redirectUrl = $url;
	}
}

class ProductStocksControllerTestCase extends CakeTestCase {
	var $fixtures = array('app.product_stock', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->ProductStocks =& new TestProductStocksController();
		$this->ProductStocks->constructClasses();
	}

	function endTest() {
		unset($this->ProductStocks);
		ClassRegistry::flush();
	}

	function testIndex() {

	}

	function testView() {

	}

	function testAdd() {

	}

	function testEdit() {

	}

	function testDelete() {

	}

}
