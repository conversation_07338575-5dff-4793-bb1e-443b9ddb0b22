<?php
/* JobOrderDetails Test cases generated on: 2021-04-17 19:17:24 : 1618679844*/
App::import('Controller', 'JobOrderDetails');

class TestJobOrderDetailsController extends JobOrderDetailsController {
	var $autoRender = false;

	function redirect($url, $status = null, $exit = true) {
		$this->redirectUrl = $url;
	}
}

class JobOrderDetailsControllerTestCase extends CakeTestCase {
	var $fixtures = array('app.job_order_detail', 'app.job_order', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->JobOrderDetails =& new TestJobOrderDetailsController();
		$this->JobOrderDetails->constructClasses();
	}

	function endTest() {
		unset($this->JobOrderDetails);
		ClassRegistry::flush();
	}

	function testIndex() {

	}

	function testView() {

	}

	function testAdd() {

	}

	function testEdit() {

	}

	function testDelete() {

	}

}
