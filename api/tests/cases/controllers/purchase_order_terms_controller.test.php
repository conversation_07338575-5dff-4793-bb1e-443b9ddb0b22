<?php
/* PurchaseOrderTerms Test cases generated on: 2021-04-17 15:59:18 : 1618667958*/
App::import('Controller', 'PurchaseOrderTerms');

class TestPurchaseOrderTermsController extends PurchaseOrderTermsController {
	var $autoRender = false;

	function redirect($url, $status = null, $exit = true) {
		$this->redirectUrl = $url;
	}
}

class PurchaseOrderTermsControllerTestCase extends CakeTestCase {
	var $fixtures = array('app.purchase_order_term', 'app.purchase_order', 'app.purchase_order_detail', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->PurchaseOrderTerms =& new TestPurchaseOrderTermsController();
		$this->PurchaseOrderTerms->constructClasses();
	}

	function endTest() {
		unset($this->PurchaseOrderTerms);
		ClassRegistry::flush();
	}

	function testIndex() {

	}

	function testView() {

	}

	function testAdd() {

	}

	function testEdit() {

	}

	function testDelete() {

	}

}
