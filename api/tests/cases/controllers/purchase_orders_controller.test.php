<?php
/* PurchaseOrders Test cases generated on: 2021-04-17 15:59:03 : 1618667943*/
App::import('Controller', 'PurchaseOrders');

class TestPurchaseOrdersController extends PurchaseOrdersController {
	var $autoRender = false;

	function redirect($url, $status = null, $exit = true) {
		$this->redirectUrl = $url;
	}
}

class PurchaseOrdersControllerTestCase extends CakeTestCase {
	var $fixtures = array('app.purchase_order', 'app.purchase_order_detail', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order', 'app.purchase_order_term');

	function startTest() {
		$this->PurchaseOrders =& new TestPurchaseOrdersController();
		$this->PurchaseOrders->constructClasses();
	}

	function endTest() {
		unset($this->PurchaseOrders);
		ClassRegistry::flush();
	}

	function testIndex() {

	}

	function testView() {

	}

	function testAdd() {

	}

	function testEdit() {

	}

	function testDelete() {

	}

}
