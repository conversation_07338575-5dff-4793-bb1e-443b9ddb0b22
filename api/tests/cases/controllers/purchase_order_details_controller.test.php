<?php
/* PurchaseOrderDetails Test cases generated on: 2021-04-17 15:59:27 : 1618667967*/
App::import('Controller', 'PurchaseOrderDetails');

class TestPurchaseOrderDetailsController extends PurchaseOrderDetailsController {
	var $autoRender = false;

	function redirect($url, $status = null, $exit = true) {
		$this->redirectUrl = $url;
	}
}

class PurchaseOrderDetailsControllerTestCase extends CakeTestCase {
	var $fixtures = array('app.purchase_order_detail', 'app.purchase_order', 'app.purchase_order_term', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->PurchaseOrderDetails =& new TestPurchaseOrderDetailsController();
		$this->PurchaseOrderDetails->constructClasses();
	}

	function endTest() {
		unset($this->PurchaseOrderDetails);
		ClassRegistry::flush();
	}

	function testIndex() {

	}

	function testView() {

	}

	function testAdd() {

	}

	function testEdit() {

	}

	function testDelete() {

	}

}
