<?php
/* JobOrders Test cases generated on: 2021-04-17 19:17:01 : 1618679821*/
App::import('Controller', 'JobOrders');

class TestJobOrdersController extends JobOrdersController {
	var $autoRender = false;

	function redirect($url, $status = null, $exit = true) {
		$this->redirectUrl = $url;
	}
}

class JobOrdersControllerTestCase extends CakeTestCase {
	var $fixtures = array('app.job_order', 'app.job_order_detail', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->JobOrders =& new TestJobOrdersController();
		$this->JobOrders->constructClasses();
	}

	function endTest() {
		unset($this->JobOrders);
		ClassRegistry::flush();
	}

	function testIndex() {

	}

	function testView() {

	}

	function testAdd() {

	}

	function testEdit() {

	}

	function testDelete() {

	}

}
