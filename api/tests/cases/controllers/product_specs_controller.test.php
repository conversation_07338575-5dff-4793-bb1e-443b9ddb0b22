<?php
/* ProductSpecs Test cases generated on: 2021-05-27 02:16:46 : 1622074606*/
App::import('Controller', 'ProductSpecs');

class TestProductSpecsController extends ProductSpecsController {
	var $autoRender = false;

	function redirect($url, $status = null, $exit = true) {
		$this->redirectUrl = $url;
	}
}

class ProductSpecsControllerTestCase extends CakeTestCase {
	var $fixtures = array('app.product_spec', 'app.product', 'app.category', 'app.delivery_detail', 'app.delivery', 'app.delivery_payment', 'app.inventory_log', 'app.price_log', 'app.inventory_adjustment', 'app.invoice_detail', 'app.invoice', 'app.invoice_payment', 'app.order_detail', 'app.order');

	function startTest() {
		$this->ProductSpecs =& new TestProductSpecsController();
		$this->ProductSpecs->constructClasses();
	}

	function endTest() {
		unset($this->ProductSpecs);
		ClassRegistry::flush();
	}

	function testIndex() {

	}

	function testView() {

	}

	function testAdd() {

	}

	function testEdit() {

	}

	function testDelete() {

	}

}
