<?php
	require (APP.'vendors'.DS.'fpdf17/formsheet.php');
	require(APP.'vendors'.DS.'/fpdf17/barcode/php-barcode.php');
	class SOAReport extends Formsheet{
		var $billing_data = null;
		var $customer_data = null;
		var $billing_details = null;
		var $ledger_entries = null;
		var $soa_no = null;
		var $customer_name = null;
		var $customer_address = null;
		var $customer_tin = null;
		var $customer_id = null;
		var $due_date = null;
		var $previous_balance = null;
		var $outstanding_balance = null;
		var $total_due = 0;

		function __construct($data = null){
			//$this->showLines = true;
			$this->_width=8.5;
			$this->_height=11;
			$this->_unit = 'in';
			$this->_orient='P';

			if ($data) {
				$this->setBillingData($data);
			}

			parent::__construct();
		}

		function setBillingData($data) {
			if (isset($data['Billing'])) {
				$this->billing_data = $data['Billing'];
				$this->soa_no = $data['Billing']['id'];
				$this->due_date = $data['Billing']['due_date'];
				$this->terms = $data['Billing']['terms'];
				$this->previous_balance = $data['Billing']['previous_balance'];
				$this->outstanding_balance = $data['Billing']['outstanding_balance'];
				$this->total_due = $data['Billing']['outstanding_balance'];
			}

			if (isset($data['Customer'])) {
				
				$this->customer_data = $data['Customer'];
				$this->account_no = $data['Customer']['account_no'];
				$this->customer_name = $data['Customer']['name'];
				$this->customer_address = $data['Customer']['address'];
				$this->customer_tin = $data['Customer']['tin'];
				$this->customer_id = $data['Customer']['alias'];
			}

			if (isset($data['BillingDetail'])) {
				$this->billing_details = $data['BillingDetail'];
			}

			if (isset($data['LedgerEntries'])) {
				$this->ledger_entries = $data['LedgerEntries'];
			}
		}

		function letterHead(){
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>8.5,
					'height'=>1.5,
					'rows'=>8,
					'cols'=>15,
			);

			$this->section($metrics);
			$path = __DIR__ .'/images'.DS.'logo.png';
			$x = 1;
			$y =1.25;
			$w = 2;
			$h = 0.5;
			$this->DrawImage($x,$y,$w,$h,$path);
			$this->GRID['font_size']=7.5;
			$this->leftText(1,4.75,'Purok 1, Brgy. San Antonio, Santo Tomas, Batangas','','i');
			$this->leftText(1,5.5,'0997-6440-5068 | 0917-3230723 | <EMAIL>','','i');


			$this->setFillColor(169,2,1);
			$this->DrawBox(10.5,0.6,4.5,1.75,'F');
			$this->setTextColor(255,255,255);
			$this->GRID['font_size']=10;
			$this->leftText(10.8,1.75,'STATEMENT OF ACCOUNT','','b');
			$this->setTextColor(0,0,0);
			$billX = 6;
			$billW = 4;
			$billY = 2.75;
			$this->GRID['font_size']=8;
			$this->rightText($billX,$billY,'BILL TO:',$billW,'');

			$vendor = $this->customer_name;
			$address = $this->customer_address;
			$tin = $this->customer_tin;
			$billY+=1;
			$this->GRID['font_size']=10;
			$this->rightText($billX,$billY,$vendor,$billW,'b');
			$billY+=0.75;
			$this->GRID['font_size']=9;
			$this->rightText($billX,$billY,$address,$billW,'');



		}

		function dueDetails(){
			$metrics = array(
					'base_x'=>0,
					'base_y'=>1.35,
					'width'=>8.5,
					'height'=>1.5,
					'rows'=>8,
					'cols'=>15,
			);
			$this->showLines=!true;
			$this->section($metrics);
			$billX = 6;
			$billW = 4;
			$ref_no = $this->soa_no;
			$this->GRID['font_size']=9.5;
			$acctNo = $this->account_no;
			$this->leftText(10,-0.25,'ACCOUNT NO.:',1,'');
			$this->rightText($billX,-1,$acctNo,$billW,'b');

			$this->leftText(10,0.5,'SOA NO.:',1,'');
			$this->rightText($billX,-0.125,$ref_no,$billW,'b');


			$this->GRID['font_size']=9.5;
			$this->leftText(1,2,'DUE INVOICES',1,'b');
			$this->GRID['font_size']=7.5;
			$this->centerText(0.75,3,'DATE',2,'b');
			$this->centerText(2.5,3,'REF NO.',2,'b');
			$this->centerText(6.5,3,'AMOUNT (PHP)',2,'b');
			$this->GRID['font_size']=9.5;
			$details = $this->billing_details;
			$detailY = 4;
			$this->setFillColor(252,247,218);
			$boxX = 0.85;
			$boxW = 7.25;
			$boxH = 1;
			$invoiceTotal  = 0 ;
			foreach($details as $dtl){
				$this->DrawBox($boxX,$detailY-0.75,$boxW,$boxH,'F');
				$this->centerText(0.75,$detailY,date('Y-M-d', strtotime($dtl['invoice_date'])),2,'');
				$this->centerText(2.5,$detailY,$dtl['ref_no'],2,'');
				$this->centerText(6.5,$detailY,number_format($dtl['invoice_amount'], 2, '.', ','),2,'');
				$detailY++;
				$invoiceTotal += $dtl['invoice_amount'];
			}
			$detailY+=0.25;
			$this->centerText(0.75,$detailY,'*** Nothing follows ***',8,'i');
			$detailY+=1;

			$this->leftText(1,$detailY,'Total Invoice Due',2,'b');
			$this->rightText(7.125,$detailY-0.75,number_format($invoiceTotal, 2, '.', ','),0.5,'');
			
			$detailY++;
			$this->leftText(1,$detailY,'Previous Balance',2,'b');
			$this->rightText(7.125,$detailY-0.75,number_format($this->previous_balance, 2, '.', ','),0.5,'');
			
			$detailY++;
			$this->leftText(1,$detailY,'Outstanding Balance',2,'b');
			$this->rightText(7.125,$detailY-0.75,number_format($this->outstanding_balance, 2, '.', ','),0.5,'b');

			$boxX = 9;
			$boxY = 1.25;
			$boxW = 5;
			$boxH = 7;
			$this->setFillColor(252,247,218);
			$this->DrawBox($boxX,$boxY,$boxW,$boxH,'F');
			$this->DrawBox($boxX,$boxY,$boxW,$boxH,'D');
			$this->GRID['font_size']=7.5;
			$this->leftText(9.25,2.5,'TERMS:',2,'');
			$this->rightText(9.25,1.75,'DUE DATE:',2.25,'');
			//$this->leftText(9.25,5.25,'PREVIOUS:',2,'');
			$this->leftText(11.5,5.25,'OUTSTANDING BALANCE:',2,'');
			$this->GRID['font_size']=12;
			$this->leftText(9.25,3.5,$this->terms,2,'b');
			$this->rightText(9.25,2.75,date('d M Y', strtotime($this->due_date)),2.25,'b');
			//$this->GRID['font_size']=10;
			//$this->leftText(9.25,6.25,'P'.number_format($this->previous_balance, 2, '.', ','),2,'');
			$this->GRID['font_size']=15;
			$this->rightText(9.25,5.75,'P '.number_format($this->outstanding_balance, 2, '.', ','),2.3,'b');
			$this->GRID['font_size']=7.5;
			$reminder ='Kindly pay the indicated due on time to ensure continuous delivery of quality service. Thank you for your support.';

			$this->wrapText(9,8.5,$reminder,5.5,'l',0.75);

		}

		function ledgerDetails(){
			$metrics = array(
					'base_x'=>0,
					'base_y'=>3.25,
					'width'=>8.5,
					'height'=>1.5,
					'rows'=>8,
					'cols'=>15,
			);
			$this->showLines=!true;
			$this->section($metrics);

			$path = __DIR__ .'/images'.DS.'watermark.png';
			$x = 4;
			$y =1;
			$w = 4.5;
			$h =4.5;
			$this->DrawImage($x,$y,$w,$h,$path);
			$billX = 6;
			$billW = 4;

			$chargeX = 7;
			$payX = 9.5;
			$balX = 12.25;
			$this->GRID['font_size']=9.5;
			$this->leftText(1,1.5,'LEDGER',1,'b');
			$this->GRID['font_size']=7.5;
			$this->centerText(0.75,2.5,'DATE',2,'b');
			$this->centerText(2.5,2.5,'REF NO.',2,'b');
			$this->centerText(4.5,2.5,'REMARKS',2,'b');
			$this->centerText($chargeX,2.5,'CHARGES',2,'b');
			$this->centerText($payX,2.5,'PAYMENT',2,'b');
			$this->centerText($balX,2.5,'BALANCE',2,'b');
			$this->GRID['font_size']=9.5;

			$detailY = 3.75;
			$boxH = 1; // Define box height

			// Use ledger entries if available, otherwise show a message
			if (!empty($this->ledger_entries)) {
				foreach($this->ledger_entries as $entry) {

					// Format date
					$formattedDate = date('d M Y', strtotime($entry['date']));

					// Format amounts
					$chargeAmount = $entry['charge'] > 0 ? number_format($entry['charge'], 2, '.', ',') : '';
					$paymentAmount = $entry['payment'] > 0 ? number_format($entry['payment'], 2, '.', ',') : '';
					$balanceAmount = number_format($entry['balance'], 2, '.', ',');

					// Display ledger entry
					$this->centerText(0.75, $detailY, $formattedDate, 2, '');
					$this->centerText(2.5, $detailY, $entry['ref_no'], 2, '');
					$this->centerText(4.5, $detailY, $entry['remarks'], 2, '');
					$this->rightText(5.5, $detailY-0.75, $chargeAmount, 1.5, '');
					$this->centerText($payX, $detailY, $paymentAmount, 2, '');
					$this->rightText(10.8, $detailY-0.75, $balanceAmount, 1.5, '');

					$detailY++;

					// Limit to 10 entries to fit on the page
					if ($detailY > 13) {
						break;
					}
				}
			} else {
				// No ledger entries available
				$this->DrawBox(0.5,$detailY-0.75,13,$boxH,'F');
				$this->centerText(6.5, $detailY, 'No ledger entries available', 2, 'i');
				$detailY++;
			}
			$detailY+=0.5;
			$this->centerText(0.75,$detailY,'*** Nothing follows ***',13,'');
		}

		function paymentInstructions(){
			$metrics = array(
					'base_x'=>0,
					'base_y'=>9,
					'width'=>8.5,
					'height'=>1.5,
					'rows'=>8,
					'cols'=>15,
			);
			$this->showLines=!true;
			$this->section($metrics);

			$boxX = 1;
			$boxY = 0;
			$boxW = 13;
			$boxH = 7;
			$this->DrawBox($boxX,$boxY,$boxW,$boxH,'D');
			$boxW = 4;
			$this->DrawBox($boxX,$boxY,$boxW,$boxH,'D');
			$boxW = 2.5;
			$this->DrawBox($boxX+10.5,$boxY,$boxW,$boxH,'D');
			$boxW = 4;
			$this->GRID['font_size']=7.5;
			$boxX = $boxX + 0.25;
			$boxY++;
			$this->leftText($boxX,$boxY,'SOA NO.:',2,'');
			$this->leftText($boxX,$boxY+0.75,'AMOUNT DUE:',1.5,'');
			$this->rightText($boxX+0.65,$boxY-0.75,$this->soa_no,1.5,'b');
			$this->GRID['font_size']=10;
			$this->rightText($boxX+0.65,$boxY+0.125,'P'.number_format($this->outstanding_balance, 2, '.', ','),1.5,'b');

			$this->GRID['font_size']=7.5;
			$reminder = 'To maintain good standing of your account kindly settle on or before the due date. ';
			$boxY +=3;
			$this->wrapText($boxX-0.125,$boxY+1,$reminder,$boxW-0.25,'l',0.7);

			$this->GRID['font_size']=9;
			$boxX = 5.125;
			$boxY = 1;
			$this->leftText($boxX,$boxY,'Payment Instructions:',2,'b');
			$this->GRID['font_size']=8.5;
			$boxY+=1.25;
			$this->leftText($boxX,$boxY,'Make all checks payable to:',2,'');
			$this->leftText($boxX+2.6,$boxY,'Railim Industrial Corporation',2,'bu');

			$boxY+=0.5;
			$this->leftText($boxX,$boxY+=0.75,'Online Payment:',2,'b');
			$this->leftText($boxX,$boxY+=0.75,'GCash Name: Rachelle Buensuceso',2,'');
			$this->leftText($boxX,$boxY+=0.75,'GCash Number: 09777 802 0641',2,'');
			$this->leftText($boxX,$boxY+=0.75,'RCBC : RAILIM INDUSTIAL CORPORATION | 7-591-149-848',2,'');
			$this->leftText($boxX,$boxY+=0.75,'BPI: RAILIM ENGINEERING SUPPLY TRADING | 9049-3320-49',2,'');


			$path = __DIR__ .'/images'.DS.'sample_qr_code.jpg';
			$imgW = 1.18;
			$imgH = 1.18;
			$imgX = 11.7;
			$imgY = 0.1;
			$this->DrawImage($imgX,$imgY,$imgW,$imgH,$path);
			$this->GRID['font_size']=7;
			$this->leftText($imgX+0.1,$imgY+6.5,'Scan to Pay via GCash',2,'');
			$this->setFillColor(169,2,1);
			$this->DrawBox(0,8,15,1,'F');
			$this->setTextColor(255,255,255);
			$this->GRID['font_size']=7.5;
			$this->leftText(1,8.65,'Railim Industial Corporation: Building Tomorrow Together','','b');
			$this->leftText(11.75,8.65,'www.railimindustrial.com','','b');
			$this->setTextColor(0,0,0);

			$bx =1.7;
			$by = $metrics['base_y']+0.666;
			$code=$this->soa_no;
			$color = '000';
			$w = 0.0092;
			$h = 0.45;
			$angle = 0;
			$type = 'code128';
			Barcode::fpdf($this, $color, $bx, $by, $angle, $type, $code,$w,$h);
		}
	}