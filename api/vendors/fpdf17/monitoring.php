<?php
	require ('formsheet.php');
	class Monitoring extends Formsheet{
		public $version = '1.0.1';
		function __construct(){
			$this->showLines = false;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='L';
			$this->_font = 20;
			
			parent::__construct();
		}
		function graph($data){
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>297,
					'height'=>210,
					'rows'=>40,
					'cols'=>41,
			);
			$this->section($metrics);
			
			
			$x = 0;
			$y = 0;
			$w =  297;
			$h = 210;

			$this->GRID['font_size'] = 12;
			$monthCovered = strtoupper((date( 'M - Y',strtotime($data['coverage']['to']))));
			
			$this->leftText(13,2,'PO MONITORING FOR THE MONTH OF '.$monthCovered,3,'b');
			$this->DrawImage(5,10,120,60,$data['graph']);
			$table =  $data['table'];
			$lenItems =  count($table);
			if($lenItems< 25):
				$y=9.5;
				$this->DrawBox(22,$y,11,12.5,'D');
				$itemH=1;
				$this->GRID['font_size']=9;
			else:
				$h = $lenItems/2;
				$y = 9.5 -  ($h/6);
				$itemH=0.52;
				$this->GRID['font_size']=8;
				$this->DrawBox(22,$y,11,$h+3,'D');
			endif;


			$y++;
			$this->leftText(22.5,$y,'#',1,'b');
			$this->leftText(23.5,$y,'Item',4,'b');
			$this->rightText(29.5,$y,'Area (mm2)',3,'b');
			$y++;
			
			foreach($table as $t):
				$code = $t['code'];
				$item = $t['item'];
				$amount =number_format($t['amount'],0,'',',');
				$this->leftText(22.5,$y,$code,1,'b');
				$this->leftText(23.5,$y,$item,4,'');
				$this->rightText(29.5,$y,$amount,3,'');
				$y+=$itemH;
			endforeach;
			$this->createSheet();

			
		}
		function hdr($data){
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>297,
					'height'=>210,
					'rows'=>40,
					'cols'=>41,
			);
			$this->section($metrics);
			
			
			$x = 0;
			$y = 0;
			$w =  297;
			$h = 210;

			$this->GRID['font_size'] = 12;
			$monthCovered = strtoupper((date( 'M - Y',strtotime($data['coverage']['to']))));
			
			$this->leftText(13,2,'PO MONITORING FOR THE MONTH OF '.$monthCovered,3,'b');
			$this->DrawBox(1,3,2,2,'D');
			$this->DrawBox(3,3,2,2,'D');
			$this->DrawBox(5,3,3,2,'D');
			$this->DrawBox(8,3,8,2,'D');
			$this->DrawBox(16,3,1,2,'D');
			$this->DrawBox(17,3,2,2,'D');
			$this->DrawBox(19,3,3,2,'D');
			$this->DrawBox(22,3,3,2,'D');
			$this->DrawBox(25,3,3,2,'D');
			$this->DrawBox(28,3,3,2,'D');
			$this->DrawBox(31,3,3,2,'D');
			$this->DrawBox(34,3,3,2,'D');
			$this->DrawBox(37,3,3,2,'D');
			
			$this->GRID['font_size']=8;
			$this->leftText(1.5,4,'Date',3,'b');
			$this->leftText(3.1,4,'Customer',3,'b');
			$this->leftText(5.5,4,'PO number',3,'b');
			$this->leftText(9.5,4,'Materials',3,'b');
			$this->leftText(16.25,4,'Qty',3,'b');
			$this->leftText(17.5,4,'Price/',3,'b');
			$this->leftText(17.5,4.5,'Unit',3,'b');
			$this->leftText(19.5,4,'Total',3,'b');
			$this->leftText(22.5,4,'Grand Total',3,'b');
			$this->leftText(25.5,4,'Import',3,'b');
			$this->leftText(28.5,4,'Local',3,'b');
			$this->leftText(31.5,4,'Pending/',3,'b');
			$this->leftText(31.5,4.5,'Cancel',3,'b');
			$this->leftText(34.5,4,'Status/',3,'b');
			$this->leftText(34.5,4.5,'Remarks',3,'b');
			$this->leftText(37.5,4,'Invoice',3,'b');
		}
		
		function data($hdr, $data, $pages, $page, $isLastPage=false){
			//pr(count($data['details'])); 
			//pr($data); 
			//exit();
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>297,
					'height'=>210,
					'rows'=>40,
					'cols'=>41,
			);
			$this->section($metrics);
			$monthCovered = strtoupper((date( 'M - Y',strtotime($hdr['coverage']['from']))));
			$monthEnd =   date( 'M d',strtotime($hdr['coverage']['to']));
			$poTotalEnd=   date( 'd.m.y',strtotime($hdr['coverage']['to']));
			
			$line = 6;
			$text_line = 5.8;
			$dates = array();
			$poTotal = 0;
			foreach($data['details'] as $i=>$item){
				//pr($item);
				$dateHeight = 0;
				$strLine = $line-1;
				
				$this->GRID['font_size']=8.5;
				//$this->leftText(3.5,$text_line,$item['customer'],null,'');
				if($poTotalEnd==$item['date'])
						$poTotal +=$item['grand_total'];
				$this->leftText(5.2,$text_line,$item['po_number'],null,'');
				//$this->rightText(24.9,$text_line,number_format($item['grand_total'],2),null,'');

				if($item['status']=='cancelled'){
					$this->SetTextColor(231,31,54);
					$this->leftText(34.2,$text_line,$item['status'],null,'');
					$this->rightText(33.9,$text_line,number_format($item['grand_total'],2),null,'');
				}else{
					$this->SetTextColor(44,39,41);
					if($item['status']=='served')
						$this->leftText(34.2,$text_line,strtoupper($item['status']),null,'');
					else
						$this->leftText(34.2,$text_line,$item['status'],null,'');
				}
				if($item['status']!='served')
					$this->rightText(33.9,$text_line,number_format($item['grand_total'],2),null,'');
				
					
				$this->SetTextColor(44,39,41);
				$this->leftText(37.2,$text_line,$item['invoice'],null,'');
				$GTOTAL = 0;
				$GTXYLN =  $text_line;
				foreach($item['materials'] as $m=>$mat){
					if(isset($mat['import'])){
						$this->rightText(27.9,$text_line,number_format($mat['import'],2),null,'');
						$GTOTAL +=$mat['import'];
					}
					if(isset($mat['local'])){
						$this->rightText(30.9,$text_line,number_format($mat['local'],2),null,'');
						$GTOTAL +=$mat['local'];
					}
				
					if(!isset($dates[$item['date']]))
						$dates[$item['date']] = 1;
					else
						$dates[$item['date']] += 1;
					
					$this->leftText(8.2,$text_line,$mat['mat'],null,'');
					$this->centerText(16,$text_line,$mat['qty'],1,'');
					$this->rightText(18.9,$text_line,number_format($mat['price'],2),null,'');

					$itemAmount = number_format(abs($mat['amount']),2);
					if($mat['amount']<0){
						$itemAmount =  sprintf("(%s)",$itemAmount);
					}
					$this->rightText(21.9,$text_line, $itemAmount,null,'');
					
					$dateHeight++;
					$this->DrawBox(8,$line-1,8,1,'D');
					$this->DrawBox(16,$line-1,1,1,'D');
					$this->DrawBox(17,$line-1,2,1,'D');
					$this->DrawBox(19,$line-1,3,1,'D');
					$this->DrawBox(25,$line-1,3,1,'D');
					$this->DrawBox(28,$line-1,3,1,'D');
					$line++;
					$text_line++;
					
				}

				$this->rightText(24.9,$GTXYLN,number_format($GTOTAL,2),null,'');
				//$this->DrawBox(1,$strLine,2,$dateHeight,'D');
				$customerLine = $strLine+0.8;
				$lnbrk = 0.4;
				if($dateHeight>1){
					$customerLine+=($dateHeight/3);
					$lnbrk = 0.5;
				}
				$this->fitParagraph(3.2,$customerLine,$item['customer'],1,$lnbrk);
				$this->DrawBox(3,$strLine,2,$dateHeight,'D');
				$this->DrawBox(5,$strLine,3,$dateHeight,'D');
				$this->DrawBox(22,$strLine,3,$dateHeight,'D');
				$this->DrawBox(25,$strLine,3,$dateHeight,'D');
				$this->DrawBox(28,$strLine,3,$dateHeight,'D');
				$this->DrawBox(31,$strLine,3,$dateHeight,'D');
				$this->DrawBox(34,$strLine,3,$dateHeight,'D');
				$this->DrawBox(37,$strLine,3,$dateHeight,'D');
			}
			//Update PO Total
			//$hdr['totals']['po_total'] = $poTotal;
			//pr($dates); exit();
			$dateLine = 6;
			//pr($dates); exit();
			foreach($dates as $i=>$date){
				$this->DrawBox(1,$dateLine-1,2,$date,'D');
				$this->leftText(1.2,$dateLine-0.25,$i,null,'');
				$dateLine+=$date;
			//	pr($date);
			}
			$line+=5;

			if($isLastPage && $line>30)
				$line-=5;
			$text_line = $line-.1;
			$this->leftText(1.2,$text_line,'Import:',null,'b');
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['import'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(1.2,$text_line,'Local:',null,'b');	
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['local'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(1.2,$text_line,'Less Raitech:',null,'b');
			$raiTecTot = $hdr['totals']['raitech'];
			$raiTec = '('.number_format($raiTecTot,2).')';
			$this->rightText(6.9,$text_line,$raiTec,null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			//$line++;
			//$text_line++;
			//$this->leftText(1.2,$text_line,'Fabrications:',null,'b');
			//$this->rightText(6.9,$text_line,'-',null,'b');
			//$this->DrawBox(1,$line-1,6,1,'D');
			$this->GRID['font_size']=8;
			$line+=2;
			$text_line+=2;
			$this->leftText(1.2,$text_line,'Grand Total:',null,'b');
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['grand_total'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(7.2,$text_line,'PENDING:',null,'b');
			$this->rightText(12.9,$text_line,number_format($hdr['totals']['pending'],2),null,'b');
			$this->DrawBox(7,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(7.2,$text_line,'SERVED:',null,'b');
			$this->rightText(12.9,$text_line,number_format($hdr['totals']['served'],2),null,'b');
			$this->DrawBox(7,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(1.2,$text_line,'PO Total '.$monthEnd.' :',null,'b');
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['po_total'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
		}
		
		function test($data){
			//createGrid($base_x ,$base_y,$width,$height,$rows,$cols)
			
			
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>297,
					'height'=>210,
					'rows'=>40,
					'cols'=>41,
			);
			$this->section($metrics);
			
			
			$x = 0;
			$y = 0;
			$w =  297;
			$h = 210;

			//$path = __DIR__ .'/images'.DS.'collection.png';
			//$this->DrawImage($x,$y,$w,$h,$path);
			$this->showLines = true;
			
			//$metrics['width']=2;
			//$metrics['base_y'] = 1;
			//$this->section($metrics);
			//$this->leftText(1,1,'Account Name:',null,'b');
			//$this->leftText(2,6,$data['dr_no'],null,'');
			//$this->leftText(4,6,$data['amount_paid'],null,'');
			//$this->leftText(5,6,'.50',null,'');

			$monthCovered = strtoupper((date( 'M - Y',strtotime($data['coverage']['from']))));
			$monthEnd=   date( 'M d',strtotime($data['coverage']['to']));
			$this->GRID['font_size'] = 12;
			$this->leftText(13,2,'PO MONITORING FOR THE MONTH OF '.$monthCovered,3,'b');
			$this->DrawBox(1,3,2,2,'D');
			$this->DrawBox(3,3,2,2,'D');
			$this->DrawBox(5,3,3,2,'D');
			$this->DrawBox(8,3,8,2,'D');
			$this->DrawBox(16,3,1,2,'D');
			$this->DrawBox(17,3,2,2,'D');
			$this->DrawBox(19,3,3,2,'D');
			$this->DrawBox(22,3,3,2,'D');
			$this->DrawBox(25,3,3,2,'D');
			$this->DrawBox(28,3,3,2,'D');
			$this->DrawBox(31,3,3,2,'D');
			$this->DrawBox(34,3,3,2,'D');
			$this->DrawBox(37,3,3,2,'D');
			
			$this->GRID['font_size']=8;
			$this->leftText(1.5,4,'Date',3,'b');
			$this->leftText(3.1,4,'Customer',3,'b');
			$this->leftText(5.5,4,'PO number',3,'b');
			$this->leftText(9.5,4,'Materials',3,'b');
			$this->leftText(16.25,4,'Qty',3,'b');
			$this->leftText(17.5,4,'Price/',3,'b');
			$this->leftText(17.5,4.5,'Unit',3,'b');
			$this->leftText(19.5,4,'Total',3,'b');
			$this->leftText(22.5,4,'Grand Total',3,'b');
			$this->leftText(25.5,4,'Import',3,'b');
			$this->leftText(28.5,4,'Local',3,'b');
			$this->leftText(31.5,4,'Pending/',3,'b');
			$this->leftText(31.5,4.5,'Cancel',3,'b');
			$this->leftText(34.5,4,'Status/',3,'b');
			$this->leftText(34.5,4.5,'Remarks',3,'b');
			$this->leftText(37.5,4,'Invoice',3,'b');
			
		
			$this->GRID['font_size']=7;
			$line = 6;
			$text_line = 5.9;
			$dates = array();
			foreach($data['details'] as $i=>$item){
				$dateHeight = 0;
				$strLine = $line-1;
				
				$this->leftText(1.2,$text_line,$item['date'],null,'');
				//$this->leftText(3.5,$text_line,$item['customer'],null,'');
				
				$this->leftText(5.2,$text_line,$item['po_number'],null,'');
				$this->rightText(24.9,$text_line,number_format($item['grand_total'],2),null,'');
				if($item['status']=='cancelled'){
					$this->SetTextColor(231,31,54);
					$this->leftText(34.2,$text_line,$item['status'],null,'');
					$this->rightText(33.9,$text_line,number_format($item['grand_total'],2),null,'');
				}else{
					$this->SetTextColor(44,39,41);
					if($item['status']=='served')
						$this->leftText(34.2,$text_line,strtoupper($item['status']),null,'');
					else
						$this->leftText(34.2,$text_line,$item['status'],null,'');
				}
				if($item['status']!='served')
					$this->rightText(33.9,$text_line,number_format($item['grand_total'],2),null,'');
				
					
				$this->SetTextColor(44,39,41);
				$this->leftText(37.2,$text_line,$item['invoice'],null,'');
				if($item['import'])
					$this->rightText(27.9,$text_line,number_format($item['import'],2),null,'');
				if($item['local'])
					$this->rightText(30.9,$text_line,number_format($item['local'],2),null,'');
				
				foreach($item['materials'] as $m=>$mat){
					
					if(!isset($dates[$item['date']]))
						$dates[$item['date']] = 1;
					else
						$dates[$item['date']] += 1;
					
					$this->leftText(8.2,$text_line,$mat['mat'],null,'');
					$this->centerText(16,$text_line,$mat['qty'],1,'');
					$this->rightText(18.9,$text_line,number_format($mat['price'],2),null,'');
					$this->rightText(21.9,$text_line,number_format($mat['amount'],2),null,'');
					
					$dateHeight++;
					$this->DrawBox(8,$line-1,8,1,'D');
					$this->DrawBox(16,$line-1,1,1,'D');
					$this->DrawBox(17,$line-1,2,1,'D');
					$this->DrawBox(19,$line-1,3,1,'D');
					$line++;
					$text_line++;
				}
				//$this->DrawBox(1,$strLine,2,$dateHeight,'D');
				$customerLine = $strLine+0.5;
				$lnbrk = 0.4;
				if($dateHeight>1){
					$customerLine+=($dateHeight/3);
					$lnbrk = 0.5;
				}
				$this->fitParagraph(3.2,$customerLine,$item['customer'],1,$lnbrk);
				$this->DrawBox(3,$strLine,2,$dateHeight,'D');
				$this->DrawBox(5,$strLine,3,$dateHeight,'D');
				$this->DrawBox(22,$strLine,3,$dateHeight,'D');
				$this->DrawBox(25,$strLine,3,$dateHeight,'D');
				$this->DrawBox(28,$strLine,3,$dateHeight,'D');
				$this->DrawBox(31,$strLine,3,$dateHeight,'D');
				$this->DrawBox(34,$strLine,3,$dateHeight,'D');
				$this->DrawBox(37,$strLine,3,$dateHeight,'D');
			}
			//pr($dates); exit();
			$dateLine = 6;
			foreach($dates as $i=>$date){
				$this->DrawBox(1,$dateLine-1,2,$date,'D');
				$dateLine+=$date;
			//	pr($date);
			}
			$line+=5;
			$text_line = $line-.1;
			$this->leftText(1.2,$text_line,'Import:',null,'b');
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['import'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(1.2,$text_line,'Local:',null,'b');	
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['local'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(1.2,$text_line,'Raitec:',null,'b');
			$this->rightText(6.9,$text_line,'-',null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			//$line++;
			//$text_line++;
			//$this->leftText(1.2,$text_line,'Fabrications:',null,'b');
			//$this->rightText(6.9,$text_line,'-',null,'b');
			//$this->DrawBox(1,$line-1,6,1,'D');
			$this->GRID['font_size']=8;
			$line+=2;
			$text_line+=2;
			$this->leftText(1.2,$text_line,'Grand Total:',null,'b');
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['grand_total'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(7.2,$text_line,'PENDING:',null,'b');
			$this->rightText(12.9,$text_line,number_format($hdr['totals']['pending'],2),null,'b');
			$this->DrawBox(7,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(7.2,$text_line,'SERVED:',null,'b');
			$this->rightText(12.9,$text_line,number_format($hdr['totals']['served'],2),null,'b');
			$this->DrawBox(7,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(1.2,$text_line,'PO Total '.$monthEnd.' :',null,'b');
			$this->rightText(6.9,$text_line,number_format($hdr['totals']['po_total'],2),null,'b');
			$this->DrawBox(1,$line-1,6,1,'D');
			
		}
		
		
	}