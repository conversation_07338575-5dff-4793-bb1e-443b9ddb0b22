<?php
	require ('reportsheet.php');
	class SOARailim extends ReportSheet{
		function __construct(){
			$this->_height=11;
			parent::__construct();
		}
		function info($info=null){
			if(!$info){
				$info  =  array();
				$info['date']='Jul 08,2021';
				$info['account']='<PERSON>';
			}
			$this->GRID['font_size']=12;
			$this->centerText(5,3.5,'Statment of Account',6,'b');

			$this->GRID['font_size']=10;
			$y =  5;
			$this->leftText(2,$y,'DATE: '.$info['date'],6,'b');
			$y+=0.5;
			$this->leftText(2,$y,'SOA# '.$info['soa_no'],6,'b');
			$this->leftText(6.5,$y,'TO: '.$info['account'],6,'b');
			$y+=0.5;
			$this->leftText(2,$y,'Terms: 30 Days',6,'b');

			$this->GRID['font_size']=9;
			$y=19.5;
			$this->leftText(2,$y,'Account Name: RAILIM ENGINEERING SUPPLY TRADING',6,'b');
			$y+=0.5;
			$this->leftText(2,$y,'ACCOUNT No BPI : 9049 3320 49',6,'');
			$y+=0.5;
			$this->leftText(2,$y,'Account Name: RAILIM ENGINEERING SUPPLY TRADING',6,'b');
			$y+=0.5;
			$this->leftText(2,$y,'ACCOUNT No RCBC : -7590 54700 9',6,'');
			$y+=1;
			$this->leftText(2,$y,'Respectfully Yours,',6,'');
			$this->leftText(8.5,$y,'Received by:',6,'');
			$this->SetDrawColor(0,0,0);
			$this->DrawLine($y+1,'h',array(8.5,3));
			$y+=1;
			$this->leftText(2,$y,'Angelie S. Samillano',6,'');
			$y+=0.5;
			$this->leftText(2,$y,'Accounting',6,'');

		}

		function details($details =null,$info=null){
			if(!$details){
				$details  =  array();
				$details['date']='';
				$details['po_no']='';
				$details['details']='';
				$details['amount']=array('text'=>'0.00','value'=>0);
				$details = array($details);
			}
			// DrawBox $x, $y, $w, $h
 			$this->DrawBox(2,7,11,11,'D');
 			$this->SetFillColor(180,180,180);
 			// Headers
 			$this->DrawBox(2,7,11,1,'FD');
 			
 			$base_x =  $this->GRID['cell_width']*2;
 			$base_y =  $this->GRID['cell_height']*8;
 			$width = $this->GRID['cell_width']*11;
 			$height = $this->GRID['cell_height']*10;
 			$rows =  10;
 			$cols =  15;
 			//$this->showLines = true;
 			$this->createGrid($base_x ,$base_y,$width,$height,$rows,$cols);
 			$this->SetDrawColor(0,0,0);
 			//
 			$this->DrawLine(3,'v',array(-1,11));
 			$this->DrawLine(6,'v',array(-1,11));
 			$this->DrawLine(10,'v',array(-1,11));
 			$this->SetFillColor(180,180,180);
 			$y = -0.35;
 			$this->centerText(0,$y,'Invoice Date',3,'b');
 			$this->centerText(3,$y,'Invoice No.',3,'b');
 			$this->centerText(6,$y,'PO No.',4,'b');
 			$this->centerText(10,$y,'Amount',5,'b');

 			
 			$grandTotal  = 0;
			
			$ctrD = count($details);
			$this->GRID['font_size'] = $ctrD <= 10? 10:7;
			$height   =  10 / ($ctrD <= 10?13:$ctrD);
			$this->DrawMulitpleLines(0,10,$height,'h');
			$y = $height/1.5;
 			foreach($details as $dtl):
				if(!isset($dtl['date'])) continue;
	 			$this->centerText(0,$y,$dtl['date'],3,'b');
	 			$this->centerText(3,$y,$dtl['details'],3,'b');
	 			$this->centerText(6,$y,$dtl['po_no'],4,'b');
	 			$this->rightText(9.75,$y,$dtl['amount']['text'],5,'b');
	 			$grandTotal += $dtl['amount']['value'];
 				$y+=$height;
 			endforeach;

 			$this->GRID['font_size']=18;
 			// Grand Total
 			$this->DrawBox(10,10,5,1,'FD');
 			$gTotal =  number_format($grandTotal,2,'.',',');
 			$this->rightText(8,10.75,$gTotal,5,'b');
 			$this->leftText(9.5,10.75,'P',5,'b');
 			
		}
		function footnote($info=null){

		}
	}
?>