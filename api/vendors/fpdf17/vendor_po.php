<?php
	require ('formsheet.php');
	class VendorPo extends Formsheet{
		function __construct(){
			$this->showLines = FALSE;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='P';
			
			parent::__construct();
		}
		
		
		
		function data($data){
			
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>210,
					'height'=>297,
					'rows'=>20,
					'cols'=>20,
			);
			$x = 1;
			$y = 0;
			$w =  180;
			$h = 60;
			$this->section($metrics);
			$path = __DIR__ .'/images'.DS.'heading1.JPG';
			$foot = __DIR__ .'/images'.DS.'footer.JPG';
			$this->DrawImage($x,$y,$w,$h,$path);
			$this->DrawImage(1.3,16.5,175,35,$foot);
			$this->showLines = FALSE;
			
			$this->GRID['font_size']=20;
			$this->leftText(7.2,5,'PURCHASE ORDER',null,'');
			$this->GRID['font_size']=14;
			$this->leftText(11,6,'P.O. No: XXX',null,'');
			$this->leftText(11,6.5,'Date: XXX',null,'');
			$this->leftText(11,7,'Attention to:',null,'');
			$this->leftText(11,7.5,'XXX',null,'');
			$this->leftText(2,7,'Vendor: ',null,'');
			$this->leftText(2,7.5,'XXX',null,'');
			
			$this->GRID['font_size']=14;
			
			$this->DrawBox(2,8,5.3,.5);
			$this->leftText(3,8.4,'Payment Terms',null,'');
			$this->DrawBox(7.3,8,5.3,.5);
			$this->leftText(8.8,8.4,'PRF No',null,'');
			$this->DrawBox(12.6,8,5.4,.5);
			$this->leftText(14,8.4,'Currency',null,'');
			$this->GRID['font_size']=10;
			$this->DrawBox(2,8.5,5.3,.4);
			$this->leftText(4,8.8,'XXX',null,'');
			$this->DrawBox(7.3,8.5,5.3,.4);
			$this->leftText(9,8.8,'XXX',null,'');
			$this->DrawBox(12.6,8.5,5.4,.4);
			$this->leftText(14.5,8.8,'XXX',null,'');
			
			$this->DrawBox(2,9.4,2,.5);
			$this->leftText(2.5,9.8,'No.',null,'');
			$this->leftText(2.5,10.25,'XXX',null,'');
			$this->DrawBox(4,9.4,7,.5);
			$this->leftText(5,9.8,'Description',null,'');
			$this->leftText(5,10.25,'XXX',null,'');
			$this->DrawBox(11,9.4,1.5,.5);
			$this->leftText(11.2,9.8,'Qty',null,'');
			$this->leftText(11.2,10.25,'XXX',null,'');
			$this->DrawBox(12.5,9.4,1.5,.5);
			$this->leftText(12.8,9.8,'Unit',null,'');
			$this->leftText(12.8,10.25,'X',null,'');
			$this->DrawBox(14,9.4,2,.5);
			$this->leftText(14.2,9.8,'Unit Price',null,'');
			$this->leftText(14.2,10.25,'XXX',null,'');
			$this->DrawBox(16,9.4,2,.5);
			$this->leftText(16.2,9.8,'Total',null,'');
			$this->leftText(16.2,10.25,'XXX',null,'');
			
			$this->DrawBox(2,9.9,16,6);
			$this->leftText(12,14.5,'Subtotal',null,'');
			$this->leftText(16,14.5,'-',null,'');
			$this->leftText(12,15,'Discount',null,'');
			$this->leftText(16,15,'-',null,'');
			$this->leftText(12,15.5,'Total',null,'');
			$this->leftText(16,15.5,'-',null,'');
			
		}
		
		
	}