<?php
	require ('reportsheet.php');
	class SPORailim extends ReportSheet{
		function __construct(){
			$this->_height=12;		
			parent::__construct();
		}
		function info($order=null){
			$this->GRID['font_size']=7;
			$this->leftText(2,3,'Tel. no.: (*************, SUN# 0943-132-4342, GLOBE -0975-256-2175)	',6,'');
			$this->leftText(2,3.3,'E-mail: <EMAIL>; <EMAIL>; ',6,'');

			$this->leftText(2,3.6,'            <EMAIL>; <EMAIL>',6,'');
			$this->leftText(2,3.9,"TIN# 232-032-360-000",6,'');
			$this->GRID['font_size']=12;
			$this->centerText(2,4.5,"PURCHASE ORDER",11,"b");
			$this->GRID['font_size']=10;
			$this->leftText(9,5.35,"PO No: ".$order['spo_no'],11,"b");
			$this->leftText(9,5.8,"Date: ".$order['order_date'],11,"b");
			$this->GRID['font_size']=8;
			$this->leftText(9,6.4,"Attention to:",11,"b");
			$this->leftText(2,6.4,"Vendor",11,"b");
			$this->GRID['font_size']=10;
			$this->leftText(9,6.8,"   ",11,"b");
			$this->GRID['font_size']=12;
			$this->leftText(2,6.8,$order['supplier'],11,"b");

		}
		function info2($order=null){
			$y=2.25;
			$ln = 0.25;
			$this->GRID['font_size']=7;

			$this->leftText(4,$y+=$ln,'Tel. no.: (*************, SUN# 0943-132-4342, GLOBE -0975-256-2175)	',6,'');
			$this->leftText(10,$y,"TIN# 232-032-360-000",6,'');
			$this->leftText(3,$y+=$ln,'E-mail: <EMAIL>; <EMAIL>;<EMAIL>; <EMAIL> ',6,'');

			
			$y = 3.2;
			$this->GRID['font_size']=13;
			$this->centerText(2,$y+0.1,"PURCHASE ORDER",11,"b");
			$this->GRID['font_size']=9;
			$this->leftText(10.5,$y,"PO No: ".$order['spo_no'],11,"b");
			$this->leftText(10.5,$y+=0.3,"Date: ".$order['order_date'],11,"b");
			
			$y = 3.9;
			$this->GRID['font_size']=8;
			$this->leftText(10.5,$y,"Terms:",4,"");
			$this->leftText(6.5,$y,"Attention to:",4,"");
			$this->leftText(2,$y,"Vendor",11,"");
			$y = 4.25;
			$this->GRID['font_size']=10;
			$this->fitText(10.5,$y,$order['terms'],4,"b");
			$this->fitText(6.5,$y,$order['attention'],4,"b");
			$this->fitText(2,$y,$order['supplier'],4,"b");

		}
		function terms(){
			$y=4.5;
			// DrawBox $x, $y, $w, $h
 			$this->DrawBox(2,$y,11,1,'D');
 			$this->SetFillColor(180,180,180);
 			// Headers
 			$this->DrawBox(2,$y,11,0.5,'FD');
 			$this->GRID['font_size']=9;
 			$y +=0.35;
 			$this->centerText(2,$y,"PAYMENT TERMS",3,"");
 			$this->centerText(5,$y,"PRF No",3,"");
 			$this->centerText(8.1,$y,"CURRENCY",3,"");

 			$y += 0.5;
 			$this->centerText(2,$y,"15days pdc",3,"b");
 			$this->centerText(5,$y," - ",3,"b");
 			$this->centerText(8.1,$y,"PHP",3,"b");
		}

		function details($details = null,$total=0,$printHalf =true){
			
			if($printHalf)
				$this->DrawBox(0,0,15,12,'D');
			$y=4.5;
			$h = !$printHalf?12:5.5;
			// DrawBox $x, $y, $w, $h
 			$this->DrawBox(2,$y,11,$h,'D');
 			$this->SetFillColor(180,180,180);
 			// Headers
 			$this->DrawBox(2,$y,11,0.5,'FD');

 			$this->GRID['font_size']=9;
 			$y += 0.35;
 			$this->centerText(2,$y,"No",1,"");
 			$this->centerText(3,$y,"Description",3.5,"");
 			$this->centerText(6.5,$y,"Qty",1.5,"");
 			$this->centerText(8,$y,"Unit",1.5,"");
 			$this->centerText(9.5,$y,"Unit Price",1.5,"");
 			$this->centerText(11,$y,"Total",1.5,"");
 			$y += 0.5;
 			$this->GRID['font_size']=8;
 			if($details):
	 			foreach($details as $dtl):
	 			$this->centerText(2,$y,$dtl['no'],1,"");
	 			$this->centerText(3,$y,$dtl['desc'],3.5,"");
	 			$this->centerText(6.5,$y,$dtl['qty'],1.5,"");
	 			$this->centerText(8,$y,"pc",1.5,"");
	 			$this->rightText(9.5,$y,$dtl['price'],1.5,"");
	 			$this->rightText(11,$y,$dtl['amount'],1.5,"");
	 			$y+=0.35;
	 			endforeach;

 			endif;

 			
 			$y+=0.1;
 			$this->centerText(2,$y,"************** NOTHING FOLLOWS **************",10.5,"");

 			$y +=0.3;
 			$this->rightText(9.5,$y,"Subtotal",1.5,"b");
 			$this->rightText(11,$y,$total,1.5,"b");
 			
 			$y +=0.3;
 			$this->rightText(9.5,$y,"Discount",1.5,"b");
 			$this->rightText(11,$y," - ",1.5,"b");

 			$y +=0.3;
 			$this->rightText(9.5,$y,"Total",1.5,"b");
 			$this->rightText(11,$y,$total,1.5,"b");

		}
		function signatories($prepare="",$printHalf =true){
			$y = $printHalf?10.5:18;
			$this->leftText(2,$y,"Prepared  by:",1,"");
			$this->leftText(4,$y,"Check By:",1,"");
			$this->leftText(6,$y,"Approved by:",1,"");
			$this->leftText(8.5,$y,"Received by:( Signature over printer name)		",1,"");

			$y += 0.75;
			$this->leftText(2,$y,$prepare,1,"");
			$this->leftText(4,$y,"Lalaine P.",1,"");
			$this->leftText(6,$y,"Rachelle Buensuceso",1,"");
			$this->leftText(8.5,$y,"Date received: ",1,"");
			$timestamp = date("M d Y, h:i:a",strtotime("-1 hour"));
			$y+=0.5;
			$this->leftText(2,$y,"Date/Time Generated: $timestamp",1,"i");
		}


	}
?>