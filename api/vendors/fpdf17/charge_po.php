<?php
	require ('formsheet.php');
	class ChargePO extends Formsheet{
		function __construct(){
			//$this->showLines = true;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='P';
			
			parent::__construct();
		}
		
		function hdr($data){
			
		}
		
		function data($hdr,$data,$total_per_page,$page){
			//pr($data); exit();
			$metrics = array(
					'base_x'=>0,
					'base_y'=>-1.25,
					'width'=>138,
					'height'=>212,
					'rows'=>28,
					'cols'=>12,
			);
			//$this->showLines = true;
			$this->section($metrics);
			
			
			$x = 0;
			$y = 0;
			$w =  210;
			$h = 297;


			$path = __DIR__ .'/images'.DS.'invoice.png';
			//$this->DrawImage($x,$y,$w,$h,$path);
			$this->showLines = true;



			$this->GRID['font_size']=10;
			$this->leftText(10.75,4,'PO-'.$hdr['po_no'],null,'');
			$this->GRID['font_size']=9;
			$this->leftText(3.55,5,$hdr['charged_to'],null,'');
			$this->leftText(3.5,5.7,$hdr['tin'],null,'');
			$this->leftText(3.6,6.3,$hdr['address'],null,'');
			$this->leftText(4,7,$hdr['business_style'],null,'');

			$this->leftText(11,5,$hdr['transac_date'],null,'');
			$this->leftText(11.5,5.7,$hdr['terms'],null,'');
			$this->leftText(11.5,7,$hdr['osca_pwd_id_no'],null,'');
			//$this->leftText(9,6.4,$data['terms'],null,'');
			
			$taxOffset =  -0.1;
			$this->leftText(11.5,22.75,$hdr['cshr_auth_rep'],null,'');
			/*
			$this->rightText(12.4,16.2+$taxOffset,number_format($hdr['total_sales'],2),null,'');
			$this->rightText(12.4,17+$taxOffset,number_format($hdr['less_vat'],2),null,'');
			$this->rightText(12.4,17.8+$taxOffset,number_format($hdr['net_of_vat'],2),null,'');
			$this->rightText(12.4,18.6+$taxOffset,number_format($hdr['less_sc_pwd_disc'],2),null,'');
			$this->rightText(12.4,19.25+$taxOffset,number_format($hdr['amount_due'],2),null,'');
			$this->rightText(12.4,20+$taxOffset,number_format($hdr['add_vat'],2),null,'');
			$this->rightText(12.4,20.8+$taxOffset,number_format($hdr['total_amount_due'],2),null,'');
			
			$y=16.2+$taxOffset;
			
			if($hdr['vat_type']=='vat')
				$this->rightText(4.75,$y+=0.725,number_format($hdr['vatable_sales'],2),null,'');
			$this->leftText(4.8,$y+=0.725,'PO-'.$hdr['po_no'],null,'');
			$this->leftText(4.8,$y+=0.75,$hdr['csi_no'],null,'');
			if($hdr['vat_type']=='zero-rated'):
				$this->leftText(4.8,$y+=0.75,$hdr['dr_no'],null,'');
				$this->leftText(4.8,$y+=0.75,'Zero Rated',null,'');
			endif;
			$this->leftText(4.8,$y+=0.8,'v3',null,'');
			$x = 0.5;
			$w = 3;
			$y = 1;
			$h = 0.5;
			$fill = 'D';
			$record = array(
						'customer'=>'062',
						'details'=>array('1','2'),
						'grand_total' =>500
						);
			*/
			$lineCtr =7.75;
			$this->rightText(10.9,$lineCtr,'W (mm)','','');
			$this->rightText(12.4,$lineCtr,'L (mm)','','');
			$this->rightText(13.9,$lineCtr,'CODE','','');
			$lineCtr =8.75;
			$itemXOffset = 0.5;
			foreach($data as $i=>$item){
				$this->GRID['font_size'] = 9;
				$this->leftText(2+$itemXOffset,$lineCtr,$item['qty'],'','');
				$this->leftText(3.25+$itemXOffset,$lineCtr,$item['unit'],'','');
				$this->GRID['font_size'] = 9;
				$this->leftText(4.35+$itemXOffset,$lineCtr,$item['description'],'','');
				$this->GRID['font_size'] = 9;
				$this->rightText(10.9,$lineCtr,'_______','','');
				$this->rightText(12.4,$lineCtr,'_______','','');
				$this->rightText(13.9,$lineCtr,'_______','','');
				$lineCtr+=.725;
			}
		}
		
	}