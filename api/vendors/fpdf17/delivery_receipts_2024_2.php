<?php
	require ('formsheet.php');
	class DeliveryReceipt extends Formsheet{
		function __construct(){
			$this->showLines = false;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='P';
			
			parent::__construct();
		}
		
		function hdr($data){
			
		}
		
		function data($hdr,$data,$total,$page){
			//createGrid($base_x ,$base_y,$width,$height,$rows,$cols)
			
			
			$metrics = array(
					'base_x'=>0,
					'base_y'=>-1.25,
					'width'=>138,
					'height'=>212,
					'rows'=>28,
					'cols'=>12,
			);
			//$this->showLines=true;
			$this->section($metrics);
			
			
			$x = 0;
			$y = 0;
			$w =  210;
			$h = 297;

			$path = __DIR__ .'/images'.DS.'delivery.png';
			//$this->DrawImage($x,$y,$w,$h,$path);
			//$this->showLines = true;

			//$this->section($metrics);
			$this->GRID['font_size'] = 9;
			$this->leftText(3,6,$hdr['charged_to'],null,'');
			$this->leftText(1.5,6.75,$hdr['tin'],null,'');
			$this->leftText(2,7.5,$hdr['address'],null,'');
			$this->leftText(9,6,$hdr['transac_date'],null,'');
			$this->leftText(9,6.75,$hdr['terms'],null,'');
			$this->leftText(8,24.5,$hdr['cshr_auth_rep'],null,'');
			$this->leftText(9,4,$hdr['dr_no'],null,'');
			$x = 0.5;
			$w = 3;
			$y = 1;
			$h = 0.5;
			$fill = 'D';
			$record = array(
						'customer'=>'062',
						'details'=>array('1','2'),
						'grand_total' =>500
						);

			
			
			/* for($col =0; $col<=2;$col++){
				$W =  $col*$w;
				//$this->DrawBox($x+$W,$y,$w,$h,$fill);
				foreach($record['details'] as $item){
					//$this->leftText()
					$lineCtr++;
				}
			} */
			
			$lineCtr =10.5;
			$lineHeight = 0.7;
			$ctr =0;
			foreach($data as $i=>$item){
				$this->centerText(1,$lineCtr,$item['qty'],0.5,'');
				$this->leftText(2.25,$lineCtr,$item['unit'],'','');
				$this->leftText(3.25,$lineCtr,$item['article'],'','');
				$lineCtr+=$lineHeight;
				$ctr++;
			}
			
			$y=20;
			$this->leftText(1.5,$y+=$lineHeight,'PO-'.$hdr['po_no'],null,'');
			$this->leftText(1.5,$y+=$lineHeight,$hdr['csi_no'],null,'');
			$this->leftText(1.5,$y+=$lineHeight,$hdr['dr_no'],null,'');
			//$this->leftText(4.8,$y+=$lineHeight,'Zero Rated',null,'');
		}
		function info($info){
			$this->GRID['font_size']=10;
			$this->leftText(1,3.5,'Account Name:',null,'b');
			$this->leftText(3,3.5,$info['account'],null);
			$this->leftText(1,4,'Billing Period:',null,'b');
			$this->leftText(3,4,$info['detail']['cutoff'].', '.date('Y',time()),null);
			$this->leftText(10.25,3.5,'Date:',null,'b');
			$this->leftText(11.75,3.5,$info['date'],null);
			$this->leftText(10.25,4,'Due Date:',null,'b');
			$this->leftText(11.75,4,$info['due_date'],null);
		}
	}