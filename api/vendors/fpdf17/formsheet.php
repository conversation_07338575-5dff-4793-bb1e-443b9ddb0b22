
<?php
define('FPDF_FONTPATH',dirname(__FILE__).'/font/');
require('fpdf.php');
	class FormSheet extends FPDF{
		private $FONT_CONST = 0.50;
		public $GRID = array();
		public $FONT = 'Arial';
		protected $showLines=false;
		protected $_width = 8.5;
		protected $_height = 11;
		protected $_unit = 'in';
		protected $_orient = 'P';
		public $version = '1.0.0';
		public function __construct(){
			$this->FPDF($this->_orient, $this->_unit,array($this->_width,$this->_height));
			$this->createSheet();
		}
		public function createSheet($config=null){
			$this->AddPage();
			$this->SetMargins(0,0);
		}
		
		public function createGrid($base_x ,$base_y,$width,$height,$rows,$cols){
			$grid = array();
			//Compute Grid Metrics
			$grid['font_size']  = $cols<15?9:round(100/($cols*$this->FONT_CONST));
			$grid['width'] = $width;
			$grid['cell_width'] = $width/$cols;
			$grid['cell_height']= $height/$rows;
			$grid['height']= $height;
			$grid['base_x']=$base_x;
			$grid['base_y']=$base_y;
			//Create Grid
			$grid['v_lines'] = $this->DrawHorLines($base_x ,$base_y,$base_x +$width,$base_y,$height,$rows);
			$grid['h_lines'] = $this->DrawVerLines($base_x ,$base_y,$base_x ,$base_y+$height,$width,$cols);
		
			$this->GRID = $grid;
		}
		protected function section($metrics){
			$this->createGrid($metrics['base_x'] ,$metrics['base_y'],$metrics['width'],$metrics['height'],$metrics['rows'],$metrics['cols']);
			$this->SetDrawColor(0);
			if(isset($metrics['border'])){
				$this->Rect($metrics['base_x'],$metrics['base_y'],$metrics['width'],$metrics['height']);
			}
		}
		public function putText($x,$y,$txt,$style=''){
			$this->SetFont('Arial',$style,$this->GRID['font_size']);
			$this->Text($this->GRID['base_x']+($this->GRID['cell_width']*$x),$this->GRID['base_y']+($this->GRID['cell_height']*$y),$txt);			
		}
		
		public function leftText($x,$y,$txt,$w,$style=''){
			$this->putText($x,$y,$txt,$style);
		}
		
		public function fitText($x,$y,$txt,$w,$style=''){
			$font_size = $this->GRID['font_size'];
			$this->SetFont('Arial',$style,10);
			$const =round($this->GetStringWidth($txt),2);
			while($const>$w*0.6){				
				$this->SetFont('Arial',$style,$font_size);
				$const =round($this->GetStringWidth($txt),2);
				$font_size-=0.1;
			}
			$this->Text($this->GRID['base_x']+($this->GRID['cell_width']*$x),$this->GRID['base_y']+($this->GRID['cell_height']*$y),$txt);
		}
		
		
		
		public function fitParagraph($x,$y,$txt,$w,$lnbrk=1){
			$words = explode(" ", $txt);	
			$paragraph = array();
			$width = $w;
			foreach($words as $word){
				$wo = array(
							'len'=>$this->GetStringWidth($word)/$this->GRID['cell_width'],
							'word'=>$word
						);
				array_push($paragraph,$wo);
			}
			$line='';
			$len = 0;
			$ctr = 1;
			foreach($paragraph as $p){
					$len = $len+$p['len'];
					$line = $line.$p['word'].' ';
				if($len>$width||$ctr == count($paragraph)){
					$this->leftText($x,$y,$line,'',$style='');
					$line='';
					$len=0;
					$y+=$lnbrk;
				}
				$ctr++;
			}
		}
		public function centerText($x,$y,$txt,$w,$style=''){
			$this->SetFont('Arial',$style,$this->GRID['font_size']);
			$disp_x = $this->GRID['base_x']+($this->GRID['cell_width']*$x);
			$disp_x += ($this->GRID['cell_width']*$w)/2;
			$disp_y = $this->GRID['base_y']+($this->GRID['cell_height']*$y);
			$disp_x = $disp_x - $this->GetStringWidth($txt)*0.5;
			$this->Text($disp_x,$disp_y,$txt);
		}
		public function rightText($x,$y,$txt,$w,$style=''){

			$disp_x = $this->GRID['base_x']+($this->GRID['cell_width']*$x);
			$disp_y = $this->GRID['base_y']+($this->GRID['cell_height']*$y);
			
			$disp_x = $disp_x +($this->GRID['cell_width']*$w);
			$disp_w = $this->GRID['cell_width']*$w;
			$disp_h = $this->GRID['cell_height'];
			$this->SetFont('Arial',$style,$this->GRID['font_size']);
			if($this->version=='1.0.0'):
				$this->SetXY($disp_x,$disp_y);
				$this->Cell($disp_w, $disp_h,$txt,0,0,'R');
			else:
				$disp_x = $disp_x - $this->GetStringWidth($txt);
				$this->Text($disp_x,$disp_y,$txt);
			endif;
		}
		public function wrapText($x,$y,$txt,$w,$align='l',$lnbrk=1){
			$this->SetFont($this->FONT,'',$this->GRID['font_size']);
			$disp_x = $this->GRID['base_x']+($this->GRID['cell_width']*$x);
			$disp_y = $this->GRID['base_y']+($this->GRID['cell_height']*$y);
			$disp_w = $this->GRID['cell_width'] * $w;
			$disp_h =  $this->GRID['cell_height'] *$lnbrk;
			$this->SetXY($disp_x,$disp_y);
			$this->MultiCell($disp_w, $disp_h, $txt,0,$align);
		}
		public function DrawLine($pt,$ort,$plot=null) {
			if($ort=='v'){
				if(empty($plot)){
					$plot =  array(0,$this->GRID['height']/$this->GRID['cell_height']);
				}
				$x1=$this->GRID['base_x']+($this->GRID['cell_width']*$pt);
				$y1=$this->GRID['base_y']+($this->GRID['cell_height']*$plot[0]);
				$x2=$this->GRID['base_x']+($this->GRID['cell_width']*$pt);
				$y2=$y1+($this->GRID['height'] - ($this->GRID['height']-($this->GRID['cell_height']*$plot[1])));
			}else if($ort=='h'){
				if(empty($plot)){
					$plot =  array(0,$this->GRID['width']/$this->GRID['cell_width']);
				}
				$x1=$this->GRID['base_x']+($this->GRID['cell_width']*$plot[0]);
				$y1=$this->GRID['base_y']+($this->GRID['cell_height']*$pt);
				$x2=$x1+($this->GRID['width'] - ($this->GRID['width']-($this->GRID['cell_width']*$plot[1])));
				$y2=$this->GRID['base_y']+($this->GRID['cell_height']*$pt);
				
			}
			$this->Line($x1,$y1,$x2,$y2);
		}
		
		public function DrawHorLines($x1,$y1,$x2,$y2,$h,$rows){
			
			$H=$h/$rows;
			$h_lines=array();
			for($index=0;$index<$rows;$index++){
				$X1=$x1;
				$Y1=$y1+($H*$index);
				$X2=$x2;
				$Y2=$y2+($H*$index);
				array_push($h_lines,$Y1);
				if($this->showLines){
					if($index%4!=0){
						$this->SetDrawColor(223,224,246);
					}else{
						$this->SetDrawColor(255,0,0);				
					}
					$this->Line($X1,$Y1,$X2,$Y2);
				}
			}
			return $h_lines;
		}
		public function DrawVerLines($x1,$y1,$x2,$y2,$w,$cols){
			
			$W=$w/$cols;
			$v_lines=array();
			for($index=0; $index<$cols; $index++){
				$X1=$x1+($W*$index);
				$Y1=$y1;
				$X2=$x2+($W*$index);
				$Y2=$y2;
				array_push($v_lines,$X1);
				if($this->showLines){
					if($index%4!=0){
						$this->SetDrawColor(223,224,246);
					}else{
						$this->SetDrawColor(255,0,0);				
					}
					$this->Line($X1,$Y1,$X2,$Y2);
				}
			}
			return $v_lines;
		}
		
		protected function DrawMulitpleLines($start_at,$ln_count,$step=1,$orient='h'){
			$ctrs = array();
			for($ctr=$start_at;$ctr<=$ln_count;$ctr+=$step){
				$this->DrawLine($ctr,$orient);
				array_push($ctrs,$ctr);
			}
			return $ctrs;
		}
		
		public function DrawBox($x,$y,$w,$h,$fill=null){
			$x1=$this->GRID['base_x']+($this->GRID['cell_width']*$x);
			$y1=$this->GRID['base_y']+($this->GRID['cell_height']*$y);
			$w1 =$this->GRID['cell_width']*$w;
			$h1 =$this->GRID['cell_height']*$h;
			$this->Rect($x1,$y1,$w1,$h1,$fill);
		}
		
		public function DrawBarcode($x,$y,$code,$format='int25'){
		
			$fontSize = 8;
			$marge    = 8/$this->k;   // between barcode and hri in pixel
			$x		  = $this->GRID['base_x']+($this->GRID['cell_width']*$x);
			$y		  = $this->GRID['base_y']+($this->GRID['cell_height']*$y);
			$height   =	18/$this->k;  // barcode height in 1D ; module size in 2D
			$width    = 1.3/$this->k;    // barcode height in 1D ; not use in 2D
			$angle    = 0;   // rotation in degrees : nb : non horizontable barcode might not be usable because of pixelisation
			$type     = $format;
			$color    = '000000'; // color in hexa
			$data = Barcode::fpdf($this, $color, $x, $y, $angle, $type, array('code'=>$code,'rect'=>true,'b2d'=>true), $width, $height);
			$len = $this->GetStringWidth($data['hri']);
			Barcode::rotate(-$len / 2, ($data['height'] / 2) + $fontSize + $marge, $angle, $xt, $yt);
			$this->RotateText($x + $xt, $y + $yt, $data['hri'], $angle);
			return $data;
		}
		protected function RotateText($x, $y, $txt, $txt_angle, $font_angle=0,$style=''){
			$font_angle+=90+$txt_angle;
			$txt_angle*=M_PI/180;
			$font_angle*=M_PI/180;		
			$txt_dx=cos($txt_angle);
			$txt_dy=sin($txt_angle);
			$font_dx=cos($font_angle);
			$font_dy=sin($font_angle);	
			$this->SetFont('Arial',$style,$this->GRID['font_size']);
			$x = $this->GRID['base_x']+($this->GRID['cell_width']*$x);
			$y = $this->GRID['base_y']+($this->GRID['cell_height']*$y);
			
			$s=sprintf('BT %.2F %.2F %.2F %.2F %.2F %.2F Tm (%s) Tj ET',$txt_dx,$txt_dy,$font_dx,$font_dy,$x*$this->k,($this->h-$y)*$this->k,$this->_escape($txt));
			if ($this->ColorFlag)
				$s='q '.$this->TextColor.' '.$s.' Q';
			$this->_out($s);
		}
		protected function DrawMultipleLines($start_at,$ln_count,$step=1,$orient='h'){
			$ctrs = array();
			for($ctr=$start_at;$ctr<=$ln_count;$ctr+=$step){
				$this->DrawLine($ctr,$orient);
				array_push($ctrs,$ctr);
			}
			return $ctrs;
		}
		
		
		public function DrawImage($x,$y,$w,$h,$path){
			$x1=$this->GRID['base_x']+($this->GRID['cell_width']*$x);
			$y1=$this->GRID['base_y']+($this->GRID['cell_height']*$y);
			$w1 =$this->GRID['cell_width']*$w;
			$h1 =$this->GRID['cell_height']*$h;
			$this->Image($path,$x1,$y1,$w,$h);
		}
		public function amountToWords($number){
			$hyphen      = '-';
		    $conjunction = ' and ';
		    $separator   = ' ';
		    $negative    = 'negative ';
		    $decimal     = ' and ';
		    $dictionary  = array(
		        0                   => 'zero',
		        1                   => 'one',
		        2                   => 'two',
		        3                   => 'three',
		        4                   => 'four',
		        5                   => 'five',
		        6                   => 'six',
		        7                   => 'seven',
		        8                   => 'eight',
		        9                   => 'nine',
		        10                  => 'ten',
		        11                  => 'eleven',
		        12                  => 'twelve',
		        13                  => 'thirteen',
		        14                  => 'fourteen',
		        15                  => 'fifteen',
		        16                  => 'sixteen',
		        17                  => 'seventeen',
		        18                  => 'eighteen',
		        19                  => 'nineteen',
		        20                  => 'twenty',
		        30                  => 'thirty',
		        40                  => 'fourty',
		        50                  => 'fifty',
		        60                  => 'sixty',
		        70                  => 'seventy',
		        80                  => 'eighty',
		        90                  => 'ninety',
		        100                 => 'hundred',
		        1000                => 'thousand',
		        100000             => 'lakh',
		        10000000          => 'crore'
		    );
		    foreach($dictionary as $i=>$d){
		    	$dictionary[$i] =  ucfirst($d);
		    }

		    if (!is_numeric($number)) {
		        return false;
		    }

		    if (($number >= 0 && (int) $number < 0) || (int) $number < 0 - PHP_INT_MAX) {
		        // overflow
		        trigger_error(
		            'amountToWords only accepts numbers between -' . PHP_INT_MAX . ' and ' . PHP_INT_MAX,
		            E_USER_WARNING
		        );
		        return false;
		    }

		    if ($number < 0) {
		        return $negative . $this->amountToWords(abs($number));
		    }

		    $string = $fraction = null;

		    if (strpos($number, '.') !== false) {
		        list($number, $fraction) = explode('.', $number);
		    }

		    switch (true) {
		        case $number < 21:
		            $string = $dictionary[$number];
		            break;
		        case $number < 100:
		            $tens   = ((int) ($number / 10)) * 10;
		            $units  = $number % 10;
		            $string = $dictionary[$tens];
		            if ($units) {
		                $string .= $hyphen . $dictionary[$units];
		            }
		            break;
		        case $number < 1000:
		            $hundreds  = $number / 100;
		            $remainder = $number % 100;
		            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
		            if ($remainder) {
		                $string .= $conjunction . $this->amountToWords($remainder);
		            }
		            break;
		        case $number < 100000:
		            $thousands   = ((int) ($number / 1000));
		            $remainder = $number % 1000;

		            $thousands = $this->amountToWords($thousands);

		            $string .= $thousands . ' ' . $dictionary[1000];
		            if ($remainder) {
		                $string .= $separator . $this->amountToWords($remainder);
		            }
		            break;
		        case $number < 10000000:
		            $lakhs   = ((int) ($number / 100000));
		            $remainder = $number % 100000;

		            $lakhs = $this->amountToWords($lakhs);

		            $string = $lakhs . ' ' . $dictionary[100000];
		            if ($remainder) {
		                $string .= $separator . $this->amountToWords($remainder);
		            }
		            break;
		        case $number < 1000000000:
		            $crores   = ((int) ($number / 10000000));
		            $remainder = $number % 10000000;

		            $crores = $this->amountToWords($crores);

		            $string = $crores . ' ' . $dictionary[10000000];
		            if ($remainder) {
		                $string .= $separator . $this->amountToWords($remainder);
		            }
		            break;
		        default:
		            $baseUnit = pow(1000, floor(log($number, 1000)));
		            $numBaseUnits = (int) ($number / $baseUnit);
		            $remainder = $number % $baseUnit;
		            $string = $this->amountToWords($numBaseUnits) . ' ' . $dictionary[$baseUnit];
		            if ($remainder) {
		                $string .= $remainder < 100 ? $conjunction : $separator;
		                $string .= $this->amountToWords($remainder);
		            }
		            break;
		    }

		    if (null !== $fraction && is_numeric($fraction)) {
		        $string .= $decimal;
		        
		        $words = array($fraction.'/100');
		        /*foreach (str_split((string) $fraction) as $number) {
		            $words[] = $dictionary[$number];
		        }*/
		        $string .= implode(' ', $words);
		    }

		    return $string;

		}
	}
?>