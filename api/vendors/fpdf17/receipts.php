<?php
	require ('formsheet.php');
	class ReceiptSheet extends Formsheet{
		function __construct(){
			$this->showLines = true;
			$this->_width=212;
			$this->_height=109;
			$this->_unit = 'mm';
			$this->_orient='L';
			
			parent::__construct();
		}
		
		function hdr($data){
			
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>212,
					'height'=>109,
					'rows'=>20,
					'cols'=>20,
			);
			$this->section($metrics);
			
			$x = 0;
			$y = 0;
			$w =  212;
			$h = 109;

			$path = __DIR__ .'/images'.DS.'collection.png';
			$this->DrawImage($x,$y,$w,$h,$path);
			$this->showLines = false;
			
			$this->leftText(17,4,'C-'.$data['cr_no'],null,'');
			$this->leftText(16,7,$data['transac_date'],null,'');
			$this->leftText(10,8,$data['received_from'],null,'');
			$this->leftText(8,9,$data['address'],null,'');
			$this->leftText(17,9,$data['tin'],null,'');
			$this->leftText(11,10,$data['business_style'],null,'');
			$amount_words =  $this->amountToWords($data['total_per_page']);
			$this->leftText(8,11,$amount_words,null,'');
			$this->rightText(18,12,number_format($data['total_per_page'],2),null,'');
			$this->leftText(10,13,$data['payment_for'],null,'');
			$this->leftText(15,15,$data['auth_rep'],null,'');
			$this->rightText(5,15.25,number_format($data['total_per_page'],2),null,'');
			if($data['payment_form']=='check'){
				$this->leftText(4.5,17.8,'X',null,'');
				$this->leftText(7,15,'Check details : '.$data['payment_detail'],null,'');
			}
			else
				$this->leftText(1.5,17.8,'X',null,'');
				
		}
		
		function data($hdr, $data, $total_page, $page){
			
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>212,
					'height'=>109,
					'rows'=>20,
					'cols'=>20,
			);
			$this->section($metrics);
			
			$lineCtr =5.5;
			foreach($data as $i=>$item){
				$this->leftText(1,$lineCtr,$item['invoice_no'],'','');
				$this->rightText(5,$lineCtr,number_format($item['amount'],2),'','');
				$lineCtr+=1.22;
			}
		}
		
		function test($data){
			//createGrid($base_x ,$base_y,$width,$height,$rows,$cols)
			
			
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>212,
					'height'=>109,
					'rows'=>20,
					'cols'=>20,
			);
			$this->section($metrics);
			
			
			$x = 0;
			$y = 0;
			$w =  212;
			$h = 109;

			$path = __DIR__ .'/images'.DS.'collection.png';
			$this->DrawImage($x,$y,$w,$h,$path);
			$this->showLines = true;

			//$metrics['width']=2;
			//$metrics['base_y'] = 1;
			//$this->section($metrics);
			//$this->leftText(1,1,'Account Name:',null,'b');
			//$this->leftText(2,6,$data['dr_no'],null,'');
			//$this->leftText(4,6,$data['amount_paid'],null,'');
			//$this->leftText(5,6,'.50',null,'');
			$this->leftText(16,7,$data['transac_date'],null,'');
			$this->leftText(10,8,$data['received_from'],null,'');
			$this->leftText(8,9,$data['address'],null,'');
			$this->leftText(17,9,$data['tin'],null,'');
			$this->leftText(11,10,$data['business_style'],null,'');
			$this->leftText(8,11,$data['amount_words'],null,'');
			$this->rightText(18,12,number_format($data['total_amount'],2),null,'');
			$this->leftText(10,13,$data['payment_for'],null,'');
			$this->leftText(15,15,$data['auth_rep'],null,'');
			$this->rightText(5,15.25,number_format($data['total_amount'],2),null,'');
			if($data['payment_form']=='check')
				$this->leftText(4.5,17.8,'X',null,'');
			else
				$this->leftText(1.5,17.8,'X',null,'');
				
			
			$x = 0.5;
			$w = 3;
			$y = 1;
			$h = 0.5;
			$fill = 'D';
			$record = array(
						'customer'=>'062',
						'details'=>array('1','2'),
						'grand_total' =>500
						);

			
			
			/* for($col =0; $col<=2;$col++){
				$W =  $col*$w;
				//$this->DrawBox($x+$W,$y,$w,$h,$fill);
				foreach($record['details'] as $item){
					//$this->leftText()
					$lineCtr++;
				}
			} */
			$lineCtr =5.5;
			foreach($data['settlements'] as $i=>$item){
				$this->leftText(1,$lineCtr,$item['invoice_no'],'','');
				$this->rightText(5,$lineCtr,number_format($item['amount'],2),'','');
				$lineCtr+=1.22;
			}
		}
		function info($info){
			$this->GRID['font_size']=10;
			$this->leftText(1,3.5,'Account Name:',null,'b');
			$this->leftText(3,3.5,$info['account'],null);
			$this->leftText(1,4,'Billing Period:',null,'b');
			$this->leftText(3,4,$info['detail']['cutoff'].', '.date('Y',time()),null);
			$this->leftText(10.25,3.5,'Date:',null,'b');
			$this->leftText(11.75,3.5,$info['date'],null);
			$this->leftText(10.25,4,'Due Date:',null,'b');
			$this->leftText(11.75,4,$info['due_date'],null);
		}
	}