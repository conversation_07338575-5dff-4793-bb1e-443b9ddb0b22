<?php
	require ('formsheet.php');
	class ChargeInvoice extends Formsheet{
		function __construct(){
			//$this->showLines = true;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='P';
			
			parent::__construct();
		}
		
		function hdr($data){
			
		}
		
		function data($hdr,$data,$total_per_page,$page,$version='2024'){
			//pr($data); exit();
			$isCashInvoice = $hdr['terms']=='CASH';
			$metrics = array(
					'base_x'=>0,
					'base_y'=>-1.25,
					'width'=>138,
					'height'=>212,
					'rows'=>28,
					'cols'=>12,
			);
			if($isCashInvoice)
				$metrics['base_y']=-2.8;
			$this->showLines = !true;
			$this->section($metrics);
			
			
			$x = 1.5;
			$y = 0;
			$w =  138;
			$h = 212;


			$path = __DIR__ .'/images'.DS.'RAILIM_SI_2024.png';
			//$this->DrawImage($x,$y,$w,$h,$path);
			//$this->showLines = true;

			
			if($isCashInvoice)
				$metrics['base_y']=-2.8;
			//$this->showLines = true;
			$this->section($metrics);

			$this->GRID['font_size']=10;
		
				$this->leftText(8.5,4,$hdr['csi_no'],null,'I');
	
			$this->GRID['font_size']=9;
			
			$bizOffset=-1.4;
			$this->leftText(2.7,7.75+$bizOffset,$hdr['charged_to'],null,'');
			$this->leftText(3.95,8.5+$bizOffset,$hdr['business_style'],null,'');
			$this->leftText(2.05,9.25+$bizOffset,$hdr['tin'],null,'');
			$this->leftText(2.9,10+$bizOffset,$hdr['address'],null,'');
		
		
			$this->leftText(8.4,7+$bizOffset,$hdr['transac_date'],null,'');
			$this->leftText(2.6,7+$bizOffset,'Terms: '.$hdr['terms'],null,'');
			
		
			//$this->leftText(11.5,6.45+$bizOffset,$hdr['osca_pwd_id_no'],null,'');
			
			//$this->leftText(9,6.4,$data['terms'],null,'');
			$taxOffset =  20.1;
			$taxHeight = 0.65;
			
			if($hdr['vat_type']=='zero-rated'):
				$SALES_X = 3.5;
				
				$this->GRID['font_size']=8.5;
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['total_sales'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format(0,2),0.5,'');
				$taxOffset =  20.1;
				$SALES_X = 9.9;
				$this->GRID['font_size']=8.5;
				$taxOffset-=0.9;

				$hdr['total_sales'] = 0;
				$hdr['net_of_vat'] = 0;
				$this->rightText($SALES_X,$taxOffset,number_format($hdr['total_sales'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['less_vat'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['net_of_vat'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['less_sc_pwd_disc'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['add_vat'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['with_holding_tax'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['total_amount_due'],2),0.5,'');
				$this->GRID['font_size']=9;
				$taxOffset+=0.5;
				$this->leftText(2,$taxOffset+1.5,'Received by:',null,'');
				$this->leftText(4,$taxOffset+1.5,'Prepared by:'.$hdr['cshr_auth_rep'],null,'');
				$this->GRID['font_size']=9;
				$taxOffset = 0.5;
				$y=17.75+$taxOffset;
			else:
				$SALES_X = 9.9;
				$this->GRID['font_size']=8.5;
				$taxOffset-=0.9;
				$this->rightText($SALES_X,$taxOffset,number_format($hdr['total_sales'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['less_vat'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['net_of_vat'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['less_sc_pwd_disc'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['add_vat'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['with_holding_tax'],2),0.5,'');
				$this->rightText($SALES_X,$taxOffset+=$taxHeight,number_format($hdr['total_amount_due'],2),0.5,'');
				$this->GRID['font_size']=9;
				$taxOffset+=0.5;
				$this->leftText(2,$taxOffset+1.5,'Received by:',null,'');
				$this->leftText(4,$taxOffset+1.5,'Prepared by:'.$hdr['cshr_auth_rep'],null,'');
				$this->GRID['font_size']=9;
				$taxOffset = 0.5;
				$y=17.75+$taxOffset;
			endif;

			
			if($hdr['vat_type']=='zero-rated')
				$y-=0.725;
			$this->leftText(1.25,$y+=0.725,'PO-'.$hdr['po_no'].' // '.$hdr['csi_no'],null,'');
			if($hdr['vat_type']=='zero-rated'):
				$this->leftText(1.25,$y+=0.75,$hdr['dr_no'],null,'');
			endif;
			//$this->leftText(4.8,$y+=0.8,'v3',null,'');
			$x = 0.5;
			$w = 3;
			$y = 1;
			$h = 0.5;
			$fill = 'D';
			$record = array(
						'customer'=>'062',
						'details'=>array('1','2'),
						'grand_total' =>500
						);

			
			$lineCtr =10.55;
			$itemXOffset =1.25;
			//$this->DrawLine(2.6,'v',array(18.5,0.75));
			//$this->DrawLine(18.85,'h',array(2.25,0.75));
// WGA LIKUTI ANG MOUSE
			//$this->DrawBox(2.25,18.5,0.75,0.75,'d');
			
			foreach($data as $i=>$item){
				
				$this->GRID['font_size'] = 7.5;
				$this->leftText(-0.25+$itemXOffset,$lineCtr,$item['description'],'','');
				$this->GRID['font_size'] = 7.5;
				$this->GRID['font_size'] = 9;
				$this->leftText(5.45+$itemXOffset,$lineCtr,$item['qty'],'','');
				if($item['u_price'])
				$this->rightText(7.8,$lineCtr-0.6,number_format($item['u_price'],2),0.5,'');
				$this->rightText(9.9,$lineCtr-0.6,number_format($item['amount'],2),0.5,'');
				$lineCtr+=.8;
			}
		}
		
	}