<?php
	require ('formsheet.php');
	class ChargeInvoice extends Formsheet{
		function __construct(){
			//$this->showLines = true;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='P';
			
			parent::__construct();
		}
		
		function hdr($data){
			
		}
		
		function data($hdr,$data,$total_per_page,$page,$version='2024'){
			//pr($data); exit();
			$isCashInvoice = $hdr['terms']=='CASH';
			$metrics = array(
					'base_x'=>0,
					'base_y'=>-1.25,
					'width'=>138,
					'height'=>212,
					'rows'=>28,
					'cols'=>12,
			);
			if($isCashInvoice)
				$metrics['base_y']=-2.8;
			//$this->showLines = true;
			$this->section($metrics);
			
			
			$x = 1.5;
			$y = 0;
			$w =  138;
			$h = 212;


			$path = __DIR__ .'/images'.DS.'RAILIM_SI_2024.png';
			//$this->DrawImage($x,$y,$w,$h,$path);
			//$this->showLines = true;

			
			if($isCashInvoice)
				$metrics['base_y']=-2.8;
			//$this->showLines = true;
			$this->section($metrics);

			$this->GRID['font_size']=10;
			if($isCashInvoice):
				$this->leftText(10.75,3.3,$hdr['csi_no'],null,'I');
			else:
				$this->leftText(10.75,3.2,$hdr['csi_no'],null,'I');
			endif;
			$this->GRID['font_size']=9;
			
			$bizOffset=$isCashInvoice?-1:0;
			$this->leftText(4.1,5+$bizOffset,$hdr['charged_to'],null,'');
			$this->leftText(3.5,5.7+$bizOffset,$hdr['tin'],null,'');
			$this->wrapText(3.6,6+$bizOffset,$hdr['address'],5,'l',0.5);
			$this->leftText(4,7.5+$bizOffset,$hdr['business_style'],null,'');

		
			if($isCashInvoice):
				$this->leftText(11,5+$bizOffset,$hdr['transac_date'],null,'');
				$this->leftText(11.5,5.7+$bizOffset,$hdr['terms'],null,'');
			else:
				$this->leftText(9.75,5+$bizOffset,$hdr['transac_date'],null,'');
				$this->leftText(9.75,5.75+$bizOffset,$hdr['terms'],null,'');
			endif;
		
			$this->leftText(11.5,6.45+$bizOffset,$hdr['osca_pwd_id_no'],null,'');
			
			//$this->leftText(9,6.4,$data['terms'],null,'');
			$taxOffset =  1.3;
			
			
			$this->rightText(12.2,16.2+$taxOffset,number_format($hdr['total_sales'],2),null,'');
			$this->rightText(12.2,17+$taxOffset,number_format($hdr['less_vat'],2),null,'');
			$this->rightText(12.2,17.8+$taxOffset,number_format($hdr['net_of_vat'],2),null,'');
			$this->rightText(12.2,18.6+$taxOffset,number_format($hdr['less_sc_pwd_disc'],2),null,'');
			$this->rightText(12.2,19.25+$taxOffset,number_format($hdr['amount_due'],2),null,'');
			$this->rightText(12.2,20+$taxOffset,number_format($hdr['add_vat'],2),null,'');
			$this->rightText(12.2,20.8+$taxOffset,number_format($hdr['total_amount_due'],2),null,'');
			$this->leftText(9.5,22.75+$taxOffset,$hdr['cshr_auth_rep'],null,'');
			$this->GRID['font_size']=6.5;
			$this->leftText(2.2,3+$taxOffset,'v2024',null,'');
			$this->GRID['font_size']=9;
			$y=15.5+$taxOffset;

			
			if($hdr['vat_type']=='vat')
				$this->leftText(4.8,$y+=0.725,number_format($hdr['vatable_sales'],2),null,'');
			$this->leftText(4.8,$y+=0.725,'PO-'.$hdr['po_no'].' // '.$hdr['csi_no'],null,'');
			if($hdr['vat_type']=='zero-rated'):
				$this->leftText(4.8,$y+=0.75,$hdr['dr_no'].'  Zero-rated',null,'');
			endif;
			//$this->leftText(4.8,$y+=0.8,'v3',null,'');
			$x = 0.5;
			$w = 3;
			$y = 1;
			$h = 0.5;
			$fill = 'D';
			$record = array(
						'customer'=>'062',
						'details'=>array('1','2'),
						'grand_total' =>500
						);

			
			$lineCtr =$isCashInvoice?9.05:9.4;
			$itemXOffset = 0.5;
			$this->DrawLine(2.6,'v',array(18.5,0.75));
			$this->DrawLine(18.85,'h',array(2.25,0.75));

			//$this->DrawBox(2.25,18.5,0.75,0.75,'d');
			
			foreach($data as $i=>$item){
				$this->GRID['font_size'] = 9;
				$this->leftText(2+$itemXOffset,$lineCtr,$item['qty'],'','');
				$this->leftText(3.05+$itemXOffset,$lineCtr,$item['unit'],'','');
				$this->GRID['font_size'] = 8.5;
				$this->leftText(4.05+$itemXOffset,$lineCtr,$item['description'],'','');
				$this->GRID['font_size'] = 8.5;
				if($item['u_price'])
				$this->rightText(10.1,$lineCtr,number_format($item['u_price'],2),0.5,'');
				$this->rightText(11.65,$lineCtr,number_format($item['amount'],2),0.5,'');
				$lineCtr+=.8;
			}
		}
		
	}