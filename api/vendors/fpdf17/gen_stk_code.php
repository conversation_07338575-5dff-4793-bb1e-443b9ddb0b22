<?php
	require ('formsheet.php');
	class StockCodeSticker extends Formsheet{
		function __construct(){
			//$this->showLines = true;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='L';
			
			parent::__construct();
		}
		function stickers($codes){
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>210,
					'height'=>297,
					'rows'=>14,
					'cols'=>7,
			);
			$this->showLines = true;
			$this->section($metrics);

			$w =1;
			$h =1;
			$index=0;
			$this->GRID['font_size']=12;
			for($r=1;$r<=14;$r++):
				for($c=1;$c<=10;$c++):
					$code = $codes[$index];
					$this->DrawBox($c-1,$r-1,$w,$h,'D');
					$this->centerText($c-1,$r-0.5,$code,1);		
					$index++;
				endfor;
			endfor;
		}
	}