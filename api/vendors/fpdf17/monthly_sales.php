<?php
	require ('formsheet.php');
	class MonthlySales extends Formsheet{
		public $version = '1.0.1';
		function __construct(){
			$this->showLines = false;
			$this->_width=210;
			$this->_height=297;
			$this->_unit = 'mm';
			$this->_orient='P';
			$this->_font = 20;
			
			parent::__construct();
		}
		function test($data,$startDate=0,$startCust=0,$oldDate=-1){
			//createGrid($base_x ,$base_y,$width,$height,$rows,$cols)
			
			
			$metrics = array(
					'base_x'=>0,
					'base_y'=>0,
					'width'=>297,
					'height'=>210,
					'rows'=>40,
					'cols'=>41,
			);
			$this->section($metrics);
			
			
			$x = 0;
			$y = 0;
			$w =  297;
			$h = 210;

			//$path = __DIR__ .'/images'.DS.'collection.png';
			//$this->DrawImage($x,$y,$w,$h,$path);
			//$this->showLines = true;
			
			//$metrics['width']=2;
			//$metrics['base_y'] = 1;
			//$this->section($metrics);
			//$this->leftText(1,1,'Account Name:',null,'b');
			//$this->leftText(2,6,$data['dr_no'],null,'');
			//$this->leftText(4,6,$data['amount_paid'],null,'');
			//$this->leftText(5,6,'.50',null,'');
			$this->GRID['font_size'] = 12;
			$monthCovered = strtoupper((date( 'M - Y',strtotime($data['coverage']['from']))));
			$monthEnd=   date( 'M d',strtotime($data['coverage']['to']));
			$this->leftText(10,2,'SALES FOR THE MONTH OF '.$monthCovered,3,'b');
			$this->DrawBox(1,3,3,2,'D');
			$this->DrawBox(4,3,4,2,'D');
			$this->DrawBox(8,3,3,2,'D');
			$this->DrawBox(11,3,3,2,'D');
			$this->DrawBox(14,3,3.5,2,'D');
			$this->DrawBox(17.5,3,3.5,2,'D');
			$this->DrawBox(21,3,3.5,2,'D');
			$this->DrawBox(24.5,3,3.5,2,'D');
			
			
			$this->GRID['font_size']=7;
			$this->leftText(1.5,4,'Invoice Date |',3,'b');
			$this->leftText(1.5,4.8,'Date Collected',3,'b');
			$this->leftText(5,4,'Customer',3,'b');
			$this->leftText(8.5,4,'Invoice No.',3,'b');
			$this->leftText(12,4,'DR No.',3,'b');
			$this->leftText(15.25,4,'Cash',3,'b');
			$this->leftText(18.5,4,'Check',3,'b');
			$this->leftText(22,4,'Terms',3,'b');
			$this->leftText(26,4,'CR',3,'b');
			
			
		
			$this->GRID['font_size']=7;
			$line = 6;
			$text_line = 5.8;
			$dates = array();
			$line_ctr  = 1;
			
			for($i=$startDate;$i<count($data['details']);$i++):
				$item = $data['details'][$i];
				$this->leftText(1.2,$text_line,$item['date'],null,'');
				$date_height = 0;
				for($j=$startCust;$j<count($item['data']);$j++):
					$d = $item['data'][$j];
					$this->leftText(4.5,$text_line,$d['customer'],null,'');
					$this->leftText(8.5,$text_line,$d['invoice_no'],null,'');
					$this->leftText(11.2,$text_line,$d['dr_no'],null,'');
					switch($d['type']){
						case 'cash': 
							$this->leftText(14.2,$text_line,number_format($d['amount'],2),null,'');
							break;
						case 'chck': 
							$this->leftText(17.7,$text_line,number_format($d['amount'],2),null,'');
							break;
						case 'term': 
							$this->leftText(21.2,$text_line,number_format($d['amount'],2),null,'');
							break;
					}
					
					$this->leftText(24.6,$text_line,$d['cr'],null,'');
					$this->DrawBox(4,$line-1,4,1,'D');
					$this->DrawBox(8,$line-1,3,1,'D');
					$this->DrawBox(11,$line-1,3,1,'D');
					$this->DrawBox(14,$line-1,3.5,1,'D');
					$this->DrawBox(17.5,$line-1,3.5,1,'D');
					$this->DrawBox(21,$line-1,3.5,1,'D');
					$this->DrawBox(24.5,$line-1,3.5,1,'D');
					$text_line++;
					$line++;
					$date_height++;
					
					if($line_ctr>=50){
						$this->DrawBox(1,$line-($date_height+1),3,$date_height,'D');
						$this->createSheet();
						return $this->test($data,$i,$j+1);
					}
					$line_ctr++;
					
					
				endfor;
				$startCust=0;
				$finDate = $date_height;
				$this->DrawBox(1,$line-($date_height+1),3,$date_height,'D');
			endfor;
			
			$dateLine = 6;
			$this->GRID['font_size']=8;
			$line+=5;
			$text_line = $line-.1;
			$this->leftText(22.2,$text_line,'Cash:',null,'b');
			$this->rightText(27.9,$text_line,number_format($data['totals']['cash'],2),null,'b');
			$this->DrawBox(22,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(22.2,$text_line,'Check:',null,'b');	
			$this->rightText(27.9,$text_line,number_format($data['totals']['check'],2),null,'b');
			$this->DrawBox(22,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(22.2,$text_line,'Terms:',null,'b');
			$this->rightText(27.9,$text_line,number_format($data['totals']['terms'],2),null,'b');
			$this->DrawBox(22,$line-1,6,1,'D');
			$line++;
			$text_line++;
			$this->leftText(22.2,$text_line,'Raitech:',null,'b');
			$this->rightText(27.9,$text_line,number_format($data['totals']['raitech'],2),null,'b');
			$this->DrawBox(22,$line-1,6,1,'D');
			$line+=3;
			$text_line+=3;
			$this->leftText(22.2,$text_line+.5,'Grand Total:',null,'b');
			$this->rightText(27.9,$text_line+.5,number_format($data['totals']['grand_total']),null,'b');
			$this->DrawBox(22,$line-1,6,2,'D');
			
			
		}
		
		
	}