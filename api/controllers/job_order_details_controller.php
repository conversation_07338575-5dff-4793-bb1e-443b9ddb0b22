<?php
class JobOrderDetailsController extends AppController {

	var $name = 'JobOrderDetails';

	function index() {
		$this->JobOrderDetail->recursive = 0;
		$this->set('jobOrderDetails', $this->paginate());
	}

	function view($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid job order detail', true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('jobOrderDetail', $this->JobOrderDetail->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->JobOrderDetail->create();
			if ($this->JobOrderDetail->save($this->data)) {
				$this->Session->setFlash(__('The job order detail has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The job order detail could not be saved. Please, try again.', true));
			}
		}
		$jobOrders = $this->JobOrderDetail->JobOrder->find('list');
		$products = $this->JobOrderDetail->Product->find('list');
		$this->set(compact('jobOrders', 'products'));
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->Session->setFlash(__('Invalid job order detail', true));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			if ($this->JobOrderDetail->save($this->data)) {
				$this->Session->setFlash(__('The job order detail has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The job order detail could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->JobOrderDetail->read(null, $id);
		}
		$jobOrders = $this->JobOrderDetail->JobOrder->find('list');
		$products = $this->JobOrderDetail->Product->find('list');
		$this->set(compact('jobOrders', 'products'));
	}

	function delete($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid id for job order detail', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->JobOrderDetail->delete($id)) {
			$this->Session->setFlash(__('Job order detail deleted', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->Session->setFlash(__('Job order detail was not deleted', true));
		$this->redirect(array('action' => 'index'));
	}
}
