<?php
class ApiController extends AppController {
	var $uses = null;

	function authenticate(){
		$data = $_POST['data'];
		$User = &ClassRegistry::init('User');
		$this->Session->write('USERID',null);
		$this->Session->write('USERNAME',null);
      	$login = array("User"=>$User->hashPasswords($data));
		if($this->Auth->login($login)){
			$user= $this->Auth->user();
			$userObj = $User->authenticate($data);
			$user['Module']=$userObj['Module'];
			$response = array();
			$response['status'] = '00';
			$response['message'] = 'User authenticated';
			$response['response'] = $user;
			$this->Session->write('USERID',$user['User']['id']);
			$this->Session->write('USERNAME',$user['User']['username']);

		}else{
			$response = array();
			$response['status'] = '404';
			$response['message'] = 'Invalid Credentials';
		}
		$this->header('Content-Type: application/json');
		echo json_encode($response,JSON_NUMERIC_CHECK );exit;
	}
	function logout(){
		$this->Session->write('USERID',null);
		$this->Session->write('USERNAME',null);
		$response = array();
		$response['status'] = '00';
		$response['message'] = 'User logged out';
		$this->header('Content-Type: application/json');
		echo json_encode($response,JSON_NUMERIC_CHECK );exit;
	}
	function renew(){
		$response['status'] = '00';
		$response['message'] = 'Session Renewed';
		$this->header('Content-Type: application/json');
		echo json_encode($response,JSON_NUMERIC_CHECK );exit;
	}
	function index($endpoint){
		date_default_timezone_set("Asia/Manila");
		Configure::write('debug', 2);
		$__Class = Inflector::classify($endpoint);
		$Endpoint = &ClassRegistry::init($__Class);
		$format = (isset($_GET['format']))?$_GET['format']:'json';
		if($this->RequestHandler->isGet()){
			$type='all';
			$Endpoint->recursive=$__Class=='Transaction'||$__Class=='CustomerLedger'||$__Class=='SupplierLedger' ||$__Class=='User'?2:-1;
			if($__Class=='PurchaseOrderDetail')
				$Endpoint->recursive = 1;
			$data = array();
			$conf =array();
			$conf['conditions']=array();
			if(!empty($_GET['data'])){
				$this->data =  $_GET['data'];
				if(isset($this->data['id'])||isset($this->data['ref_no'])){
					$type='first';
					if($__Class=='Transaction'){
						if(isset($this->data['type'])){
							$conf['conditions']=array(
									$__Class.'.ref_no'=>$this->data['ref_no'],
									$__Class.'.type'=>$this->data['type'],
								);
						}
					}
					else $conf['conditions']=array($__Class.'.id'=>$this->data['id']);
				}
			}else{
				$page = isset($_GET['page'])?$_GET['page']:1;
				if($format=='csv'||$format=='pdf'&&!isset($_GET['page'])){
					$page = null;
				}
				$limit = $conf['limit'] = isset($_GET['limit'])?$_GET['limit']:500;
				if($format=='csv'||$format=='pdf'&&!isset($_GET['limit'])){
					 $conf['limit'] = $limit = null;
				}
				$offset = $conf['offset'] = $page&&$limit?($page-1)*$limit:null;
				$keyword  = null;
				$fields = null;
				$filter = null;
				$export = null;
				$soa  = null;
				$last_bill  = null;
				$dashboard = null;
				if(isset($_GET['sort'])) {
					switch($_GET['sort']){
						case 'latest':
							if($__Class=='Transaction'||$__Class=='CustomerLedger'||$__Class=='SupplierLedger'):
								$conf['order']=array($__Class.'.timestamp DESC');
							elseif($__Class=='Order'):
								$conf['conditions']=array('Order.spo_no');
								$conf['order']=array($__Class.'.spo_no DESC');

							else:
								$conf['order']=array($__Class.'.modified DESC');
							endif;


						break;
						case 'oldest':
							if($__Class=='Transaction'||$__Class=='CustomerLedger'||$__Class=='SupplierLedger')
								$conf['order']=array($__Class.'.timestamp ASC');
							else
								$conf['order']=array($__Class.'.modified ASC');
						break;
						case 'alpha':
							if($__Class=='Customer' || $__Class =='Supplier')
								$conf['order']=array($__Class.'.name ASC');
						break;
					}
				}
				if(isset($_GET['keyword'])) $keyword = '%'.$_GET['keyword'].'%';
				//pr($_GET);exit;
				if($keyword&&isset($_GET['fields'])) $fields = explode(',',$_GET['fields']);
				if(isset($_GET['export'])) $export = explode(',',$_GET['export']);

				$hasPOFilter = null;
				$invIDs =  null;
				if($keyword && $fields){
					$cond = array();
					
					foreach($fields as $fld){
						if($fld=='entity_name'){
							$cond['OR'] = array();
							if($__Class!='SupplierLedger') $cond['OR']['Customer.name LIKE'] = $keyword;
							if($__Class!='CustomerLedger') $cond['OR']['Supplier.name LIKE'] = $keyword;
						}else if($fld=='po_no'){
							$hasPOFilter = array(
								'OR'=>array(
										'PurchaseOrder.po_no LIKE '=>$keyword,
										'PurchaseOrder.customer LIKE '=>$keyword
								)	
							);
							 $cond['OR']['PurchaseOrder.po_no LIKE'] =  $keyword;
							 $cond['OR']['PurchaseOrder.customer LIKE'] =  $keyword;
						}else if($fld=='si_no'){
							$Invoice = &ClassRegistry::init('Invoice');
							$invCond =  array(
								'OR'=>array(
									'Invoice.si_no LIKE'=>$keyword,
									'Invoice.customer LIKE'=>$keyword
									)
								);
							$invFld =  array('Invoice.purchase_order_id','Invoice.id');
							$invList = $Invoice->find('list',array('conditions'=>$invCond,'fields'=>$invFld));
							$invPOs =  array_keys($invList);
							$invIDs =  array_values($invList);

							if($invPOs):
							$cond['OR']['PurchaseOrder.id'] = $invPOs;
							$hasPOFilter=array('OR'=>array($hasPOFilter,
													array('PurchaseOrder.id'=>$invPOs)
												));
						 	endif;
							//$cond['OR'] = array();

							/*if($__Class!='Invoice') $cond['OR']['Invoice.si_no LIKE'] = $keyword;
							*/
						}else if($fld=='display_title'){
							$keyword =  $_GET['keyword'];
							preg_match("|(\s+\d+([\.]\d+)?)|", $keyword, $matches);
							$thickness = false;
							if(count($matches)){
								$thickness =  preg_replace('/([mM]{1,2})?/','',$matches[0]);
								$keyword = preg_replace('/\s+(\d+([\.]\d+)?)([mM]{1,2})?/', "", $keyword);

							}
							$keywords = explode(' ',$keyword);

							$regex = implode('[[:space:]]+', $keywords);
							if(!$thickness):
								$cond[] = array(
										'OR'=>array(
										"Product.particular REGEXP '$regex'", 
										"Product.description REGEXP '$regex'", 
										 "Product.thickness REGEXP '$regex'"
										)
									);
							else:
								$cond[] = array(
										'AND'=>array(
											'OR'=>
											array("Product.particular REGEXP '$regex'", 
												"Product.description REGEXP '$regex'", 
											),
										 "Product.thickness = $thickness"
										)
									);
							endif;

							//pr($cond);exit;
						}
						else $cond[$__Class.'.'.$fld.' LIKE'] = $keyword;
					}
					$conf['conditions'] = array_merge($conf['conditions'],array('OR'=>$cond));
				}

				if(isset($_GET['filter'])) $filter = json_decode($_GET['filter'],true);
				if(isset($_GET['soa'])) $soa =true;
				if(isset($_GET['last_bill'])) $last_bill =$_GET['last_bill'];
				if(isset($_GET['dashboard'])) $dashboard =$_GET['dashboard'];
				if($filter){
					$__filter = array();
					foreach($filter as $key=>$value){
						switch($__Class){
							case 'Product':
								switch($key){
									case 'category':
										if($value!='ALL') $__filter['Product.category_id'] = $value;
									break;
									case 'quantity':
										switch($value){
											case 'ZERO':
												array_push($__filter,'Product.soh_quantity = 0');
											break;
											case 'MIN':
												 array_push($__filter,'Product.soh_quantity < Product.min_quantity');
											break;
											case 'MAX':
												array_push($__filter,'Product.soh_quantity > Product.max_quantity');
											break;
											case 'ADJ':
												array_push($__filter,array('OR'=>array('Product.tmp_quantity','Product.tmp_srp')));
											break;
										}
									break;
									default:
										array_push($__filter,'Product.'.$key.' = \''.$value.'\'');
									break;
								}
							break;
							default:
								switch($key){
									case 'from':
										$__filter[$__Class.'.timestamp >='] = $value. ' 00:00:00';
									break;
									case 'to':
										$__filter[$__Class.'.timestamp <='] = $value. ' 23:59:59';
									break;
									case 'coverage':
										$today = date('Y-m-d', strtotime('0 days'));
										
										if($__Class=='Transaction'||$__Class=='CustomerLedger'||$__Class=='SupplierLedger')
											$key = 'timestamp';
										else
											$key = 'created';
										switch($value){
											case 'today':
												$__filter[$__Class.'.'.$key.' >='] = $today. ' 00:00:00';
												$__filter[$__Class.'.'.$key.' <='] = $today. ' 23:59:59';
											break;
											case 'yesterday':
												$yesterday = date('Y-m-d', strtotime('-1 day'));
												$firstDay = $yesterday;
												$lastDay = $today;
												$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
												$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
											break;
											case '7D':
												$lastWeek = date('Y-m-d', strtotime('-7 days'));
												$__filter[$__Class.'.'.$key.' >='] = $lastWeek. ' 00:00:00';
												$__filter[$__Class.'.'.$key.' <='] = $today. ' 23:59:59';
											break;
											case '30D':
												$lastMonth = date('Y-m-d', strtotime('-30 days'));
												$__filter[$__Class.'.'.$key.' >='] = $lastMonth. ' 00:00:00';
												$__filter[$__Class.'.'.$key.' <='] = $today. ' 23:59:59';
											break;
											case 'YTD':
												$lastMonth = date('Y-m-d', strtotime('-365 days'));
												$__filter[$__Class.'.'.$key.' >='] = $lastMonth. ' 00:00:00';
												$__filter[$__Class.'.'.$key.' <='] = $today. ' 23:59:59';
											break;
											case 'SOA': case 'MON':
												$bill_month = 'this';
												$current_month = (int) date('m', strtotime("this month"));
												if($last_bill &&  $last_bill == $current_month) $bill_month = "this";
												$firstDay = date('Y-m-d', strtotime("first day of $bill_month month"));

												// If SOA get all billable transactions
												if($value=='SOA')
													$firstDay = null;

												$lastDay = date('Y-m-d', strtotime("last day of $bill_month  month"));
												$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
												$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
											break;
										}
									break;
									default:
										if($key=='type'&&($__Class=='CustomerLedger'||$__Class=='SupplierLedger')) {
											if($value!='ALL') $__filter[$__Class.'.ref_no LIKE'] = $value.'%';
										}else if($key=='type'&&$value=='ALL'&&$__Class=='Transaction'){
											
										}else $__filter[$__Class.'.'.$key] = explode(',',$value);
									break;
								}
							break;
						}
					}
					
					if(count($__filter)) $conf['conditions'] = array_merge($conf['conditions'],array('AND'=>$__filter));
				}
				
				if($export){
					$conf['fields']=$export;
				}
				if($dashboard){
					$Endpoint->recursive = 0;
					if($__Class=='PurchaseOrderDetail'):
						switch($dashboard){
							case 'day':
								$conf['group'] = array('HOUR(PurchaseOrderDetail.created)');
								$conf['fields'] = array('SUM(PurchaseOrderDetail.quantity_area) as dash_amount','CONCAT(HOUR(PurchaseOrderDetail.created),":00") as dash_time');
							break;
							case 'week':
								$conf['group'] = array('DAY(PurchaseOrderDetail.created)');
								$conf['fields'] = array('SUM(PurchaseOrderDetail.quantity_area) as dash_amount','CONCAT(YEAR(PurchaseOrderDetail.created),"-",MONTH(PurchaseOrderDetail.created),"-", DAY(PurchaseOrderDetail.created) ) as dash_date');
							break;
							case 'month':
								$conf['group'] = array('MONTH(PurchaseOrderDetail.created)','DAY(PurchaseOrderDetail.created)');
								$conf['fields'] = array('SUM(PurchaseOrderDetail.quantity_area) as dash_amount','MONTH(PurchaseOrderDetail.created) as dash_month','CONCAT(YEAR(PurchaseOrderDetail.created),"-",MONTH(PurchaseOrderDetail.created),"-", DAY(PurchaseOrderDetail.created) ) as dash_date');
							break;
							case 'year':
								$conf['group'] = array('MONTH(PurchaseOrderDetail.created)');
								$conf['fields'] = array('SUM(PurchaseOrderDetail.quantity_area) as dash_amount','CONCAT(YEAR(PurchaseOrderDetail.created),"-",MONTH(PurchaseOrderDetail.created),"-", DAY(PurchaseOrderDetail.created) ) as dash_date');
							break;
						}
						
						$conf['group'] = array('Product.description');
						$conf['order'] = array('dash_amount'=>'desc');
						//$conf['limit'] =10;
						array_push($conf['fields'],'PurchaseOrderDetail.id'," UPPER(Product.description) as dash_item");

						
					else:
						switch($dashboard){
							case 'day':
								$conf['group'] = array('HOUR(Transaction.timestamp)');
								$conf['fields'] = array('SUM(Transaction.amount) as dash_amount','CONCAT(HOUR(Transaction.timestamp),":00") as dash_time');
							break;
							case 'week':
								$conf['group'] = array('DAY(Transaction.timestamp)');
								$conf['fields'] = array('SUM(Transaction.amount) as dash_amount','CONCAT(YEAR(Transaction.timestamp),"-",MONTH(Transaction.timestamp),"-", DAY(Transaction.timestamp) ) as dash_date');
							break;
							case 'month':
								$conf['group'] = array('MONTH(Transaction.timestamp)','DAY(Transaction.timestamp)');
								$conf['fields'] = array('SUM(Transaction.amount) as dash_amount','MONTH(Transaction.timestamp) as dash_month','CONCAT(YEAR(Transaction.timestamp),"-",MONTH(Transaction.timestamp),"-", DAY(Transaction.timestamp) ) as dash_date');
							break;
							case 'year':
								$conf['group'] = array('MONTH(Transaction.timestamp)');
								$conf['fields'] = array('SUM(Transaction.amount) as dash_amount','CONCAT(YEAR(Transaction.timestamp),"-",MONTH(Transaction.timestamp),"-", DAY(Transaction.timestamp) ) as dash_date');
							break;
						}
					endif;
				}
				
				
				$count_conf = $conf;
				
				unset($count_conf['limit']);
				unset($count_conf['offset']);

				// Bind additional models for Transaction
				if($__Class=='Transaction'){
					if(isset($filter['type'])){
						switch($filter['type']){
							case 'po':
								$belongsTo = array(
									'PurchaseOrder' => array(
									'className' => 'PurchaseOrder',
									'foreignKey' => 'ref_no',
									'conditions' => $hasPOFilter,
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;

							case 'jo':
								$belongsTo = array(
									'JobOrder' => array(
									'className' => 'JobOrder',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => ''),

								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;

							case 'invoice':
								$belongsTo = array(
									'Invoice' => array(
									'className' => 'Invoice',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;
							case 'orders':
								$belongsTo = array(
									'Order' => array(
									'className' => 'Order',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;
							case 'bill':
								$belongsTo = array(
									'Billing' => array(
									'className' => 'Billing',
									'foreignKey' => 'ref_no',
									'conditions' => array('Billing.id = Transaction.ref_no'),
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;
						}
					}
					if(isset($filter['coverage'])){
						switch($filter['coverage']){
							case 'SOA':
								$belongsTo = array(
									'Invoice' => array(
									'className' => 'Invoice',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => '')

								);
								
								$conf['fields'][]='ref_no';
								$conf['fields'][]='Invoice.si_no';
								$conf['fields'][]='Invoice.cr_no';
								$conf['fields'][]='Invoice.po_no';
								unset($conf['conditions']['OR']['OR']['PurchaseOrder.po_no LIKE']);
								unset($conf['conditions']['OR']['OR']['PurchaseOrder.id']);
								unset($count_conf['conditions']['OR']['OR']['PurchaseOrder.po_no LIKE']);
								unset($count_conf['conditions']['OR']['OR']['PurchaseOrder.id']);
								
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
								//pr($Endpoint->belongsTo);

							break;
						}
					}
				}
				
				
				$count = $Endpoint->find('count',$count_conf);
				
				$last = $limit?ceil($count/$limit):1;
				$next = $page < $last ? $page + 1:null;
				$prev = $page>1?$page - 1:null;
			}
			$meta = array();
			$scheme = 'https';
			$base_url = $scheme.'://'.$_SERVER['HTTP_HOST'];
			$href = $base_url.$_SERVER['REQUEST_URI'];
			$meta['href'] = $href;

			if($type=='all'){
				$get_vars = array();
				foreach($_GET as $get_key=>$get_val){
					if($get_key!='url'&&$get_key!='page'){
						array_push($get_vars,$get_key.'='.$get_val);
					}
				}
				$path = parse_url($href, PHP_URL_PATH);
				$page_url = '/'.$endpoint.'?'.implode('&',$get_vars).'&page=';
				if(!$dashboard){
					$meta['next'] = $next? $page_url.$next:null;
					$meta['prev'] = $prev? $page_url.$prev:null;
					$meta['last'] = $page_url.$last;
					$meta['items'] = $count;
					$meta['pages'] = $last;
				}else{
					foreach($filter as $key=>$value)
						$meta[$key]=$value;
				}
				if($dashboard){
					$dash_prev_month =  $dash_curr_month = null;
					$dash_prev_year =  $dash_curr_year = null;
					$dash_coverage = $dashboard=='day';
					if(!isset($filter['coverage']))
						$filter['coverage'] = 'YTD';
					$dash_year =  $filter['coverage']=='YTD';
				}

				// Bind additional models for Transaction
				if($__Class=='Transaction'){
					if(isset($filter['type'])){
						switch($filter['type']){
							case 'po':
								$belongsTo = array(
									'PurchaseOrder' => array(
									'className' => 'PurchaseOrder',
									'foreignKey' => 'ref_no',
									'conditions' => $hasPOFilter,
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;

							case 'jo':
								$belongsTo = array(
									'JobOrder' => array(
									'className' => 'JobOrder',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;

							case 'invoice':
								$belongsTo = array(
									'Invoice' => array(
									'className' => 'Invoice',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;
							case 'orders':
								$belongsTo = array(
									'Order' => array(
									'className' => 'Order',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;

							case 'bill':
								$belongsTo = array(
									'Billing' => array(
									'className' => 'Billing',
									'foreignKey' => 'ref_no',
									'conditions' => array('Billing.id = Transaction.ref_no'),
									'order' => '')
								);
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;
						}
					}
					if(isset($filter['coverage'])){
						switch($filter['coverage']){
							case 'SOA':
								$belongsTo = array(
									'Invoice' => array(
									'className' => 'Invoice',
									'foreignKey' => 'ref_no',
									'conditions' => null,
									'order' => '')
								);
								$conf['fields'][]='ref_no';
								$conf['fields'][]='Invoice.si_no';
								$conf['fields'][]='Invoice.cr_no';
								$Endpoint->bindModel(array('belongsTo'=>$belongsTo));
							break;
						}
					}
				}
				if($__Class=='Product' && $export){
					$conf['order']=array('Product.display_title'=>'asc');
					$conf['fields'][] = 'Product.id';
					
				}

				$results = $Endpoint->find($type,$conf);
				if($results)
				foreach($results as $ei=>$ep){

					if($__Class=='Product'){
						$ProductStock = &ClassRegistry::init('ProductStock');
						$stocks  = array();
						if(isset($ep[$__Class]['id']))
						$stocks =  $ProductStock->getStockdetails($ep[$__Class]['id']);
					
						$stk_qty = 0;
						$stk_area = 0;
						$org_area = 0;
						$ini_qty = 0;
						$ini_area = 0;
						foreach($stocks as $s){
							$stk_area +=$s['area_actual'];
							$stk_qty +=$s['quantity_actual'];
							$ini_qty +=$s['quantity'];
							$ini_area +=$s['quantity'] * $s['area'];
						}
						$ep[$__Class]['stock_quantity'] =   $stk_qty;
						$ep[$__Class]['stock_area'] =  $stk_area;
						$ep[$__Class]['ini_quantity'] =  $ini_qty;
						$ep[$__Class]['ini_area'] =  $ini_area;

						
					}
					if($__Class=='PurchaseOrderDetail' && !$dashboard){
						if(isset($ep['Product']['id'])){
							$ep[$__Class]['particular'] =$ep['Product']['particular'];		
							$ep[$__Class]['unit'] =$ep['Product']['unit'];		
							$ep[$__Class]['part_no'] =$ep['Product']['part_no'];		
						}
						
					}

					if($__Class=='Customer'){
							// Update balances
							$Ledger = &ClassRegistry::init($__Class.'Ledger');
							$custObj =  $ep['Customer'];
							$entity = $custObj;
							$bill_month = 'this';
							$current_month = (int) date('m', strtotime("this month"));
							if($custObj['last_bill'] &&  $custObj['last_bill'] == $current_month) $bill_month = "this";
							if(!$custObj['last_bill'] ){
								$bill_month = '-1';
								
							}
							$balance =  $custObj['begin_balance'];
							foreach($Ledger->getBalances($balance,$custObj['id'],$bill_month) as $key=>$value){
								$ep['Customer'][$key] = $value;
							}
							
							
						}

						if($__Class=='CustomerLedger'){
							
							$Transaction = &ClassRegistry::init('Transaction');
							$tCond = array('ref_no'=>$ep[$__Class]['ref_no'],'amount'=>$ep[$__Class]['amount']);
							$Trnx = $Transaction->find('first',array('conditions'=>$tCond));

							if($Trnx['Transaction']['type']=='payment'){
								$particulars  =$Trnx['TransactionPayment'][0]['detail'];
								$ep[$__Class]['particulars'] =  $particulars;
							}
							/* $insertInv  = array(
								'customer_id'=>153,
								'ref_no'=>'SI-4963',
								'particular'=>'15',
								'amount'=>'12445',
								'timestamp'=>'2021-06-16 01:23:45',
								
								
							);
							$Endpoint->save($insertInv); */
						}
					array_push($data,$ep[$__Class]);

					if($Endpoint->recursive==2 || $dashboard){
						foreach($ep as $epk => $epv){

							if($epk=='Supplier' && $__Class=='Transaction'){
								unset($epv['Delivery']);
								unset($epv['Order']);
							}
							if($dashboard &&  $epk=='0' ){
								$data[$ei]['amount']= $epv['dash_amount'];
								
								if($dash_year){
									$dash_time =  strtotime($epv['dash_date']);
									$dash_curr_year = date('Y',$dash_time);
									$data[$ei]['date']= date($dash_curr_year!=$dash_prev_year?'M Y':'M', $dash_time);
									$dash_prev_year = $dash_curr_year;	
								}else{
									$dash_time =  strtotime($epv[$dash_coverage?'dash_time':'dash_date']);
									$dash_curr_month = date('M',$dash_time);
									$data[$ei]['date']= date($dash_coverage?'h A':($dash_curr_month!=$dash_prev_month?'M d':'d'), $dash_time);
									$dash_prev_month = $dash_curr_month;	

								}

								if($__Class=='PurchaseOrderDetail'){
									$data[$ei]['item']= $epv['dash_item'];
									$data[$ei]['code']= $ei+1;
									unset($data[$ei]['date']);
								}
										
							} 
							else if($epk!=$__Class) {
								if($__Class=='Transaction' && $epk=='JobOrder'){
									
									$cond = array('type'=>'po','ref_no'=>$epv['purchase_order_id']);
									$PO_TRNX = $Endpoint->find('first',array('conditions'=>$cond))['Transaction'];
									$data[$ei]['discount'] = $PO_TRNX['discount'];
									$data[$ei]['discount_percent'] = $PO_TRNX['discount_percent'];

									if($PO_TRNX['status']=='cancelled'){
										$JO_TRNX= $Endpoint->findRelated('jo',$epv['id']);
										$Endpoint->updateStatus($JO_TRNX,'cancelled');
									}
								}
								if($__Class=='Transaction' && $epk=='PurchaseOrder'){
									$ProductStock = &ClassRegistry::init('ProductStock');
									foreach($epv['PurchaseOrderDetail'] as $p=>$dtl){
										$stocks =   $ProductStock->getStockdetails($dtl['product_id']);
										$epv['PurchaseOrderDetail'][$p]['stocks'] =  $stocks;
									}
									
								}
								$data[$ei][$epk]=$epv;
							}
						}
					}
				}
			}else{
				$result = $Endpoint->find($type,$conf);
				$data = $result[$__Class];
				switch($__Class){
					case 'User':
						$access = array();
						foreach($result['Module'] as $module){
							unset($module['ModuleUser']);
							array_push($access,$module);
						}
						unset($data['password']);
						$data['access'] = $access;
					break;
					case 'Product':
						$InventoryAdjustment = &ClassRegistry::init('InventoryAdjustment');
						$InventoryLog = &ClassRegistry::init('InventoryLog');
						$ProductStock = &ClassRegistry::init('ProductStock');

						$adjustment = $data['tmp_quantity']?$InventoryAdjustment->getLastAdjustment($data['id']):false;
						if($adjustment){
							$adjustment =  $adjustment[0]['inventory_adjustments'];
							
							$logs = $InventoryLog->getProductActivity($data['id'],$adjustment['created']);
							$adj_quantity =  $adjustment['tmp_quantity'];

							foreach($logs as $log){
								switch($log['inventory_logs']['source']){
									// POS -  Point of Sales
									// R2S -  Return to Supplier
									// CDL -  Cancel Delivery
									// CRC -  Cancel Return From Customer
									// CTI -  Cancel Trade In
									// JOB -  Job Order
									case 'POS': case 'R2S': case 'CDL': case 'CRC': case 'CTI': case 'JOB':
										$adj_quantity-=$log[0]['quantity'];
									break;

									// DEL -  Delivery
									// TIN -  Trade In
									// RFC -  Return From Customer
									// CSS -  Cancel Sales
									// CRS -  Cancel Return To Supplier
									case 'DEL': case 'TIN':  case 'RFC':  case 'CSS': case 'CRS':
										$adj_quantity+=$log[0]['quantity'];
									break;									
								}
							}
							$adjustment['adj_quantity'] = $adj_quantity;
							$adjustment['adj_qty_timestamp'] =date('M d Y h:i a ',strtotime($adjustment['created']));


						}else{
							$adjustment = null;
							$logs = null;
						}
						if($data['tmp_srp']){
							if(!$adjustment) $adjustment = array();
							$adjustment['tmp_srp']  = $data['tmp_srp'];
							$adjustment['adj_srp_timestamp']  = date('M d Y h:i a ',strtotime($data['created']));
						}

						$stocks =  $ProductStock->getStockdetails($data['id']);
						$data['adjustment']=$adjustment;
						$data['logs']=$logs;
						$data['stocks'] =  $stocks;
						$stk_qty = 0;
						$stk_area = 0;
						$ini_qty = 0;
						$ini_area = 0;
						foreach($stocks as $s){
							$stk_area +=$s['area_actual'];
							$stk_qty +=$s['quantity_actual'];
							$ini_qty +=$s['quantity'];
							$ini_area +=$s['quantity'] * $s['area'];
						}
						$data['stock_quantity'] =   $stk_qty;
						$data['stock_area'] =  $stk_area;
						$data['ini_quantity'] =  $ini_qty;
						$data['ini_area'] =  $ini_area;
					break;
					case 'Transaction':
						foreach($result as $key=>$value){
							if($key!='Transaction'){
								$data[$key] = $value;
							}
						}
					break;
					case 'Customer': case 'Supplier':
						$Entity = &ClassRegistry::init($__Class);
						$Ledger = &ClassRegistry::init($__Class.'Ledger');
						$entity = $Entity->findById($data['id']);
						$entity  = $entity [$__Class];
						$bill_month = 'this';
						$current_month = (int) date('m', strtotime("this month"));
						if($entity['last_bill'] &&  $entity['last_bill'] == $current_month) $bill_month = "this";
						if(!$entity['last_bill']){
							$bill_month = '-1';
						}
						$balance =  $data['begin_balance'];
						foreach($Ledger->getBalances($balance,$data['id'],$bill_month) as $key=>$value){
							$data[$key] = $value;
						}
						$data['allow_posting'] = ($entity['last_bill'] < $current_month || ($current_month==1 && $entity['last_bill']!=1) ||  abs($current_month - $entity['last_bill']) > 1  )  && $entity['status']=='open';
					break;
				}
			}
		}else if($this->RequestHandler->isPost()){
			$data = array();
			$meta = array();
			if(!empty($_POST['data'])){
				$this->data =  $_POST['data'];
				$this->data['header']['user'] =  $this->Session->read('USERNAME');
				$action = 'save';
				if(isset($this->data['action'])) $action = $this->data['action'];
				switch($action){
					case 'save':
						if($__Class=='Transaction'){
							$this->data['header']['timestamp'] = $this->data['header']['date']. date(' H:i:s', time());
							switch($this->data['header']['type']){
								case 'bill':
									// Handle billing creation
									$Billing = &ClassRegistry::init('Billing');
									$BillingDetail = &ClassRegistry::init('BillingDetail');
									$soa_no = $this->data['header']['soa_no'];
									$this->data['header']['id'] = $soa_no;
									$this->data['header']['status'] ='created';

									$payload = array(
										'customer_id'=>$this->data['header']['customer_id'],
										'date'=>$this->data['header']['date'],
										'amount'=>$this->data['header']['amount'],
										'terms'=>$this->data['header']['terms']
									);
									$this->data['header']['hash'] = md5(json_encode($payload));
									
									$Billing->save($this->data['header']);

									$this->data['header']['ref_no'] = $soa_no;
									$this->data['header']['user'] =  $this->Session->read('USERNAME');
									
									$this->data['header']['id']=null;
									$Endpoint->save($this->data['header']);
									
									$transaction_id = $Endpoint->id;


									// If we have details array, use it for billing details
									if (isset($this->data['details']) && !empty($this->data['details'])) {
										foreach ($this->data['details'] as $detail) {
											
											// Create billing detail record
											$billingDetail = array(
												'billing_id' => $soa_no,
												'ref_no' => $detail['ref_no'],
												'invoice_date' => $detail['invoice_date'],
												'invoice_amount' => $detail['invoice_amount'],
												'status' => 'billed'
											);
											$BillingDetail->create();
											$BillingDetail->save($billingDetail);

											// Update related invoice transaction
											$invoice_id = $detail['invoice_id'];
											$Endpoint->save(array('id'=>$invoice_id,'status'=>'billed'));
											
										}
									}

									
									

								break;
								case 'sales':
									$Invoice = &ClassRegistry::init('Invoice');
									if(isset($this->data['header']['customer']['name'])){
										$this->data['header']['customer'] = $this->data['header']['customer']['name'];
									}
									$Invoice->save($this->data['header']);									
									$invoice_id = $Invoice->id;
									$this->data['header']['ref_no'] = $invoice_id;
									
									
									$Endpoint->save($this->data['header']);
									$transaction_id = $Endpoint->id;
									
									$InvoiceDetail = &ClassRegistry::init('InvoiceDetail');
									
									foreach($this->data['details'] as $detail){
										$detail['id']=null;
										$detail['soh_quantity'] = $detail['quantity'];
										$detail['invoice_id']=$invoice_id;
										$detail['transaction_id']=$transaction_id;
										$InvoiceDetail->save($detail);
										$InvoiceDetail->Product->updateInventory(array($detail),-1,'POS');
										$InvoiceDetail->Product->updatePricing(array($detail),'tmp');
										$Endpoint->TransactionDetail->save($detail);
									}
									$InvoicePayment = &ClassRegistry::init('InvoicePayment');
									foreach($this->data['payments'] as $payment){
										$payment['id']=null;
										$payment['invoice_id']=$invoice_id;
										$payment['transaction_id']=$transaction_id;
										$InvoicePayment->save($payment);
										$Endpoint->TransactionPayment->save($payment);
										if($payment['payment_type']=='CHRG'){
											switch($this->data['header']['entity_type']){
												case 'customer':
													$CustomerLedger = &ClassRegistry::init('CustomerLedger');
													$entry = array('CustomerLedger'=>array(
																'id'=>null,
																'customer_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$this->data['header']['ref_no'],
																'particulars'=>$payment['detail'],
																'amount'=>$payment['amount'],
																'flag'=>$this->data['header']['type']=='sales'?'c':'d',
																'timestamp'=>$this->data['header']['timestamp'],
															));
													$CustomerLedger->save($entry);
												break;
											}
										}else if($payment['payment_type']=='CASH'){
											$CashFlow =  &ClassRegistry::init('CashFlow');
											$entry = array('CashFlow'=>array(
															'id'=>null,
															'particulars'=>'sales-'.$invoice_id,
															'amount'=>$this->data['header']['amount'],
															'flag'=>'c',//Income
															'timestamp'=>$this->data['header']['timestamp']

														));
											$CashFlow->save($entry);
										}
									}
								break;
								case 'deliveries':
									$Delivery = &ClassRegistry::init('Delivery');
									$ProductStock = &ClassRegistry::init('ProductStock');
									if(isset($this->data['header']['supplier']['name'])){
										$this->data['header']['supplier'] = $this->data['header']['supplier']['name'];
									}
									$this->data['header']['order_id']=$this->data['header']['doc_no'];
									$Delivery->save($this->data['header']);									
									$delivery_id = $Delivery->id;
									$this->data['header']['ref_no'] = $delivery_id;
									$si_no = $this->data['header']['si_no'];
									$Endpoint->save($this->data['header']);
									$transaction_id = $Endpoint->id;
									
									$DeliveryDetail = &ClassRegistry::init('DeliveryDetail');
									
									foreach($this->data['details'] as $detail){
										$detail['id']=null;
										$detail['price'] = $detail['capital'];
										$detail['soh_quantity'] = $detail['quantity'] = $detail['delivered_area'];
										$detail['delivery_id']=$delivery_id;
										$detail['transaction_id']=$transaction_id;
										$DeliveryDetail->save($detail);
										$DeliveryDetail->Product->updateInventory(array($detail),1,'DEL');
										$DeliveryDetail->Product->updatePricing(array($detail),'delivery',$delivery_id);
										$addToStocks = true;
										if($addToStocks):
											$code =  $ProductStock->generateStockCode();
											$stock = array(
											'id'=>$code,
											'type'=>'FL',
											'product_id'=>$detail['product_id'],
											'quantity'=>$detail['delivered'],
											'area'=>$detail['area'],
											'quantity_actual'=>$detail['delivered'],
											'area_actual'=>$detail['area'],
											'notes'=>$si_no
											);
											$ProductStock->save($stock);
										else:
											$stocks = $ProductStock->getStockdetails($detail['product_id']);
											$code = $stocks[0]['id'];
											$area = $detail['delivered_area'];
											$stock = array(
											'id'=>$code,
											'area'=>$area
											);
											$ProductStock->updateStocks(array($stock),1);
										endif;
										

										$Endpoint->TransactionDetail->save($detail);
									}
									
									$DeliveryPayment = &ClassRegistry::init('DeliveryPayment');
									
									foreach($this->data['payments'] as $payment){
										$payment['id']=null;
										$payment['delivery_id']=$delivery_id;
										$payment['transaction_id']=$transaction_id;
										$DeliveryPayment->save($payment);
										$Endpoint->TransactionPayment->save($payment);
										if($payment['payment_type']=='CHRG'){
											switch($this->data['header']['entity_type']){
												case 'supplier':
													$SupplierLedger = &ClassRegistry::init('SupplierLedger');
													$entry = array('SupplierLedger'=>array(
																'id'=>null,
																'supplier_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$this->data['header']['ref_no'],
																'particulars'=>$payment['detail'],
																'amount'=>$payment['amount'],
																'flag'=>$this->data['header']['type']=='deliveries'?'c':'d',
																'timestamp'=>$this->data['header']['timestamp'],
															));
													$SupplierLedger->save($entry);
												break;
											}
										}
									}
									if(isset($this->data['header']['doc_no'])){
										$doc_no = $this->data['header']['doc_no'];
										$delivery_date = $this->data['header']['delivery_date'];

										$Order = &ClassRegistry::init('Order');
										$order_trnx = $Endpoint->find('first',array('conditions'=>array('type'=>'orders','ref_no'=>$doc_no)));
										$trnx_id = $order_trnx[$__Class]['id'];

										$Order->save(array('id'=>$doc_no,'status'=>'fulfilled','delivery_date'=>$delivery_date));
										$Endpoint->save(array('id'=>$trnx_id,'status'=>'fulfilled'));
									}
									
								break;
								case 'orders':
									$Order = &ClassRegistry::init('Order');
									if(isset($this->data['header']['supplier']['name'])){
										$this->data['header']['supplier'] = $this->data['header']['supplier']['name'];
									}
									$User = &ClassRegistry::init('User');
									$user = $User->me()['User'];
									$prepare =  $user['first_name'].' '.$user['last_name'][0].'.';
									$this->data['header']['prepared_by']=$prepare;
									$Order->save($this->data['header']);									
									$order_id = $Order->id;
									$this->data['header']['ref_no'] = $order_id;
									
									$Endpoint->save($this->data['header']);
									$transaction_id = $Endpoint->id;
									
									$OrderDetail = &ClassRegistry::init('OrderDetail');
									
									foreach($this->data['details'] as $detail){
										$detail['id']=null;
										$detail['price'] = $detail['capital'];
										$detail['order_id']=$order_id;
										$detail['transaction_id']=$transaction_id;
										$OrderDetail->save($detail);
										$Endpoint->TransactionDetail->save($detail);
									}
								break;
								case 'tradein': case 'return':
									$Endpoint->save($this->data['header']);
									$transaction_id = $Endpoint->id;
									$Product = &ClassRegistry::init('Product');
									foreach($this->data['details'] as $detail){
										$detail['id']=null;
										$detail['transaction_id']=$transaction_id;
										$detail['soh_quantity'] = $detail['quantity'];
										$type = $this->data['header']['type'];
										switch($this->data['header']['entity_type']){
											case 'customer':
												if($type=='tradein') $type = 'TIN'; //Trade In
												else if($type=='return') $type = 'RFC'; //Return From Customer
												$Product->updateInventory(array($detail),1,$type);
											break;
											case 'supplier':
												if($type=='return') $type = 'R2S'; //Return To Supplier
												$Product->updateInventory(array($detail),-1,$type);
											break;
										}
										$Endpoint->TransactionDetail->save($detail);
									}
									foreach($this->data['payments'] as $payment){
										$payment['id']=null;
										$payment['transaction_id']=$transaction_id;
										$Endpoint->TransactionPayment->save($payment);
										$transac_date = $this->data['header']['transac_date'];
										if($payment['payment_type']=='CHRG'){
											switch($this->data['header']['entity_type']){
												case 'customer':
													$CustomerLedger = &ClassRegistry::init('CustomerLedger');
													$entry = array('CustomerLedger'=>array(
																'id'=>null,
																'customer_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$transac_date,
																'particulars'=>$payment['detail'],
																'amount'=>$payment['amount'],
																'flag'=>'d',
																'timestamp'=>$this->data['header']['timestamp'],
															));
													$CustomerLedger->save($entry);
												break;
												case 'supplier':
													$SupplierLedger = &ClassRegistry::init('SupplierLedger');
													$entry = array('SupplierLedger'=>array(
																'id'=>null,
																'supplier_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$transac_date,
																'particulars'=>$payment['detail'],
																'amount'=>$payment['amount'],
																'flag'=>'d',
																'timestamp'=>$this->data['header']['timestamp'],
															));
													$SupplierLedger->save($entry);
												break;
											}
										}else if($payment['payment_type']=='CASH'){
											$CashFlow =  &ClassRegistry::init('CashFlow');
											$entry = array('CashFlow'=>array(
															'id'=>null,
															'particulars'=>$this->data['header']['type'].'-'.$transac_date,
															'amount'=>$this->data['header']['amount'],
															'flag'=>'d', //Expense
															'timestamp'=>$this->data['header']['timestamp']

														));
											$CashFlow->save($entry);
										}
									}
								break;

								case 'po':  // Purchase Order Saving
									$PurchaseOrder = &ClassRegistry::init('PurchaseOrder');
									if(isset($this->data['header']['customer']['name'])){
										$this->data['header']['customer'] = $this->data['header']['customer']['name'];
									}
									$PurchaseOrder->save($this->data['header']);									
									$po_id = $PurchaseOrder->id;
									$po_no = $this->data['header']['po_no'];
									$this->data['header']['ref_no'] = $po_id;
									
									$Endpoint->save($this->data['header']);
									$transaction_id = $Endpoint->id;
									
									$PurchaseOrderDetail = &ClassRegistry::init('PurchaseOrderDetail');
									
									foreach($this->data['details'] as $detail){
										$detail['id']=null;
										$detail['soh_quantity'] = $detail['quantity'];
										$detail['purchase_order_id']=$po_id;
										$detail['transaction_id']=$transaction_id;
										$PurchaseOrderDetail->save($detail);

										// TODO Remove Inventory changes, implement in JO
										//$PurchaseOrderDetail->Product->updateInventory(array($detail),-1,'POS');
										//$PurchaseOrderDetail->Product->updatePricing(array($detail),'tmp');


										// Use quantity_area as quantity in TRNXDTL
										//$detail['quantity'] =  $detail['quantity_area'];
										$Endpoint->TransactionDetail->save($detail);
									}

									$PurchaseOrderTerm = &ClassRegistry::init('PurchaseOrderTerm');
									foreach($this->data['payments'] as $payment){
										$payment['id']=null;
										$payment['purchase_order_id']=$po_id;
										$payment['transaction_id']=$transaction_id;
										$PurchaseOrderTerm->save($payment);
										$Endpoint->TransactionPayment->save($payment);
										// Move to Sales Invoice module
										/*
										if($payment['payment_type']=='CHRG'){
											switch($this->data['header']['entity_type']){
												case 'customer':
													$CustomerLedger = &ClassRegistry::init('CustomerLedger');
													$entry = array('CustomerLedger'=>array(
																'id'=>null,
																'customer_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$this->data['header']['po_no'],
																'particulars'=>$payment['detail'],
																'amount'=>$payment['amount'],
																'flag'=>$this->data['header']['type']=='po'?'c':'d',
																'timestamp'=>$this->data['header']['timestamp'],
															));
													$CustomerLedger->save($entry);
												break;
											}
										}else if($payment['payment_type']=='CASH'){
											$CashFlow =  &ClassRegistry::init('CashFlow');
											$entry = array('CashFlow'=>array(
															'id'=>null,
															'particulars'=>'sales-'.$invoice_id,
															'amount'=>$this->data['header']['amount'],
															'flag'=>'c',//Income
															'timestamp'=>$this->data['header']['timestamp']

														));
											$CashFlow->save($entry);
										}*/
									}
								break;

								case 'jo':  // Job Order Saving
									$JobOrder = &ClassRegistry::init('JobOrder');

									$JobOrder->save($this->data['header']);

									$jo_id = $JobOrder->id;
									$po_no = $this->data['header']['po_no'];
									$this->data['header']['ref_no'] = $jo_id;
									
									$Endpoint->save($this->data['header']);
									$transaction_id = $Endpoint->id;
									
									$JobOrderDetail = &ClassRegistry::init('JobOrderDetail');
									$ProductStock = &ClassRegistry::init('ProductStock');
									foreach($this->data['details'] as $detail){
										$detail['id']=null;
										$detail['quantity'] =  $detail['quantity_actual'];
										$detail['job_order_id']=$jo_id;
										$detail['transaction_id']=$transaction_id;
										$JobOrderDetail->save($detail);
										$detail['quantity'] =  $detail['quantity_area_actual'];
										$detail['soh_quantity'] = $detail['quantity'];
										// Update inventory  
										$JobOrderDetail->Product->updateInventory(array($detail),-1,'JOB');

										// Update stocks if code is inputted
										if(isset($detail['code_actual'])):
											$stock = array(
												'id'=>$detail['code_actual'],
												'area'=>$detail['quantity_area_actual']
											);
											$ProductStock->updateStocks(array($stock),-1);
										endif;

										// Use quantity_area as quantity in TRNXDTL
										$Endpoint->TransactionDetail->save($detail);
									}
									$PO_TRNX = $this->data['header']['po_trnx_id'];
									$Endpoint->updateStatus($PO_TRNX,'inprogress');
								break;

								case 'invoice': 
									$Invoice = &ClassRegistry::init('Invoice');
									
									// Get all related trnx ids
									
									if(isset($this->data['header']['customer']['name'])){
										$this->data['header']['customer'] = $this->data['header']['customer']['name'];
									}
									$hasMultipleSI = false;
									$hasMultipleDR = false;
									$SIarr = explode('-', $this->data['header']['si_no']);
									if(!isset($this->data['header']['dr_no']))
										$this->data['header']['dr_no'] =  '';
									$DRarr = explode('-', $this->data['header']['dr_no']);
									$DRNO = $SINO =null;
									$isZERORated =  $this->data['header']['tax_type']=='ZRO';
									
									if(!isset($DRarr[1]))
										$DRarr = array('','');
									$SIstart = $SIend =(int)$SIarr[1];
									$DRstart = $DRend =(int)$DRarr[1];
									if(isset($this->data['header']['si_end'])){
										$this->data['header']['si_no']  =$SIarr[0].'-'.$SIstart;
										$SIarr = explode('-', $this->data['header']['si_end']);
										$SIend = (int)$SIarr[1];
										$hasMultipleSI = true;;
									}
									if(isset($this->data['header']['dr_end'])){
										$this->data['header']['dr_no'] =  $DRarr[0].'-'.$DRstart;
										$DRarr = explode('-', $this->data['header']['dr_end']);
										$DRend = (int)$DRarr[1];
										$hasMultipleDR = true;
									}
									$invoices = array();
									$transactions = array();
									$si_nos = array();

									$itemPerPage = 10;//$this->data['header']['item_per_page'];
									if($hasMultipleSI){
										//Multiple SI
										$itemPerPage = $this->data['header']['item_per_page'];
										$drNO =  $DRstart;
										for($siNO =  $SIstart;$siNO<=$SIend;$siNO+=1){
											$this->data['header']['id']=null;
											$siNOT = str_pad($siNO, 4, "0", STR_PAD_LEFT);
											$SINO  =$SIarr[0].'-'.$siNOT;
											$this->data['header']['si_no']=$SINO;
											array_push($si_nos,$SINO);
											if($hasMultipleDR){
												$drNOT = str_pad($drNO, 4, "0", STR_PAD_LEFT);
												$DRNO  =$DRarr[0].'-'.$drNOT;
												$this->data['header']['dr_no']=$DRNO;
												$drNO++;
											}


											$Invoice->save($this->data['header']);
											$invoice_id = $Invoice->id;
											array_push($invoices,$invoice_id);
											
											
											$this->data['header']['ref_no'] = $invoice_id;
											
											$Endpoint->save($this->data['header']);
											$transaction_id = $Endpoint->id;
											array_push($transactions,$transaction_id);
										}
									}else{
										//Single SI
										$Invoice->save($this->data['header']);	
										$SINO =  $this->data['header']['si_no'];
										$DRNO =  $this->data['header']['dr_no'];
										array_push($si_nos,$SINO);
										$invoice_id = $Invoice->id;
										array_push($invoices,$invoice_id);
										$this->data['header']['ref_no'] = $invoice_id;
										
										$Endpoint->save($this->data['header']);
										$transaction_id = $Endpoint->id;
										array_push($transactions,$transaction_id);
									}
																
									unset($this->data['header']['si_no']);
									unset($this->data['header']['dr_no']);
									$InvoiceDetail = &ClassRegistry::init('InvoiceDetail');
									$invDetails = $this->data['details'];
									$invDetails = array_chunk($invDetails, $itemPerPage);
									
									$payments = array();
									foreach($invDetails as $index=>$details):
										
										$invoice_id =  $invoices[$index];
										$transaction_id =  $transactions[$index];
										$payObj = $this->data['payments'];
										$payObj[0]['amount']  = 0;

										foreach($details as $detail){
											$detail['id']=null;
											$detail['soh_quantity'] = $detail['quantity'];
											$detail['invoice_id']=$invoice_id;
											$detail['transaction_id']=$transaction_id;
											$InvoiceDetail->save($detail);
											$InvoiceDetail->Product->updatePricing(array($detail),'tmp');
											$Endpoint->TransactionDetail->save($detail);
											$payObj[0]['amount'] +=$detail['amount'];
										}
										//Update Invoice amount
										$totalDue = $payObj[0]['amount'];
										$discPerc = $this->data['header']['discount_percent']/100;
										$discount = $totalDue * $discPerc;
										$taxableAmt =  $totalDue -  $discount;
										$tax = ( $taxableAmt  / 1.12 )*0.12;
										$net_of_vat = $taxableAmt  - $tax;
										$with_holding_tax = 0;// $net_of_vat *0.01;
										if(isset($this->data['header']['with_holding_tax'])):
											$with_holding_tax =  $this->data['header']['with_holding_tax'];
										endif;
										if($isZERORated):
											$tax =0;
											$net_of_vat = $taxableAmt;
											$with_holding_tax = 0;
										endif;
										$this->data['header']['id']=$invoice_id;
										$this->data['header']['total'] = $totalDue;
										$this->data['header']['discount'] = $discount;
										$this->data['header']['tax'] = $tax;
										$this->data['header']['net_of_vat'] = $net_of_vat;
										$this->data['header']['with_holding_tax'] = $with_holding_tax;
										$Invoice->save($this->data['header']);
										//Update Transaction amount
										$this->data['header']['id']=$transaction_id;
										$this->data['header']['amount'] = $totalDue;
										$this->data['header']['discount'] = $discount;
										$this->data['header']['ref_no'] = $invoice_id;
										$this->data['header']['tax'] = $tax;
										$Endpoint->save($this->data['header']);

										array_push($payments, $payObj);
									endforeach;


									$InvoicePayment = &ClassRegistry::init('InvoicePayment');
									foreach($payments  as $index=>$payObj):
										$invoice_id =  $invoices[$index];
										$transaction_id =  $transactions[$index];
										$ref_no =  $si_nos[$index];
										foreach( $payObj as $payment){
											$payment['id']=null;
											$payment['invoice_id']=$invoice_id;
											$payment['transaction_id']=$transaction_id;
											$InvoicePayment->save($payment);
											$Endpoint->TransactionPayment->save($payment);
											if($payment['payment_type']=='CHRG'){
												switch($this->data['header']['entity_type']){
													case 'customer':
														$CustomerLedger = &ClassRegistry::init('CustomerLedger');
														$entry = array('CustomerLedger'=>array(
																	'id'=>null,
																	'customer_id'=>$this->data['header']['entity']['id'],
																	'ref_no'=>$ref_no,
																	'particulars'=>$payment['detail'],
																	'amount'=>$payment['amount'],
																	'flag'=>'c',
																	'timestamp'=>$this->data['header']['timestamp'],
																));
														$CustomerLedger->save($entry);

														// TODO Compute to term_date add x days;
														$invObj=array('id'=>$invoice_id,
																'terms'=>$payment['detail']);
														$Invoice->save($invObj);
													break;
												}
											}else if($payment['payment_type']=='CASH'){
												$CashFlow =  &ClassRegistry::init('CashFlow');
												$entry = array('CashFlow'=>array(
																'id'=>null,
																'particulars'=>'sales-'.$invoice_id,
																'amount'=>$this->data['header']['amount'],
																'flag'=>'c',//Income
																'timestamp'=>$this->data['header']['timestamp']

															));
												$CashFlow->save($entry);
											}
										}
									endforeach;

									$JO_TRNX = $this->data['header']['jo_trnx_id'];
									$PO_ID = $this->data['header']['purchase_order_id'];
									$PO_TRNX = $Endpoint->findRelated('po',$PO_ID);
									$this->data['header']['si_no'] = $SINO;
									if($DRNO)
										$this->data['header']['dr_no'] = $DRNO;

									$this->data['ids'] = json_encode($transactions);
									$this->data['invoices'] = $invoices;
									$Endpoint->updateStatus($JO_TRNX,'invoiced');
									$Endpoint->updateStatus($PO_TRNX,'invoiced');
								break;
							}

							$this->data['id'] = $transaction_id;
							$this->data['token'] = md5($Endpoint->id);
						}else if($__Class=='Product'){
							if(isset($this->data['adjusted']['quantity'])){
								if($this->data['adjusted']['quantity']) $this->data['tmp_quantity'] = null;
							}
							if(isset($this->data['adjusted']['price'])){
								if($this->data['adjusted']['price']) $this->data['tmp_srp'] = null;
							}
						}else if($__Class=='User'){
							if(isset($this->data['password'])) $this->data['password']  = md5($this->data['password']);
							$Endpoint->save($this->data);
							$user_id = $this->data['id'] = $Endpoint->id;
							if(isset($this->data['access'])) $Endpoint->updateAccess($this->data['access'],$user_id);
							else $Endpoint->revokeAccess($user_id);
						}else if($__Class=='CustomerLedger'){
							$User = &ClassRegistry::init('User');
							$Transaction = &ClassRegistry::init('Transaction');
							$user = $User->me()['User'];
							$timestamp = $this->data['timestamp'];

							$ref_no = $this->data['ref_no'];
							$detail = $this->data['particulars'];
							$paymentAmount = $amount = $this->data['amount'];
							$check_no = $this->data['check_no'];
							$customerId = $this->data['customer_id'];
							$trnx = array(
								'user'=>'',
								'type'=>'payment',
								'status'=>'paid',
								'entity_type'=>'customer',
								'entity_id'=>$customerId,
								'ref_no'=>$ref_no,
								'amount'=>$amount,
								'timestamp'=>$timestamp,
							);
							$Transaction->save($trnx);
							$trnxId =  $Transaction->id;
							$Invoice =  &ClassRegistry::init('Invoice');
							// SI defined
							if($detail && $detail!='NA'):
								// Get related SI
								$refNos = explode(',', $detail);
							else: 
								// if NO SI
								$refNos = $Invoice->getUnpaid($customerId);
							endif;

							$Invoice->applyPayment($refNos,$ref_no,$check_no,$paymentAmount,$customerId);
							if(isset($check_no)){
								$detail =  array($detail);
								array_push($detail,$this->data['check_no']);
								array_push($detail,$this->data['check_bank']);
								array_push($detail,$this->data['check_date']);
								$detail  = implode(';', $detail);
							}

							$pay = array('transaction_id'=>$trnxId,
											'payment_type'=>'CHQE',
											'detail'=>$detail,
											'amount'=>$amount,
											);
							$Transaction->TransactionPayment->save($pay);
							
						}
						$Endpoint->save($this->data);
					break;
					case 'delete':
						if(isset($this->data['id'])){
							if($__Class =='CustomerLedger'):
								$ID = $this->data['id'];
								$CLD = $Endpoint->findById($ID);
								$Transaction = &ClassRegistry::init('Transaction');
								$Invoice = &ClassRegistry::init('Invoice');
								// Look for releated
								$ref_no = $CLD['CustomerLedger']['ref_no'];
								$amount = $CLD['CustomerLedger']['amount'];
								$flag   =  $CLD['CustomerLedger']['flag'];
								$relTxn = null;
								$invPay = null;
								switch($flag){
									case 'c':
										$invPay = array('payment_type'=>'CHRG','ref_no'=>$ref_no, 'amount'=>$amount);
										$relTxn = $Transaction->findRelated('invoice',$ref_no);
									break;
									case 'd':
										$invPay = array('payment_type !='=>'CHRG','ref_no'=>$ref_no, 'amount'=>$amount);
										$relTxn = $Transaction->findRelated('payment',$ref_no);
								 	break;
								}

								$updTxn = array('id'=>$relTxn,'status'=>'archived');
								if($relTxn)
									$Transaction->save($updTxn);
								if($flag=='d'){
									$Invoice->recursive=0;
									$si_no = $CLD['CustomerLedger']['particulars'];
									$invCond = array('si_no'=>$si_no,'total'=>$amount);
									
									$INV = $Invoice->find('first',array('conditions'=>$invCond));
									if($INV):
										$INV['Invoice']['total_paid'] = $INV['Invoice']['total_paid'] -$amount; 
									
										$Invoice->save($INV);
										$Invoice->InvoicePayment->deleteAll($invPay);
									
										$inv_id = $INV['Invoice']['id'];
										$invTxn = $Transaction->findRelated('invoice',$inv_id);
										$updTxn = array('id'=>$invTxn,'status'=>'unpaid');
										$Transaction->save($updTxn);
									endif;
								}

								if($flag=='c'){
									$si_no = $ref_no;
									$INV = $Invoice->findBySiNo($si_no);
									if($Invoice):
										$inv_id = $INV['Invoice']['id'];
										$invTxn = $Transaction->findRelated('invoice',$inv_id);
										$updTxn = array('id'=>$invTxn,'status'=>'archived');
										$Transaction->save($updTxn);
										$Invoice->delete($inv_id);
									endif;
								}

								$this->data['ref_no']=  $ref_no;
								$this->data['flag']=  $flag;
								$this->data['relTxn']=  $relTxn;
								
								$Endpoint->delete($this->data['id']);
							else:
								$Endpoint->delete($this->data['id']);
							endif;
						}
					break;
					case 'archive':
						if(isset($this->data['id'])){
							$object = array('id'=>$this->data['id'],'status'=>'archive');
							$Endpoint->save($object);
						}
					break;
					case 'activate':
						if(isset($this->data['id'])){
							$object = array('id'=>$this->data['id'],'status'=>'active');
							$Endpoint->save($object);
						}
					break;
					case 'unlink':
						if(isset($this->data['id'])){
							$object = array('id'=>$this->data['id'],
									'product_id'=>null,
									'type'=>null,
									'quantity'=>null,
									'area'=>null,
									'quantity_actual'=>null,
									'area_actual'=>null,
									'notes'=>null,
							);
							$Endpoint->save($object);
						}
					break;
					case 'uptax':

						if(isset($this->data['id'])){
							$object = array('id'=>$this->data['id'],
									'tax'=>$this->data['tax'],
							);
							$Endpoint->save($object);
							$Invoice = &ClassRegistry::init('Invoice');
							$object['id']=  $this->data['invoice_id'];
							$Invoice->save($object);

							$cond = array('type'=>'invoice','ref_no'=>$object['id']);
							$trnx = $Endpoint->find('first',array('conditions'=>$cond));
							$object['id'] =  $trnx['Transaction']['id'];
							$Endpoint->save($object);
						}

					break;
					case 'update':
						if($__Class=='Product'){
							
							if(isset($this->data['tmp_quantity'])):
								$adjustment = array('id'=>null,'product_id'=>$this->data['id'],'tmp_quantity'=>$this->data['tmp_quantity']);
								$InventoryAdjustment =  &ClassRegistry::init('InventoryAdjustment');
								$InventoryAdjustment->save($adjustment);
							elseif(isset($this->data['stocks'])):
								$ProductStock = &ClassRegistry::init('ProductStock');
								$stocks = $this->data['stocks'];
								foreach($stocks as $i=>$stock){
									$stocks[$i]['product_id']=$this->data['id'];
								}
								$ProductStock->saveAll($stocks);

								$adjustment = array('id'=>null,'product_id'=>$this->data['id'],'tmp_quantity'=>$this->data['total_area']);
								$InventoryAdjustment =  &ClassRegistry::init('InventoryAdjustment');
								$InventoryAdjustment->save($adjustment);

							endif;
							$Endpoint->save($this->data);
						}else if($__Class =='Invoice'){
							$CustomerLedger = &ClassRegistry::init('CustomerLedger');
							$cid = $this->data['customer_id'];
							$refNo = $this->data['old_ref_no'];
							$cond = array('customer_id'=>$cid, 'ref_no'=>$refNo);
							$CustomerLedger->recursive=-1;
							//Get Old SI
							$CLItem = $CustomerLedger->find('first',array('conditions'=>$cond));
							$CLItem['CustomerLedger']['ref_no'] =  $this->data['si_no'];
							$CustomerLedger->save($CLItem);
							$Endpoint->save($this->data);
						}else if($__Class=='Transaction'){
							// IF Has Token, Ready for Update
							// GET PO Obj then delete details, payments
							// GET TrnxDtl TrnxPay then delete details, payments
							if(isset($this->data['header']['token'])):
								$token = $this->data['header']['token'];
								$TRNX = $Endpoint->findById($token);
								$TXData = $POData =  $this->data['header'];
								$tr_id =  $TRNX['Transaction']['id'];
								$po_id =$POData['id'] = $TRNX['Transaction']['ref_no'];
								
								$TXData['id']=$tr_id;
								$Endpoint->save($TXData);

								$PurchaseOrder = &ClassRegistry::init('PurchaseOrder');
									if(isset($POData['customer']['name'])){
										$POData['customer'] = $POData['customer']['name'];
									}
									$PurchaseOrder->save($POData);									
									
									$PurchaseOrderDetail = &ClassRegistry::init('PurchaseOrderDetail');
									$PDDel = array(
												'purchase_order_id'=>$po_id
												);
									$TDDel = array(
												'transaction_id'=>$tr_id
									);
									$PurchaseOrderDetail->deleteAll($PDDel);
									$Endpoint->TransactionDetail->deleteAll($TDDel);
									foreach($this->data['details'] as $detail){
										$detail['id']=null;
										$detail['soh_quantity'] = $detail['quantity'];
										$detail['purchase_order_id']=$po_id;
										$detail['transaction_id']=$tr_id;
										$PurchaseOrderDetail->save($detail);
										$Endpoint->TransactionDetail->save($detail);
									}

									$PurchaseOrderTerm = &ClassRegistry::init('PurchaseOrderTerm');

									$PurchaseOrderTerm->deleteAll($PDDel);
									$Endpoint->TransactionPayment->deleteAll($TDDel);
									foreach($this->data['payments'] as $payment){
										$payment['id']=null;
										$payment['purchase_order_id']=$po_id;
										$payment['transaction_id']=$tr_id;
										$PurchaseOrderTerm->save($payment);
										$Endpoint->TransactionPayment->save($payment);
									}
								unset($this->data['header']['token']);
								$this->data['id'] = $tr_id;
								$this->data['header']['ref_no'] = $po_id;
								$this->data['token'] = md5($Endpoint->id);
							endif;
							$Endpoint->save($this->data);
						}
					break;
					case 'post':
						if($__Class=='Customer' || $__Class=='Supplier'){
							$last_bill = $this->data['last_bill'];
							$current_month = (int) date('m', strtotime("this month"));
							if($last_bill){
								if($current_month!=$last_bill){
									$last_bill = $current_month;
								}
							}
							else $last_bill = $current_month-1;
							$this->data['begin_balance'] =  $this->data['current_balance'];
							$this->data['current_balance'] =  0;
							$this->data['last_bill'] = $last_bill;
							$Endpoint->save($this->data);
						}
					break;
					case 'cancel':
						if($__Class=='Transaction'){
							
							$trnx = $Endpoint->findById($this->data['header']['id']);
							$this->data['header']['status']='cancelled';
							$this->data['header']['commission']=$trnx['Transaction']['commission'];
							$this->data['header']['tax']=$trnx['Transaction']['tax'];
							$this->data['header']['discount']=$trnx['Transaction']['discount'];
							$timestamp = date('Y-m-d H:i:s', time());
							switch($this->data['header']['type']){
								case 'sales':
									foreach($this->data['details'] as $detail){
										$detail['soh_quantity'] = $detail['quantity'];
										$Endpoint->TransactionDetail->Product->updateInventory(array($detail),1,'CSS');
									}
									foreach($this->data['payments'] as $payment){
										if($payment['payment_type']=='CHRG'){
											switch($this->data['header']['entity_type']){
												case 'customer':
													$CustomerLedger = &ClassRegistry::init('CustomerLedger');
													$entry = array('CustomerLedger'=>array(
																'id'=>null,
																'customer_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$this->data['header']['ref_no'],
																'particulars'=>'Cancel Transaction',
																'amount'=>$payment['amount'],
																'flag'=>'d',
																'timestamp'=>$timestamp
															));
													$CustomerLedger->save($entry);
												break;
											}
										}
									}
								break;
								case 'deliveries':
									foreach($this->data['details'] as $detail){
										$detail['soh_quantity'] = $detail['quantity'];
										$detail['capital'] = $detail['price'];
										$ref_no = $this->data['header']['ref_no'];
										$Endpoint->TransactionDetail->Product->updateInventory(array($detail),-1,'CDL');
										$Endpoint->TransactionDetail->Product->updatePricing(array($detail),'canceldelivery',$ref_no);
									}
									foreach($this->data['payments'] as $payment){
										if($payment['payment_type']=='CHRG'){
											switch($this->data['header']['entity_type']){
												case 'supplier':
													$SupplierLedger = &ClassRegistry::init('SupplierLedger');
													$entry = array('SupplierLedger'=>array(
																'id'=>null,
																'supplier_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$ref_no,
																'particulars'=>'Cancel Transaction',
																'amount'=>$payment['amount'],
																'flag'=>'d',
																'timestamp'=>$timestamp
															));
													$SupplierLedger->save($entry);
												break;
											}
										}
									}
								break;
								case 'tradein': case 'return':
									foreach($this->data['details'] as $detail){
										$detail['soh_quantity'] = $detail['quantity'];
										$type = $this->data['header']['type'];
										switch($this->data['header']['entity_type']){
											case 'customer':
												if($type=='tradein') $type = 'CTI'; //Trade In
												else if($type=='return') $type = 'CRC'; //Return From Customer
												$Endpoint->TransactionDetail->Product->updateInventory(array($detail),-1,$type);
											break;
											case 'supplier':
												if($type=='return') $type = 'CRS'; //Return To Supplier
												$Endpoint->TransactionDetail->Product->updateInventory(array($detail),1,$type);
											break;
										}
									}
									foreach($this->data['payments'] as $payment){
										if($payment['payment_type']=='CHRG'){
											switch($this->data['header']['entity_type']){
												case 'customer':
													$CustomerLedger = &ClassRegistry::init('CustomerLedger');
													$entry = array('CustomerLedger'=>array(
																'id'=>null,
																'customer_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$this->data['header']['ref_no'],
																'particulars'=>'Cancel Transaction',
																'amount'=>$payment['amount'],
																'flag'=>'c',
																'timestamp'=>$timestamp
															));
													$CustomerLedger->save($entry);
												break;
												case 'supplier':
													$SupplierLedger = &ClassRegistry::init('SupplierLedger');
													$entry = array('SupplierLedger'=>array(
																'id'=>null,
																'supplier_id'=>$this->data['header']['entity']['id'],
																'ref_no'=>$this->data['header']['type'].'-'.$this->data['header']['ref_no'],
																'particulars'=>'Cancel Transaction',
																'amount'=>$payment['amount'],
																'flag'=>'c',
																'timestamp'=>$timestamp
															));
													$SupplierLedger->save($entry);
												break;
											}
										}
									}
								break;
								case 'po':
									//Implent Cancel PO , CPO
									// Review transactions
									if($trnx['Transaction']['status']=='created')
										$this->data['header']['status']='archived';
									if($trnx['Transaction']['status']=='inprogress'):
										$this->data['header']['status']='cancelled';
										$poid = $this->data['header']['ref_no'];
										$JobOrder = &ClassRegistry::init('JobOrder');
										$JO = $JobOrder->findByPOID($poid);
										if($JO):
											$relTrnx =$Endpoint->findRelated('jo',$JO['id']);

											$Endpoint->updateStatus ($relTrnx, 'cancelled');
										endif;
									endif;
								break;
								case 'jo':
									//Implent Cancel JO , CJO
									//  Revert inventory
								break;
								case 'invoice':
									// TODO Implent Cancel SI , CSI
									//  Revert customer ledger and inventory
								break;

								case 'bill':
									// Handle billing creation
									$Billing = &ClassRegistry::init('Billing');
									$BillingDetail = &ClassRegistry::init('BillingDetail');
									$soa_no = $this->data['header']['soa_no'];
									$this->data['header']['id']=$soa_no;
									$this->data['header']['ref_no'] = $soa_no;
									$payload = 	array(
										'customer_id'=>$this->data['header']['customer_id'],
										'due_date'=>$this->data['header']['due_date'],
										'due_amount'=>$this->data['header']['due_amount']
									);
									$this->data['header']['hash'] = md5(json_encode($payload));
									if(!$Billing->save($this->data['header'])){
										print_r($Billing->validationErrors);exit;
									}

									// Save transaction
									if(!$Endpoint->save($this->data['header'])){
										print_r($Endpoint->validationErrors);exit;
									}
									$transaction_id = $Endpoint->id;

									// Process selected invoices
									$total_amount = 0;
									foreach ($this->data['invoices'] as $invoice_id) {
										// Get invoice details
										$Invoice = &ClassRegistry::init('Invoice');
										$invoice = $Invoice->findById($invoice_id);

										if ($invoice) {
											// Create billing detail record
											$detail = array(
												'id' => null,
												'billing_id' => $billing_id,
												'invoice_id' => $invoice_id,
												'amount' => $invoice['Invoice']['total'],
												'transaction_id' => $transaction_id
											);

											$BillingDetail->save($detail);

											// Update invoice status to billed
											$Invoice->save(array(
												'id' => $invoice_id,
												'status' => 'billed'
											));

											$total_amount += $invoice['Invoice']['total'];
										}
									}

									// Update billing and transaction with total amount
									$Billing->save(array(
										'id' => $billing_id,
										'total_amount' => $total_amount
									));

									$this->data['header']['amount'] = $total_amount;
									$Endpoint->save($this->data['header']);

									// Return the SOA number for PDF generation
									$data = array(
										'id' => $billing_id,
										'soa_no' => $soa_no,
										'total_amount' => $total_amount
									);

									
								break;
							}
							$Endpoint->save($this->data['header']);
						}
					break;
				}
				$data = $this->data;
			}
		}else if($this->RequestHandler->isPut()){
			if(!empty($_POST['data'])){
				$this->data =  $_POST['data'];
				$Endpoint->save($this->data);
				$data = array();
			}
		}else if($this->RequestHandler->isDelete()){
		
		}

		$response =  array();
		$response['status'] = '00';
		$response['message'] = '00';		
		$response['response'] =array('meta'=>$meta,'data'=>$data);
		$blacklist = array('Customer','Supplier','id','TransactionDetail','TransactionPayment','customer_id','supplier_id','entity_id','flag','created');
		switch($format){
			case 'csv':
			case 'pdf':
				$cols = array(array());
				$rows = array();
				if(count($data))
				foreach($data[0] as $key=>$value){
					if(($key=='Customer'||$key=='Supplier')&&!in_array('Transactee',$cols[0])){
						array_push($cols[0],'Transactee');
					}
					if($key=='TransactionDetail'){
						array_push($cols[0],'Product');
						array_push($cols[0],'Qty');
						array_push($cols[0],'Price');
					}
					if($key=='TransactionPayment'){
						array_push($cols[0],'Payment');
						array_push($cols[0],'Cash Amount');
						array_push($cols[0],'Charge Amount');
					}
					if($key=='flag' && ($__Class=='CustomerLedger'||$__Class=='SupplierLedger')){
						array_push($cols[0],'Debit');
						array_push($cols[0],'Credit');						
					}
					if($key=='created'){
						array_push($cols[0],'Timestamp');
					}
					if(!in_array($key,$blacklist)&&!($key=='amount'&& in_array('flag',$export)))
						array_push($cols[0],Inflector::humanize($key));
				}
				$totalAmount = 0;
				$totalCancelled = 0;
				foreach($data as $index=>$datum){
					$row = array();
					$buffer = null;
					$items = null;
					foreach($datum as $key=>$value){
						if($key=='Customer'||$key=='Supplier'){
							$allow=$__Class=='Transaction'?strtolower($key)==$datum['entity_type']:true;
							if(isset($value['name'])){
								if($value['name']&&$allow)
									array_push($row,$value['name']);
							}
						}else{
							if($key=='TransactionDetail'){
								if(!count($buffer)){
									$buffer=array();
									for($bi=0;$bi<count($row);$bi++){
										array_push($buffer,'  ');
									}
								}
								if(is_array($value)&&$format!='pdf'){
									$items = array();
									foreach($value as $k=>$v){
										if(isset($v['Product'])){
											if(isset($v['Product']['display_title'])){
												$item = array('product'=>$v['Product']['display_title'],'quantity'=>$v['quantity'],'amount'=>$v['amount']);
												array_push($items,$item);
											}
										}
									}
									if(isset($items[0])){
										array_push($row,$items[0]['product']);
										array_push($row,$items[0]['quantity']);
										array_push($row,$items[0]['amount']);
									}else{
										array_push($row,' ');
										array_push($row,' ');
										array_push($row,' ');
									}
								}
							}else if($key=='TransactionPayment'){
								if(is_array($value)){
									$payment = array();
									$cash = null;
									$charge = null;
									foreach($value as $k=>$v){
										if($v['payment_type']=='CASH'){
											array_push($payment,$v['payment_type'].'/'.$v['detail'].'/'.$v['amount']);
											$cash = $v['amount'];
										}else if($v['payment_type']=='CHRG'){
											array_push($payment,$v['payment_type'].'/'.$v['detail'].'/'.$v['amount']);
											$charge = $v['amount'];
											if($datum['type']=='return') $charge = -$charge;
											if($datum['status']=='cancelled') $charge = 0;
										}
										else array_push($payment,$v['payment_type'].'/'.$v['detail'].'/'.$v['amount']);
									}
									array_push($row,implode(',',$payment));
									if($cash) array_push($row,$cash);
									if($charge){
										$totalAmount += $charge;
										array_push($row,'');
										array_push($row,$charge);
									} 
								}
							}else if($key=='amount'&& in_array('flag',$export)){
								if($__Class=='CustomerLedger'||$__Class=='SupplierLedger'){
									switch($datum['flag']){
										case 'd':
											array_push($row,$datum['amount']);
											array_push($row,' ');
										break;
										case 'c':
											array_push($row,' ');
											array_push($row,$datum['amount']);
										break;
									}
								}
								
							}
							else if($key!='id'&&$key!='customer_id'&&$key!='supplier_id'&&$key!='entity_id'&&$key!='flag') array_push($row,$value);
							if($key=='amount'){
								 if(!$soa) $totalAmount +=$value;
								 if(isset($datum['status'])){
									if($datum['status']=='cancelled'){
										$totalCancelled +=$value;
									}
								}
							}
							
						}
						
					}
					array_push($rows,$row);
					if(count($buffer)&&$format!='pdf'){
						foreach($items as $ii=>$io){
							if($ii>0){
								$row = array_merge($buffer,	array($io['product'],$io['quantity'],$io['amount']));
								array_push($rows,$row);
							}
						}
					}
				}
				if($totalCancelled>0){
					$row = array();
					array_push($rows,$row);
					if($soa) $totalAmount +=$totalCancelled;
					$row = array(Inflector::humanize('gross_total'),$totalAmount);
						   array_push($rows,$row);
					$row = array(Inflector::humanize('total_cancelled'),$totalCancelled);
						   array_push($rows,$row);
					$netTotal =  $totalAmount - $totalCancelled;
					$row = array(Inflector::humanize('net_total'),$netTotal);
						   array_push($rows,$row);
				}else{
					$netTotal =  $totalAmount;
					$row = array(Inflector::humanize('total_amount'),$totalAmount);
					array_push($rows,$row);
				}
				$row = array();
				array_push($rows,$row);
				
				$filename = $__Class;
				
				
				if($__Class=='CustomerLedger' && $format=='pdf'){
					$CustomerObj = $data[0]['Customer'];
					
					$class = 'Customer';
					$Ledger = &ClassRegistry::init($class.'Ledger');
					$Entity = &ClassRegistry::init($class);
					$entity  = $Entity->findById($data[0]['customer_id']);
					$row = array(Inflector::humanize('begin_balance'),$entity[$class]['begin_balance']);
					array_push($rows,$row);
					$bill_detail = null;
				}
				if($__Class=='Transaction' && $soa){
					$class = Inflector::classify($data[0]['entity_type']);
					$Ledger = &ClassRegistry::init($class.'Ledger');
					$Entity = &ClassRegistry::init($class);
					$entity  = $Entity->findById($data[0]['entity_id']);
					$row = array(Inflector::humanize('begin_balance'),$entity[$class]['begin_balance']);
					array_push($rows,$row);
					$bill_month = 'this';
					$current_month = (int) date('m', strtotime("this month"));
					if($last_bill &&  $last_bill == $current_month) $bill_month = "this";
					foreach($bill_detail = $Ledger->getBalances($entity[$class]['begin_balance'],$entity[$class]['id'],$bill_month) as $key=>$value){						
						$row = array(Inflector::humanize($key),$value);
						array_push($rows,$row);
					}
					$filename = $entity[$class]['name'].'- SOA';
					$row = array(Inflector::humanize('Adjustments'),$totalAmount);
				}
				if(isset($filter['type']) && !$soa){
					$filename = Inflector::Humanize($filter['type']) . ' '. $filename;
				}				
				if(isset($filter['coverage'])){
					switch($filter['coverage']){
						case 'today':
							$filename .= ' - ' .$today;
						break;
						case '7D':
							$filename .= ' - ' .$lastWeek . ' - ' .$today;
						break;
						case '30D':
							$filename .= ' - ' .$lastMonth . ' - ' .$today;
						break;
						case 'SOA':
							$filename .= ' - ' .$firstDay . ' - ' .$lastDay;
						break;
					}
				}
				$filename = str_replace(',','',$filename);
				if($format=='csv'){
					//pr($rows);
					$this->header('Content-Type: text/csv; charset=utf-8');
					$this->header('Content-Disposition: attachment; filename='.$filename.'.csv');
					$output = fopen('php://output', 'w');

					foreach(array_merge($cols,$rows) as $d){
						
						if(isset($d['InvoiceDetail'])):
							$newD = array($d['po_no'],$d['si_no'],$d['invoice_date']);
							fputcsv($output, $newD);
							
						else:
							if(isset($d[8]))
								if(is_array($d[8])):
									$invoice = $d[8]['si_no'];
									$d[6] = $d[8]['po_no'];
									if(isset($d[8]['dr_no']))
										$invoice .= '/'.$d[8]['dr_no'];
									if(isset($d[8]['cr_no']))
										$invoice .= '/'.$d[8]['cr_no'];
									$d[8] =  $invoice;
								endif;
							
							fputcsv($output, $d);
						endif;
						
						
					}
					exit;
				}
				$run_total = 0;
				if(isset($entity))
				if($format=='pdf'){
					App::import('Vendor','soa_railim');
					$soa =  new SOARailim();
					$Invoice = &ClassRegistry::init('Invoice');
					$Transaction = &ClassRegistry::init('Transaction');
					if($__Class=='Transaction'):
						$User = &ClassRegistry::init('User');
						$user = $User->me()['User'];
						$due_month = "this";
						$soa_no =  isset($_GET['soa_no'])?$_GET['soa_no']:'';
						$info=array('account'=>$entity[$class]['name'],
									'detail'=>$bill_detail,
									'total'=>number_format($totalAmount,2,'.',','),
									'date'=>date('F d, Y',strtotime("today")),
									'due_date'=>date('F d, Y',strtotime("last day of $due_month  month")),
									'user'=>$user['full_name'],
									'soa_no'=>$soa_no,
									);
						$details=array();
						
						$run_total = 0;
						//gpr($rows);exit;
						foreach($rows as $line=>$row){
							if(count($row)>2){
								$detail = array();
								$detail['no']=$line+1;
								$detail['date']=date('M d, Y',strtotime($row[0]));
								$hasPayment = false;
								$insert = array();
								$dtl = $row[1];
								if($row[2]=='archived')
									continue;
								if(is_array($row[8])):
									if(isset($row[8]['total'])):
										
										if($row[8]['total']==$row[8]['total_paid']+$row[8]['discount']):
											continue; // Skip loop if invoice fully paid

										elseif(is_array($row[8]['InvoicePayment'])):
											// Run payments made to compute for total paid
											$payments = $row[8]['InvoicePayment'];
											$invObj = array('id'=>$row[8]['id'],'total_paid'=>0);
											foreach($payments as $pay):
												if($pay['payment_type']=='CASH' || $pay['payment_type']=='CHQE'):
													$invObj['total_paid']+=$pay['amount'];
													if($pay['payment_type']=='CHQE'):
														$hasPayment = true;
														if($invObj['total_paid']!=$row[8]['total']):
															$amtVal = $pay['amount']*-1;
															$amtTxt = '('.number_format($pay['amount'],2,'.',',').')';
															$payTxn = $Transaction->findRelated('payment',$pay['ref_no']);
															$txnObj = $Transaction->findById($payTxn);
															
															if($txnObj):
																$payTxnObj = $txnObj['TransactionPayment'][0];
																$payDtl  =explode(';',$payTxnObj['detail']);
																$checkNo =  $payDtl[1];
																$bank = "";
																if(isset($payDtl[2]))
																	$bank =  $payDtl[2];
																$checkDtl = sprintf('%s %s',$checkNo,$bank);
																$insert['no']=$line+1;
																$insert['date']='';
																$insert['po_no']=$checkDtl;
																$insert['details']=$pay['ref_no'];
																$insert['amount']=array(
																		'text'=>$amtTxt,
																		'value'=>$amtVal
																);
															endif;
														endif;
													endif;
												endif;
											endforeach;
											if($Invoice->save($invObj)){
												if($row[8]['total']==$invObj['total_paid'])
													continue;	 // Skip loop if invoice fully paid
											}
										endif;
									else:
										if($row[1]=='payment')
											continue;
									endif;
									$dtl  =$row[8]['si_no'];
									if(!isset($row[8]['dr_no']))
										$row[8]['dr_no'] = "";
									if($row[8]['dr_no'])
										$dtl .= '/'.$row[8]['dr_no'];
									
									if($row[2]=='fulfilled' ||$row[2]=='unpaid')
										$run_total +=(float)$row[4];
									$detail['po_no'] =  $row[8]['po_no'];
								endif;
								if($row[2]=='cancelled'){
									$dtl.= ' Cancelled';
								}
								$detail['details']=$dtl;
								
								if($row[2]=='paid'){
									$dtl =  $row[6];
									$amount = $row[4]*-1;
									$run_total  +=$amount;
									if($row[9]):
									$si_no =  explode('/',$row[9])[1];
									$detail['details']=$si_no;
									endif;
									$detail['po_no']=$dtl;
								}else{
									$amount = $row[2]=='cancelled'?$row[4]:(isset($row[11])?$row[11]:$row[10]);
								}
								$detail['amount']=array('text'=>number_format(abs($amount),2,'.',','),'value'=>$amount);
								if($row[1]=='return'){
									$detail['details'] = 'Return:'. $detail['details'];
								}
								if($amount<0){
									$detail['amount']['text']= '('.$detail['amount']['text'].')';
								}
								
								array_push($details,$detail);

								if($hasPayment){
									array_push($details,$insert);
									$run_total +=$amtVal;
								}
							}
						}
						
						$info['total'] =  $run_total;
						//pr($details);
						$soa->info($info);
						$soa->details($details,$info);
					elseif($__Class =='CustomerLedger'):

						$info=array('account'=>$entity[$class]['name'],
							'detail'=>$bill_detail,
							'total'=>number_format($totalAmount,2,'.',','),
							'date'=>date('F d, Y',strtotime("today")),
							'due_date'=>'',
							'user'=>'',//$user['full_name'],
							);
						

						$details=array();
						$run_total = 0;
						foreach($rows as $line=>$row){
							
								$detail = array();
								$detail['no']=$line+1;
								$detail['date'] ='';
								$dtl =  $pay =  $chg='';
								if(isset($row[0])){
									$detail['date']=date('M d, Y',strtotime($row[0]));	
									if(count($row)>2){
										$dtl =$row[1];
										$pay =$row[2];
										$chg =$row[3];	
										$run_total  =  $run_total  + $chg -  $pay;
									}else{
										break;
									}
								}
								

								
								$detail['details']=$dtl;
								$detail['payment']=$pay;
								$detail['charge']=$chg;
								
								array_push($details,$detail);
							
						}
						$info['total'] =  $run_total;
						$soa->info($info);
						$soa->billing($details,$info);
					endif;
				
					$soa->footnote($info);
					$soa->output();
				}
			break;
			case 'json':
			default:
				$this->header('Content-Type: application/json');
				echo json_encode($response,JSON_NUMERIC_CHECK  | JSON_PRETTY_PRINT);exit;
			break;
		}		
	}
}
