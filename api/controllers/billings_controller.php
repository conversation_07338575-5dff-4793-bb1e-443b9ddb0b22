<?php
class BillingsController extends AppController {

	var $name = 'Billings';

	var $uses = array('Billing', 'BillingDetail', 'Customer', 'Transaction');

	/**
	 * List all billings with pagination
	 */
	function index() {
		$this->Billing->recursive = 1;
		$conditions = array();

		// Handle search by customer name
		if (isset($this->params['url']['keyword']) && !empty($this->params['url']['keyword'])) {
			$keyword = $this->params['url']['keyword'];
			$conditions['OR'] = array(
				'Billing.id LIKE' => "%{$keyword}%",
				'Customer.name LIKE' => "%{$keyword}%"
			);
		}

		// Handle status filter
		if (isset($this->params['url']['status']) && !empty($this->params['url']['status'])) {
			$conditions['Billing.status'] = $this->params['url']['status'];
		} else {
			$conditions['Billing.status'] = 'created';
		}

		if(isset($_GET['customer_id'])){
			$conditions['Billing.customer_id'] = $_GET['customer_id'];
		}

		$this->paginate = array(
			'conditions' => $conditions,
			'limit' => isset($this->params['url']['limit']) ? $this->params['url']['limit'] : 10,
			'order' => array('Billing.created' => 'DESC')
		);
		$billings = $this->paginate('Billing');
		$meta = array(
			'page' => $this->params['paging']['Billing']['page'],
			'pages' => $this->params['paging']['Billing']['pageCount'],
			'limit' => $this->paginate['limit'],
			'count' => $this->params['paging']['Billing']['count']
		);

		$this->layout = 'json';
		$this->set(compact('billings', 'meta'));
	}

	function generate_id(){
		$BID = $this->Billing->generateBillingId();
		$billings = array('Billing'=>array('id'=>$BID));

		$meta = array(
			'page' => 1,
			'pages' =>1,
			'limit' => 1,
			'count' => 1
		);

		$this->layout = 'json';
		$this->set(compact('billings', 'meta'));
	}
	/**
	 * View a specific billing
	 */
	function view($id = null) {
		if (!$id) {
			$this->_sendError('Invalid billing ID');
			return;
		}

		// Get the billing with basic details
		$this->Billing->recursive = 1;
		$billing = $this->Billing->findById($id);

		if (!$billing) {
			$this->_sendError('Billing not found');
			return;
		}

		// Get enhanced billing details with invoice information
		$billingDetails = $this->BillingDetail->getInvoiceDetailsForBilling($id);
		$billing['BillingDetail'] = $billingDetails;

		$this->layout = 'json';
		$this->set('billing', $billing);
	}

	/**
	 * Add a new billing
	 */
	function add() {
		if (!empty($this->data)) {
			$this->Billing->create();

			// Start a transaction
			$dataSource = $this->Billing->getDataSource();
			$dataSource->begin();

			// Save the billing header
			if ($this->Billing->save($this->data)) {
				$billingId = $this->Billing->id;
				$success = true;

				// Save billing details if provided
				if (isset($this->data['BillingDetail']) && !empty($this->data['BillingDetail'])) {
					foreach ($this->data['BillingDetail'] as $detail) {
						$this->BillingDetail->create();
						$detail['billing_id'] = $billingId;
						if (!$this->BillingDetail->save(array('BillingDetail' => $detail))) {
							$success = false;
							break;
						}
					}
				}

				if ($success) {
					$dataSource->commit();
					$billing = $this->Billing->findById($billingId);
					$this->layout = 'json';
					$this->set('billing', $billing);
					return;
				} else {
					$dataSource->rollback();
					$this->_sendError('Failed to save billing details');
					return;
				}
			} else {
				$dataSource->rollback();
				$this->_sendError('Failed to save billing');
				return;
			}
		} else {
			$this->_sendError('No data provided');
			return;
		}
	}

	/**
	 * Update an existing billing
	 */
	function edit($id = null) {
		if (!$id) {
			$this->_sendError('Invalid billing ID');
			return;
		}

		$this->Billing->id = $id;
		if (!$this->Billing->exists()) {
			$this->_sendError('Billing not found');
			return;
		}

		if (!empty($this->data)) {
			// Start a transaction
			$dataSource = $this->Billing->getDataSource();
			$dataSource->begin();

			// Save the billing header
			if ($this->Billing->save($this->data)) {
				$success = true;

				// Handle billing details if provided
				if (isset($this->data['BillingDetail']) && !empty($this->data['BillingDetail'])) {
					// First, delete existing details
					$this->BillingDetail->deleteAll(array('BillingDetail.billing_id' => $id));

					// Then save new details
					foreach ($this->data['BillingDetail'] as $detail) {
						$this->BillingDetail->create();
						$detail['billing_id'] = $id;
						if (!$this->BillingDetail->save(array('BillingDetail' => $detail))) {
							$success = false;
							break;
						}
					}
				}

				if ($success) {
					$dataSource->commit();
					$billing = $this->Billing->findById($id);
					$this->layout = 'json';
					$this->set('billing', $billing);
					return;
				} else {
					$dataSource->rollback();
					$this->_sendError('Failed to save billing details');
					return;
				}
			} else {
				$dataSource->rollback();
				$this->_sendError('Failed to save billing');
				return;
			}
		} else {
			$this->_sendError('No data provided');
			return;
		}
	}

	/**
	 * Delete a billing
	 */
	function delete($id = null) {
		if (!$id) {
			$this->_sendError('Invalid billing ID');
			return;
		}

		$this->Billing->id = $id;
		if (!$this->Billing->exists()) {
			$this->_sendError('Billing not found');
			return;
		}

		// Soft delete by updating status
		if ($this->Billing->saveField('status', 'deleted')) {
			$this->layout = 'json';
			$this->set('success', true);
			$this->set('message', 'Billing deleted successfully');
		} else {
			$this->_sendError('Failed to delete billing');
		}
	}

	/**
	 * Generate Statement of Account (SOA) PDF
	 */
	function soa() {
		// Get SOA number from POST data
		$soa_no = isset($_POST['soa_no']) ? $_POST['soa_no'] : null;

		if (!$soa_no) {
			$this->_sendError('Invalid SOA number');
			return;
		}

		// Get the billing data for the specified SOA number
		$billing = $this->Billing->find('first', array(
			'conditions' => array('Billing.id' => $soa_no),
			'contain' => array(
				'Customer',
				'BillingDetail'
			)
		));

		
		// Get customer ID
		$customer_id = $billing['Billing']['customer_id'];

		// Get all related billings for the same customer
		$allBillings = $this->Billing->find('list', array(
			'conditions' => array(
				'Billing.customer_id' => $customer_id,
				'Billing.status !=' => 'deleted'
			),
			'fields' => array('Billing.id','Billing.status'),
			'recursive' => -1
		));
		$billDetails = $this->BillingDetail->getInvoiceDetailsForBilling(array_keys($allBillings));
	
		// Prepare ledger entries
		$ledgerEntries = array();
		$balance = 0;
		

		if (!empty($billDetails)) {
				foreach ($billDetails as $detail) {
					$amount = floatval($detail['BillingDetail']['invoice_amount']);
					$balance += $amount;
					$ref_no = $detail['Invoice']['po_no'];
					$ledgerEntries[] = array(
						'date' => $detail['BillingDetail']['invoice_date'],
						'ref_no' => $detail['BillingDetail']['ref_no'],
						'remarks' => $ref_no,
						'charge' => $amount,
						'payment' => '-',
						'balance' => $balance
					);
				}
			}






		// Add ledger entries to the billing data
		$billing['LedgerEntries'] = $ledgerEntries;

		// Set the data for the view
		$this->set('billing_data', $billing);

		// Set the layout to PDF
		$this->layout = 'pdf';
		$this->render('soa');
	}

	function details(){
		$this->params['url']['limit']=9999;
		$this->index();
		$billings=$this->viewVars['billings'];
		$meta=$this->viewVars['meta'];
		$this->layout = 'json';
		$this->set(compact('billings', 'meta'));

	}

	/**
	 * Helper method to send error responses
	 */
	function _sendError($message) {
		$this->layout = 'json';
		$this->set('success', false);
		$this->set('message', $message);
	}
}