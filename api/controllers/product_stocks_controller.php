<?php
class ProductStocksController extends AppController {

	var $name = 'ProductStocks';

	function index() {
		$this->ProductStock->recursive = 0;
		$this->set('productStocks', $this->paginate());
	}

	function view($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid product stock', true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('productStock', $this->ProductStock->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->ProductStock->create();
			if ($this->ProductStock->save($this->data)) {
				$this->Session->setFlash(__('The product stock has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The product stock could not be saved. Please, try again.', true));
			}
		}
		$products = $this->ProductStock->Product->find('list');
		$this->set(compact('products'));
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->Session->setFlash(__('Invalid product stock', true));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			if ($this->ProductStock->save($this->data)) {
				$this->Session->setFlash(__('The product stock has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The product stock could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->ProductStock->read(null, $id);
		}
		$products = $this->ProductStock->Product->find('list');
		$this->set(compact('products'));
	}

	function delete($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid id for product stock', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->ProductStock->delete($id)) {
			$this->Session->setFlash(__('Product stock deleted', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->Session->setFlash(__('Product stock was not deleted', true));
		$this->redirect(array('action' => 'index'));
	}
}
