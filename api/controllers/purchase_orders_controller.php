<?php
class PurchaseOrdersController extends AppController {

	var $name = 'PurchaseOrders';

	function index() {
		$this->PurchaseOrder->recursive = 0;
		$this->set('purchaseOrders', $this->paginate());
	}

	function view($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid purchase order', true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('purchaseOrder', $this->PurchaseOrder->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->PurchaseOrder->create();
			if ($this->PurchaseOrder->save($this->data)) {
				$this->Session->setFlash(__('The purchase order has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The purchase order could not be saved. Please, try again.', true));
			}
		}
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->Session->setFlash(__('Invalid purchase order', true));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			if ($this->PurchaseOrder->save($this->data)) {
				$this->Session->setFlash(__('The purchase order has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The purchase order could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->PurchaseOrder->read(null, $id);
		}
	}

	function delete($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid id for purchase order', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->PurchaseOrder->delete($id)) {
			$this->Session->setFlash(__('Purchase order deleted', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->Session->setFlash(__('Purchase order was not deleted', true));
		$this->redirect(array('action' => 'index'));
	}
}
