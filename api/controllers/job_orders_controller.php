<?php
class JobOrdersController extends AppController {

	var $name = 'JobOrders';

	function index() {
		$this->JobOrder->recursive = 0;
		$this->set('jobOrders', $this->paginate());
	}

	function view($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid job order', true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('jobOrder', $this->JobOrder->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->JobOrder->create();
			if ($this->JobOrder->save($this->data)) {
				$this->Session->setFlash(__('The job order has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The job order could not be saved. Please, try again.', true));
			}
		}
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->Session->setFlash(__('Invalid job order', true));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			if ($this->JobOrder->save($this->data)) {
				$this->Session->setFlash(__('The job order has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The job order could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->JobOrder->read(null, $id);
		}
	}

	function delete($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid id for job order', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->JobOrder->delete($id)) {
			$this->Session->setFlash(__('Job order deleted', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->Session->setFlash(__('Job order was not deleted', true));
		$this->redirect(array('action' => 'index'));
	}
}
