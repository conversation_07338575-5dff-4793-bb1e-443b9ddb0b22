<?php
class PurchaseOrderDetailsController extends AppController {

	var $name = 'PurchaseOrderDetails';

	function index() {
		$this->PurchaseOrderDetail->recursive = 0;
		$this->set('purchaseOrderDetails', $this->paginate());
	}

	function view($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid purchase order detail', true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('purchaseOrderDetail', $this->PurchaseOrderDetail->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->PurchaseOrderDetail->create();
			if ($this->PurchaseOrderDetail->save($this->data)) {
				$this->Session->setFlash(__('The purchase order detail has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The purchase order detail could not be saved. Please, try again.', true));
			}
		}
		$purchaseOrders = $this->PurchaseOrderDetail->PurchaseOrder->find('list');
		$products = $this->PurchaseOrderDetail->Product->find('list');
		$this->set(compact('purchaseOrders', 'products'));
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->Session->setFlash(__('Invalid purchase order detail', true));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			if ($this->PurchaseOrderDetail->save($this->data)) {
				$this->Session->setFlash(__('The purchase order detail has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The purchase order detail could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->PurchaseOrderDetail->read(null, $id);
		}
		$purchaseOrders = $this->PurchaseOrderDetail->PurchaseOrder->find('list');
		$products = $this->PurchaseOrderDetail->Product->find('list');
		$this->set(compact('purchaseOrders', 'products'));
	}

	function delete($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid id for purchase order detail', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->PurchaseOrderDetail->delete($id)) {
			$this->Session->setFlash(__('Purchase order detail deleted', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->Session->setFlash(__('Purchase order detail was not deleted', true));
		$this->redirect(array('action' => 'index'));
	}
}
