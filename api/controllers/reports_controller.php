<?php
class ReportsController extends AppController {
	var $name = 'Reports';
	var $uses = array('Transaction','Invoice','PurchaseOrder','ProductStock','Order');

	function index(){
		
	}
	function convertUnit($matches){
			
			$nominal = (float) str_replace('MM', 'M', $matches[0]);

			if($nominal>=1000){
				$nominal = $nominal/1000;
				$nominalMeter =  $nominal.'M';
				return $nominalMeter;
			}else{
				return $matches[0];
			}
		
			
		}
	protected function buildInvoice($trnx){
		$ref_no =  $trnx['ref_no'];
		$cust_id =  $trnx['entity_id'];
		
		$this->Invoice->recursive =2;
		
		$IObj = $this->Invoice->findById($ref_no);
		$CObj = $this->Transaction->Customer->findById($cust_id);
		

		$UnitSys = $CObj['Customer']['unit_system'];
		// Build date
		$transac_date =  date("d M Y", strtotime($IObj['Invoice']['si_date']));
		//Build terms
		$terms = "CASH";
		foreach($IObj['InvoicePayment'] as $payment){
			if($payment['payment_type']=='CHRG'){
				$terms = $payment['detail'].' Days';  
			}
		}
		
		// Build tax computation
		$discount =  $trnx['discount'];
		$discount_perc = (float) $trnx['discount_percent'];
		$vatable_sales = $trnx['amount']- $discount;
		$tax = $trnx['tax'];
	
		$total_sales=$vatable_sales;
		$less_vat=$tax;
		$net_of_vat=$trnx['net_of_vat'];
		$less_sc_pwd_disc=0;
		$amount_due=$net_of_vat-$less_sc_pwd_disc;
		$add_vat=$tax;
		$with_holding_tax = $trnx['with_holding_tax'];
		$total_amount_due=$amount_due+$add_vat-$with_holding_tax;
		$vat_type='vat';
		if($CObj['Customer']['tax_type']=='ZRO'){
			$vat_type='zero-rated';
			$vatable_sales = 0;
		}
		
		$cashier =  $trnx['user'];

		// Build items
		$items = array();

		foreach($IObj['InvoiceDetail'] as $dtl){
			$item =array(
			  "qty"=> $dtl['quantity'],
			  "unit"=> "PC",
			  "description"=>  $dtl['description'],
			  "u_price"=>  $dtl['price'],
			  "amount"=>  $dtl['amount']
			);
			if($item['qty']>1)
				$item["unit"]="PCS";
			array_push($items, $item);
		}
		
		
		
		$pattern = '/\d+MM/';
		foreach($items as $i=> $dtl){
			$desc = strtoupper($dtl['description']);
			$desc=  preg_replace_callback($pattern, array(&$this,"convertUnit"), $desc);
			
			$desc = utf8_decode($desc);
			$items[$i]['description'] =  $desc;
		}


		
		if($discount_perc>0){
			$item =array(
			  "qty"=> "",
			  "unit"=> "",
			  "description"=>  " DISCOUNT $discount_perc%",
			  "u_price"=>  "",
			  "amount"=>  $discount
			);
			array_push($items, $item);
		}

		// Build data
		$data = array(
		  "po_no"=> $IObj['Invoice']['po_no'],
		  "csi_no"=> $IObj['Invoice']['si_no'],
		  "dr_no"=> $IObj['Invoice']['dr_no'],
		  "charged_to"=> utf8_decode( $IObj['Invoice']['customer']),
		  "tin"=> $CObj['Customer']['tin'],
		  "address"=> utf8_decode($CObj['Customer']['address']),
		  "business_style"=> $CObj['Customer']['business_style'],
		  "osca_pwd_id_no"=> "N/A",
		  "transac_date"=> $transac_date,
		  "terms"=> $terms,
		  "items"=> $items,
		  "vatable_sales"=> $vatable_sales,
		  "total_sales"=> $total_sales,
		  "less_vat"=> $less_vat,
		  "net_of_vat"=> $net_of_vat,
		  "less_sc_pwd_disc"=> $less_sc_pwd_disc,
		  "amount_due"=> $amount_due,
		  "add_vat"=> $add_vat,
		  "with_holding_tax"=> $with_holding_tax,
		  "total_amount_due"=> $total_amount_due,
		  "vat_type"=> $vat_type,
		  "cshr_auth_rep"=> $cashier
		);
		return $data;

	}

	protected function buildPO($trnx){
		$ref_no =  $trnx['ref_no'];
		$cust_id =  $trnx['entity_id'];
		
		$this->PurchaseOrder->recursive =2;
		
		$IObj = $this->PurchaseOrder->findById($ref_no);
		$CObj = $this->Transaction->Customer->findById($cust_id);
		
		// Build date
		$transac_date =  date("d M Y", strtotime($IObj['PurchaseOrder']['po_date']));
		//Build terms
		$terms = "CASH";
		foreach($IObj['PurchaseOrderTerm'] as $payment){
			if($payment['payment_type']=='CHRG'){
				$terms = $payment['detail'].' Days';  
			}
		}
		
		// Build tax computation
		$vatable_sales = $trnx['amount'];
		$tax = $trnx['tax'];
		$total_sales=$vatable_sales;
		$less_vat=$tax;
		$net_of_vat=$total_sales-$tax;
		$less_sc_pwd_disc=0;
		$amount_due=$net_of_vat-$less_sc_pwd_disc;
		$add_vat=$tax;
		$total_amount_due=$amount_due+$add_vat;
		$vat_type='vat';
		if($CObj['Customer']['tax_type']=='ZRO'){
			$vat_type='zero-rated';
			$net_of_vat = 0;
			$vatable_sales = 0;
		}
		
		$cashier =  $trnx['user'];

		// Build items
		$items = array();
		function convertUnit($matches){
			

			$nominal = (float) str_replace('MM', 'M', $matches[0]);

			if($nominal>=1000){
				$nominal = $nominal/1000;
				$nominalMeter =  $nominal.'M';
				return $nominalMeter;
			}else{
				return $matches[0];
			}
		
			
		}
		$pattern = '/\d+MM/';

		foreach($IObj['PurchaseOrderDetail'] as $dtl){
			$desc= $dtl['Product']['description'].' ';
			$desc.= (float)($dtl['Product']['thickness']).'mm x';
			if($dtl['Product']['category_id']=='PLT8')
			$desc.= $dtl['width'].'mm x';
			$desc.= $dtl['length'].'mm ';

			$desc = strtoupper($desc);
			$desc=  preg_replace_callback($pattern, array(&$this,"convertUnit"), $desc);
			
			$item =array(
			  "qty"=> $dtl['quantity'],
			  "unit"=> "PC",
			  "description"=>  utf8_decode($desc),
			  "u_price"=>  $dtl['price'],
			  "amount"=>  $dtl['amount']
			);
			if($item['qty']>1)
				$item['unit']="PCS";
			array_push($items, $item);
		}
		// Build data
		$data = array(
		  "po_no"=> $IObj['PurchaseOrder']['po_no'],
		  "csi_no"=> '',
		  "dr_no"=> '',
		  "charged_to"=>  utf8_decode($IObj['PurchaseOrder']['customer']),
		  "tin"=> $CObj['Customer']['tin'],
		  "address"=> utf8_decode($CObj['Customer']['address']),
		  "business_style"=> $CObj['Customer']['business_style'],
		  "osca_pwd_id_no"=> "N/A",
		  "transac_date"=> $transac_date,
		  "terms"=> $terms,
		  "items"=> $items,
		  "vatable_sales"=> $vatable_sales,
		  "total_sales"=> $total_sales,
		  "less_vat"=> $less_vat,
		  "net_of_vat"=> $net_of_vat,
		  "less_sc_pwd_disc"=> $less_sc_pwd_disc,
		  "amount_due"=> $amount_due,
		  "add_vat"=> $add_vat,
		  "total_amount_due"=> $total_amount_due,
		  "vat_type"=> $vat_type,
		  "cshr_auth_rep"=> $cashier
		);
		return $data;

	}


	protected function buildCollect($trnx){
		$ref_no =  $trnx['ref_no'];
		$cust_id =  $trnx['entity_id'];
		$this->Invoice->recursive =2;
		
		$IObj = $this->Invoice->findById($ref_no);
		$CObj = $this->Transaction->Customer->findById($cust_id);
		
		// Build date
		$transac_date =  date("d M Y", strtotime($IObj['Invoice']['cr_date']));
		//Build terms
		$payment_form = "CASH";
		$payment_detail = "";
		$amount_paid = 0;
		foreach($IObj['InvoicePayment'] as $payment){
			if($payment['payment_type']=='CHCK'){
				$payment_form = "check";
				$payment_detail = $payment['detail'];  
			}
			$amount_paid = $payment['amount'];  
		}
		
		// Build data
		$cashier =  $trnx['user'];
		$data = array(
			"po_no"=> $IObj['Invoice']['po_no'],
			"cr_no"=> $IObj['Invoice']['cr_no'],
			"received_from"=>  $IObj['Invoice']['customer'],
			"tin"=> $CObj['Customer']['tin'],
			"address"=> $CObj['Customer']['address'],
			"business_style"=> $CObj['Customer']['business_style'],
			"osca_pwd_id_no"=> "N/A",
			"amount_paid"=> $amount_paid,
			"amount_words"=> '...',
			"transac_date"=> $transac_date,
			"payment_for"=>"Supplies",
			"settlements"=>array(
				array("invoice_no"=>$IObj['Invoice']['si_no'],"amount"=>$amount_paid),
			),
			"total_amount"=>$amount_paid,
			"payment_form"=>$payment_form,
			"payment_detail"=>$payment_detail,
			"auth_rep"=>$cashier
		);
		return $data;

	}

	function buildDelivery($trnx){
		
		$ref_no =  $trnx['ref_no'];
		$cust_id =  $trnx['entity_id'];
		$this->Invoice->recursive =2;
		
		$IObj = $this->Invoice->findById($ref_no);
		$CObj = $this->Transaction->Customer->findById($cust_id);
		
		// Build date
		$transac_date =  date("d M Y", strtotime($IObj['Invoice']['si_date']));
		//Build terms
		$terms = "CASH";
		foreach($IObj['InvoicePayment'] as $payment){
			if($payment['payment_type']=='CHRG'){
				$terms = $payment['detail'].' Days';  
			}
		}
		
		$cashier =  $trnx['user'];

		// Build items
		$items = array();
		foreach($IObj['InvoiceDetail'] as $dtl){
			$item =array(
			  "qty"=> $dtl['quantity'],
			  "unit"=> "PC",
			  "article"=>  utf8_decode($dtl['description']),

			);

			if($item['qty']>1)
				$item['unit']="PCS";

			array_push($items, $item);
		}
		// Build data
		
		$data = array(
		  "po_no"=> $IObj['Invoice']['po_no'],
		  "csi_no"=> $IObj['Invoice']['si_no'],
		  "dr_no"=> $IObj['Invoice']['dr_no'],
		  "charged_to"=>  utf8_decode($IObj['Invoice']['customer']),
		  "tin"=> $CObj['Customer']['tin'],
		  "address"=>utf8_decode( $CObj['Customer']['address']),
		  "business_style"=> $CObj['Customer']['business_style'],
		  "osca_pwd_id_no"=> "N/A",
		  "transac_date"=> $transac_date,
		  "terms"=> $terms,
		  "items"=> $items,
		  "cshr_auth_rep"=> $cashier
		);
		return $data;
	}

	function buildPOMonitoring($filter,$keyword,$fields){
		$__Class = 'Transaction';
		
		$totalFilter['coverage']='MON';
		$filterObj = $this->buildFilter($filter,$keyword,$fields);
		
		$invPOs  = null;
		if(is_array($fields))
			if(in_array('si_no', $fields))
				$invPOs =$filterObj['filter'][0]['OR']['PurchaseOrder.id'];


		if(is_array($fields)):
			if(in_array('po_no',$fields))
				unset($fields[1]);
			if(in_array('si_no',$fields))
				unset($fields[2]);
		endif;

		$totalFilter =  $filter;
		$totalFilter['coverage']='MON';

		$filterObj2 = $this->buildFilter($totalFilter,$keyword,$fields);
		//pr($filterObj2);exit;
		$__filter =  $filterObj['filter'];
		$firstDay =  $filterObj['firstDay'];
		$lastDay =  $filterObj['lastDay'];
		
		
		$order =array($__Class.'.timestamp ASC');
		


		$hasPOFilter=array('OR'=>array(
								array('`PurchaseOrder`.`po_no` LIKE '=>'%'.$keyword.'%'),
								array('`PurchaseOrder`.`customer` LIKE '=>'%'.$keyword.'%'),
								array('PurchaseOrder.id'=>$invPOs)
							));
		
		$belongsTo = array(
			'PurchaseOrder' => array(
			'className' => 'PurchaseOrder',
			'foreignKey' => 'ref_no',
			'conditions' => $hasPOFilter,
			'order' => '')
		);
		$this->Transaction->recursive=2;
		$conditions =  $filterObj2['filter'];

		
		$totalsTrnx =$this->Transaction->find('all',compact('conditions','order'));
		
		$conditions =  $__filter;
		
		
		$this->Transaction->bindModel(array('belongsTo'=>$belongsTo));
		
		$transactions = $this->Transaction->find('all',compact('conditions','order'));
		
		
		$import = 0;
		$local = 0;
		$raitech = 0;
		$grand_total = 0;
		$pending = 0;
		$served= 0;

		foreach($totalsTrnx as $t):
			$customer =  $t['Customer']['alias'];
			$status = $t['Transaction']['status'];
			$discAmt = $t['Transaction']['discount'];
			$discPer = $t['Transaction']['discount_percent'];
			
			foreach($t['TransactionDetail'] as $dtl):
				$amount = $dtl['amount'];
				$type = $dtl['Product']['type'];
				if($discPer >0){
					$amount = $amount * (100 - $discPer )/ 100;
				}
				
					//$raitech+=$amount;
					switch($type){
						case 'IMP':
							$import+=$amount;
							$grand_total+=$amount;
						break;
						case 'LOC':
							$local+=$amount;
							$grand_total+=$amount;
						break;
					}
					if($type=='IMP'||$type=='LOC'):
						if($status!='served'):
							$pending+=$amount;
						else:
							$served+=$amount;
						endif;


					endif;
				

			endforeach;

			
		endforeach;
		$coverage =  array(
			'from'=>$firstDay,
			'to'=>$lastDay
		);
		$details  =array();
		
		$totals = array(
			"import"=> $import,
			"local"=> $local,
			"raitech"=> $raitech,
			"grand_total"=> $grand_total,
			"pending"=> $pending,
			"served"=>$served,
			"po_total"=> 0,
		);

		//pr($totals);exit;
		$trnxLen = count($transactions);
		$lastDate = $transactions[$trnxLen-1]['Transaction']['timestamp'];
		$lastDate = (int)str_replace('-', '', substr($lastDate, 2,-9));
		foreach($transactions as $trnx){
			$T =  $trnx['Transaction'];
			$TD =  $trnx['TransactionDetail'];
			$C =  $trnx['Customer'];
			$P =  $trnx['PurchaseOrder'];
			$PD =  null;
			if(isset( $P['PurchaseOrderDetail']))
			$PD =  $P['PurchaseOrderDetail'];

			if(isset( $P['Invoice']))
			$I =  $P['Invoice'];
			$date =  date('d.m.y',strtotime($T['timestamp']));
			$customer =  $C['alias'];
			$po =  $P['po_no'];
			$grand_total =  $T['amount'];
			$timestamp = (int)str_replace('-', '', substr($T['timestamp'], 2,-9));
			$status = '';
			switch( $T['status']){
				case 'served':
					$status = 'served';
				break;
				case 'invoiced':
					$status = 'for-ship';
				break;
				case 'inprogress':
					$status = 'for-approval';
				break;
				case 'cancelled':
					$status = 'cancelled';
				break;
				case 'started': case 'created':
					$status = 'pending';
				break;
			}

			$invoice =  isset($I['id'])?$I['si_no']:'-';
			$materials = array();
			$local = 0;
			$import = 0;

			foreach($TD as $k=>$td){
				$title =  $td['Product']['description'];
				$thick =  (float)$td['Product']['thickness'];
				$cat =  $td['Product']['category_id'];
				$unit =  $td['Product']['unit'];
				$type =  $td['Product']['type'];
				if(!isset($PD[$k])):
					continue;
				endif;
				$len =  $PD[$k]['length'];
				$wid =  $PD[$k]['width'];
				
				if($cat=='PLT8')
					 $mat = sprintf('%s × %s%s  × %s%s  × %s%s',$title,$thick,$unit,$wid,$unit, $len,$unit);
				else
					$mat =  sprintf('%s %s%s × %s%s',$title,$thick,$unit, $len,$unit);	
				$mat =  utf8_decode($mat);

				$qty =  $td['quantity'];
				$prc =  $td['price'];
				$amt =  $td['amount'];
				

				$item = array(
				  "mat"=> $mat,
				  "qty"=> $qty,
				  "price"=> $prc,
				  "amount"=> $amt
				);


				$amt =  $amt * (100 - $T['discount_percent']) / 100;
				if($type=='LOC'){
					$local +=$amt;
					$item['local'] = $amt;
					//$totals['local']+=$amt;
				}
				if($type=='IMP'){
					$import +=$amt;
					$item['import'] = $amt;
					//$totals['import']+=$amt;
				}
				
				if($customer=='RAITECH'):
					$totals['raitech']+=$amt;

				endif;
				

				array_push($materials,$item);
			}

			if($T['discount_percent']>0){
				
				$discItem = array(
				  "mat"=> 'Discount:'. (float)$T['discount_percent'].'%',
				  "qty"=> 1,
				  "price"=> $T['discount'],
				  "amount"=> $T['discount']*-1,
				);
				array_push($materials,$discItem);
				$grand_total = $grand_total - $T['discount'];
			}

			$t =array(
			  "date"=>$date,
			  "customer"=> $customer,
			  "po_number"=> $po,
			  "materials"=> $materials,
			  "grand_total"=> $grand_total,
			  "type"=> "import",
			  "import"=>$import,
			  "local"=>$local,
			  "status"=> $status,
			  "invoice"=>$invoice
			);
			
			//$totals['grand_total']+=$grand_total;
			if($timestamp==$lastDate)
				$totals['po_total']+=$grand_total;
			/*if($status=='served'){
				$totals['served']+=$grand_total;
			}else{
				$totals['pending']+=$grand_total;
			}
			*/
			array_push($details, $t);
		}
		// Total Adjustments
		$totals['grand_total']-=$totals['raitech'];
		$totals['served']  = $totals['grand_total'] -  $totals['pending'];
		if($status!=='served')
			$totals['served']  +=$totals['raitech'];
		$data = array(
			'coverage' =>$coverage,
			'details'=>$details,
			'totals'=>$totals
		);
		//pr($data); exit();
		return $data;

	}

	function buildSalesMonitoring($filter){
		$__Class='Transaction';
		$filterObj = $this->buildFilter($filter);
		$__filter =  $filterObj['filter'];
		$firstDay =  $filterObj['firstDay'];
		$lastDay =  $filterObj['lastDay'];
		$__filter['Transaction.status'] ='fulfilled';
		$conditions =  $__filter;
		$order =array($__Class.'.timestamp ASC');
		$belongsTo = array(
			'Invoice' => array(
			'className' => 'Invoice',
			'foreignKey' => 'ref_no',
			'conditions' => null,
			'order' => '')
		);
		$this->Transaction->recursive=2;
		$this->Transaction->bindModel(array('belongsTo'=>$belongsTo));
		$transactions = $this->Transaction->find('all',compact('conditions','order'));
		$coverage =  array(
			'from'=>$firstDay,
			'to'=>$lastDay
		);
		$details  =array();
		$totals = array(
			"cash"=> 0,
			"check"=> 0,
			"terms"=> 0,
			"raitech"=> 0,
			"grand_total"=> 0
		);
		$dateDetails = array();
		foreach($transactions as $trnx){
			$T =  $trnx['Transaction'];
			$C =  $trnx['Customer'];
			$I =  $trnx['Invoice'];
			$IP =  $I['InvoicePayment'];
			$date =  date('d.m.y',strtotime($T['timestamp']));
			$customer =  $C['alias'];
			$si =  $I['si_no'];
			$dr =  $I['dr_no'];
			$cr =  $I['cr_no'];
			
			if(!isset($dateDetails[$date])){
				$dateDetails[$date] = array('date'=>$date,'data'=>array());
			}
			foreach($IP as $k=>$ip){
				$amt = $ip['amount'];
				$dtlData = array(
					'customer'=>$customer,
					'invoice_no'=>$si,
					'dr_no'=>$dr,
					'amount'=>$amt,
					'cr'=>'',
					'type'=>''
				);
				
				if($ip['payment_type']=='CASH'){
					$dtlData['type'] = 'cash';
					$totals['cash']+=$amt;
				}

				if($ip['payment_type']=='CHCK'||$ip['payment_type']=='CHQE'){
					$dtlData['type'] = 'chck';
					$totals['check']+=$amt;
					$dtlData['cr'] = $ip['ref_no'];
				}

				if($ip['payment_type']=='CHRG'){
					$dtlData['type'] = 'term';
					$totals['terms']+=$amt;
				}
				if($customer=='RAITECH')
					$totals['raitech']+=$amt;
				else
				$totals['grand_total']+=$amt;
			}
			array_push($dateDetails[$date]['data'],$dtlData);
			
		}
		$details = array_values($dateDetails);
		$data = array(
			'coverage' =>$coverage,
			'details'=>$details,
			'totals'=>$totals
		);
		return $data;

	}

	protected function buildFilter($filter,$keyword=null,$fields=null){
		$today = date('Y-m-d', strtotime('0 days'));
		$__Class = 'Transaction';
		$key = 'timestamp';
		$type = $filter;
		$__filter = array($__Class.'.type'=>$type);
		$coverage = 'today';
		$from =  $to = null;
		$firstDay = $from;
		$lastDay = $to;
		if(isset($filter['coverage'])){
			$coverage = $filter['coverage'];
		}
		if(isset($filter['from'])&&isset($filter['to'])){
			$coverage = 'custom';
			$from = date('Y-m-d', strtotime($filter['from']));
			$to = date('Y-m-d', strtotime($filter['to']));
		}
		$search = null;
		if($keyword && $fields){
			$cond = array();
			foreach($fields as $fld){
				if($fld=='entity_name'){
					$cond['OR'] = array();
					if($__Class!='SupplierLedger') $cond['OR']['Customer.name LIKE'] = $keyword;
					if($__Class!='CustomerLedger') $cond['OR']['Supplier.name LIKE'] = $keyword;
				}else if($fld=='po_no'){
					 $cond['OR']['PurchaseOrder.po_no LIKE'] =  $keyword;
					 $cond['OR']['PurchaseOrder.customer LIKE'] =  $keyword;
				}else if($fld=='si_no'){
					$Invoice = &ClassRegistry::init('Invoice');
					$invCond =  array(
						'OR'=>array(
							'Invoice.si_no LIKE'=>$keyword,
							'Invoice.customer LIKE'=>$keyword,
						)
					);
					
					$invFld =  array('Invoice.purchase_order_id','Invoice.id');
					$invList = $Invoice->find('list',array('conditions'=>$invCond,'fields'=>$invFld));
					$invPOs =  array_keys($invList);
					$cond['OR']['PurchaseOrder.id'] = $invPOs;
				}
				else $cond[$__Class.'.'.$fld.' LIKE'] = $keyword;
			}
			$search = $cond;
		}
		switch($coverage){
			case 'today':
				$firstDay = $today;
				$lastDay = $today;
				$__filter[$__Class.'.'.$key.' >='] = $today. ' 00:00:00';
				$__filter[$__Class.'.'.$key.' <='] = $today. ' 23:59:59';
			break;
			case 'yesterday':
				$yesterday = date('Y-m-d', strtotime('-1 day'));
				$firstDay = $yesterday;
				$lastDay = $today;
				$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
				$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
			break;
			case '7D':
				$lastWeek = date('Y-m-d', strtotime('-7 days'));
				$firstDay = $lastWeek;
				$lastDay = $today;
				$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
				$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
			break;
			case '30D':
				$lastMonth = date('Y-m-d', strtotime('-30 days'));
				$firstDay = $lastMonth;
				$lastDay = $today;
				$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
				$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
			break;
			case 'YTD':
				$lastYear = date('Y-m-d', strtotime('-365 days'));
				$firstDay = $lastYear;
				$lastDay = $today;
				$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
				$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
			break;
			case 'SOA': case 'MON':
				$bill_month = 'this';
				$current_month = (int) date('m', strtotime("this month"));
				$firstDay = date('Y-m-d', strtotime("first day of $bill_month month"));
				$lastDay = date('Y-m-d', strtotime("last day of $bill_month  month"));
				$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
				$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
			break;

			case 'custom':
				$firstDay = $from;
				$lastDay = $to;
				$__filter[$__Class.'.'.$key.' >='] = $firstDay. ' 00:00:00';
				$__filter[$__Class.'.'.$key.' <='] = $lastDay. ' 23:59:59';
			break;
		}
		if($search) 
			$__filter =  array($search, array('AND'=>$__filter));


		return array('filter'=>$__filter,'firstDay'=>$firstDay,'lastDay'=>$lastDay);
	}
	function collection(){
		//Paper Size
		// W -  138 mm
		// L -  212 mm
		$data = array(
			"dr_no"=> 1305,
			"received_from"=> "Juan Ñino Dela Cruz",
			"tin"=> "100-1000-000",
			"address"=> "Sto Tomas, Batangas",
			"business_style"=> "Construction",
			"transac_date"=> "01 MAR 2021",
			"amount_paid"=> 13032.50,
			"amount_words"=>"Thirteen Thousand Thirty Two Pesos and Fifty Centavos Only",
			"payment_for"=>"Supplies",
			"settlements"=>array(
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
				array("invoice_no"=>"CSI 3728","amount"=>13032.50),
			),
			"total_amount"=>13032.50,
			"payment_form"=>"check",
			"payment_detail"=>"BDO 123 / 15 MAR 2021",
			"auth_rep"=>"Cashier 1"
		);
		if(isset( $_POST)):
			$TRNX_ID =  $_POST['trnx_id'];
			$TOKEN =  $_POST['token'];
			$TObj = $this->Transaction->findById($TRNX_ID);
			$data = $this->buildCollect($TObj['Transaction']);
		endif;
		


		$this->set(compact('data'));
	}
	function delivery(){
		//Paper Size
		// W -  138 mm
		// L -  212 mm	
		$data =	array(
		  "dr_no"=> 1305,
		  "po_no"=> 'NO',
		  "csi_no"=> 105,
		  "charged_to"=> utf8_decode("Juan Ñino Dela Cruz"),
		  "tin"=> "100-1000-000",
		  "address"=> "Sto Tomas, Batangas",
		  "business_style"=> "Construction",
		  "transac_date"=> "01 MAR 2021",
		  "terms"=> "15 Days",
		  "items"=> array(
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 1 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 2 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 3 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 4 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 5 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 6 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 7 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 8 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 9 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 10 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 11 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 12 25mm x 25mm x0.5mm",
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "article"=> "Item 13 25mm x 25mm x0.5mm",
				)
			),
		  "cshr_auth_rep"=> "Delivery 1"
		);
		
		$data = array();
		if(isset( $_POST['ref_no'])):
			$cond = array('ref_no'=>$_POST['ref_no'],'type'=>'invoice');
			$this->Transaction->recursive = -1;
			$trnx = $this->Transaction->find('first',array('conditions'=>$cond));
			$TID = $trnx['Transaction']['id'];
			$_POST['trnx_id'] = json_encode(array($TID));
			$_POST['token'] =  md5($_POST['trnx_id']);
		endif;
		
		if(isset( $_POST['trnx_id'])):
			$IDS =  json_decode($_POST['trnx_id'],true);
			foreach($IDS as $TRNX_ID):
				$TOKEN =  $_POST['token'];
				$TObj = $this->Transaction->findById($TRNX_ID);
				$delData = $this->buildDelivery($TObj['Transaction']);
				array_push($data, $delData);
			endforeach;
		else:
			$TRNX_ID ='60b6e495-0d14-4db7-887c-3318c0a8006a';
			$TObj = $this->Transaction->findById($TRNX_ID);
			$delData = $this->buildDelivery($TObj['Transaction']);
			array_push($data, $delData);
			
		endif;
		
		$this->set(compact('data'));
	}
	function invoice(){
		//Paper Size
		// W - 109mm
		// L - 212mm
		// Test Data

		$data = array(
		  "po_no"=> '*1231',
		  "csi_no"=> 3728,
		  "dr_no"=> 3728,
		  "charged_to"=> utf8_decode("Juan Ñino Dela Cruz"),
		  "tin"=> "100-1000-000",
		  "address"=> "Sto Tomas, Batangas",
		  "business_style"=> "Construction",
		  "osca_pwd_id_no"=> "N/A",
		  "transac_date"=> "01 MAR 2021",
		  "terms"=> "15 Days",
		  "items"=> array(
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> "",
			  "unit"=> "",
			  "description"=> "DISCOUNT 5%",
			  "u_price"=> "",
			  "amount"=> 52.5
			),
			
			
		  ),
		  "total_sales"=> 13032.5,
		  "less_vat"=> 0,
		  "net_of_vat"=> 13032.5,
		  "less_sc_pwd_disc"=> 0,
		  "amount_due"=> 13032.5,
		  "add_vat"=> 0,
		  "total_amount_due"=> 13032.5,
		  "vat_type"=> "zero-rated",
		  "cshr_auth_rep"=> "Cashier 1"
		);

		//Actual Data
		
		$data = array();
		$version = '2024-2';
		if(isset( $_POST['ref_no'])):
			$cond = array('ref_no'=>$_POST['ref_no'],'type'=>'invoice');
			$this->Transaction->recursive = -1;
			$trnx = $this->Transaction->find('first',array('conditions'=>$cond));
			$TID = $trnx['Transaction']['id'];
			$_POST['trnx_id'] = json_encode(array($TID));
			$_POST['token'] =  md5($_POST['trnx_id']);
		endif;
		if(isset( $_POST['trnx_id'])):
			$IDS =  json_decode($_POST['trnx_id'],true);
			$TOKEN =  $_POST['token'];
			foreach($IDS as $TRNX_ID):
				$TObj = $this->Transaction->findById($TRNX_ID);
				$invData = $this->buildInvoice($TObj['Transaction']);
				array_push($data,$invData);
			endforeach;
			if(isset($_POST['version'])):
				$version = $_POST['version'];
			endif;
		else:
			$TRNX_ID = '66debd41-b504-4984-9485-1448c0a8fe7a';
			$TObj = $this->Transaction->findById($TRNX_ID);

			$data = array($this->buildInvoice($TObj['Transaction']));
		endif;
		//pr($data); exit();
		$this->set(compact('data','version'));
	}

	function po(){
		//Paper Size
		// W - 109mm
		// L - 212mm
		// Test Data

		$data = array(
		  "po_no"=> '*1231',
		  "csi_no"=> 3728,
		  "dr_no"=> 3728,
		  "charged_to"=> "Juan Dela Cruz",
		  "tin"=> "100-1000-000",
		  "address"=> "Sto Tomas, Batangas",
		  "business_style"=> "Construction",
		  "osca_pwd_id_no"=> "N/A",
		  "transac_date"=> "01 MAR 2021",
		  "terms"=> "15 Days",
		  "items"=> array(
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			array(
			  "qty"=> 10,
			  "unit"=> "pc",
			  "description"=> "Item 1 25mm x 25mm x0.5mm",
			  "u_price"=> 100.25,
			  "amount"=> 1002.5
			),
			
		  ),
		  "total_sales"=> 13032.5,
		  "less_vat"=> 0,
		  "net_of_vat"=> 13032.5,
		  "less_sc_pwd_disc"=> 0,
		  "amount_due"=> 13032.5,
		  "add_vat"=> 0,
		  "total_amount_due"=> 13032.5,
		  "vat_type"=> "zero-rated",
		  "cshr_auth_rep"=> "Cashier 1"
		);

		//Actual Data

		if(isset( $_POST['trnx_id'])):
			$TRNX_ID =  $_POST['trnx_id'];
			$TOKEN =  $_POST['token'];

			$TObj = $this->Transaction->findById($TRNX_ID);

			$data = $this->buildPO($TObj['Transaction']);
		else:
			$TRNX_ID =  '60e6bb2a-c1b0-4edf-a094-2dfcc0a8006a';
			$TObj = $this->Transaction->findById($TRNX_ID);
			$data = $this->buildPO($TObj['Transaction']);
		endif;
		//pr($data); exit();
		$this->set(compact('data'));
	}
	
	function monitoring(){
		$data = array(
		  "coverage"=>array(
			"from"=>"01 JAN 2021",
			"to"=>"06 JAN 2021",
		  ),
		  "details"=> array(
			array(
			  "date"=>"01.04.21",
			  "customer"=> "062",
			  "po_number"=> "THRU PHONE",
			  "materials"=> array(
				array(
				  "mat"=> "AL 5052 12M X 105MM X 440MM",
				  "qty"=> 1,
				  "price"=> 1235,
				  "amount"=> 1235
				),
				array(
				  "mat"=> "PU 14MM X 15MM X 120MM",
				  "qty"=> 1,
				  "price"=> 60,
				  "amount"=> 60
				)
			  ),
			  "grand_total"=> 1295,
			  "type"=> "import",
			  "import"=>1295,
			  "local"=>0,
			  "status"=> "served",
			  "invoice"=> "SI-3347"
			),
			array(
			  "date"=> "01.04.21",
			  "customer"=> "058",
			  "po_number"=> "2101-04005",
			  "materials"=> array(
				array(
				  "mat"=> "BRONZE DIA 36MM X 120MM",
				  "qty"=> 1,
				  "price"=> 1130,
				  "amount"=> 1130
				)
			  ),
			  "grand_total"=> 1130,
			  "type"=> "local",
			  "import"=>0,
			  "local"=>1130,
			  "status"=> "served",
			  "invoice"=> "SI-3405"
			),
			array(
			  "date"=> "01.05.21",
			  "customer"=> "066",
			  "po_number"=> "*5627",
			  "materials"=> array(
				array(
				  "mat"=> "BRONZE DIA 36MM X 120MM",
				  "qty"=> 1,
				  "price"=> 1130,
				  "amount"=> 1130
				)
			  ),
			  "grand_total"=> 1130,
			  "type"=> "local",
			  "import"=>0,
			  "local"=>1130,
			  "status"=> "for-ship",
			  "invoice"=> null
			),
			array(
			  "date"=> "01.05.21",
			  "customer"=> "046",
			  "po_number"=> "THRU PHONE",
			  "materials"=> array(
				array(
				  "mat"=> "BRONZE DIA 36MM X 120MM",
				  "qty"=> 1,
				  "price"=> 1130,
				  "amount"=> 1130
				)
			  ),
			  "grand_total"=> 1130,
			  "type"=> "local",
			  "import"=>0,
			  "local"=>1130,
			  "status"=> "for-aprv",
			  "invoice"=> null
			),
			array(
			  "date"=> "01.06.21",
			  "customer"=> "058",
			  "po_number"=> "*210118041",
			  "materials"=> array(
				array(
				  "mat"=> "DELRIN WHITE 12MM X 395MM X 395MM",
				  "qty"=> 1,
				  "price"=> 4000,
				  "amount"=> 4000
				)
			  ),
			  "grand_total"=> 4000,
			  "type"=> "local",
			  "import"=>0,
			  "local"=>1130,
			  "status"=> "cancelled",
			  "invoice"=> "SI-3555"
			)
		  ),
		  "totals"=>array(
			"import"=> 1295,
			"local"=> 7390,
			"grand_total"=> 8585,
			"pending"=> 2425,
			"served"=> 6160,
			"po_total"=> 0
		  )
		);
		//$data = json_encode($data);
		set_time_limit(0);
		if(isset($_POST)){
			$hash = md5($_POST['filter']);
			$filter = json_decode($_POST['filter'],true);
			$keyword = null;
			$fields = null;
			if(isset($_POST['keyword'])) $keyword = '%'.$_POST['keyword'].'%';
			if($keyword&&isset($_POST['fields'])) $fields = explode(',',$_POST['fields']);

			$graph = $_POST['graph'];
			$file = APP.DS.'tmp'.DS.'img'.DS.$hash.".png";
			$uri = substr($graph,strpos($graph, ",") + 1);
			file_put_contents($file, base64_decode($uri));
			$table =  json_decode($_POST['table'],true);

			$data = $this->buildPOMonitoring($filter,$keyword,$fields);
			$data['graph'] =  $file;
			$data['table'] =  $table;
			//pr($data);exit;
		}
		
		//pr($data);exit;

		$this->set(compact('data'));
	}
	
	function monthly_sales(){
		$data = array(
		  "coverage"=>array(
			"from"=>"01 JAN 2021",
			"to"=>"06 JAN 2021",
		  ),
		  "details"=> array(
			array(
				'date'=>'03.04.21',
				'data'=>array(
					array(
						'customer'=>'020',
						'invoice_no'=>'C-1286',
						'dr_no'=>'',
						'type'=>'cash',
						'amount'=>400,
						'cr'=>''
					),
					array(
						'customer'=>'087',
						'invoice_no'=>'SI-3559',
						'dr_no'=>'',
						'type'=>'term',
						'amount'=>1251,
						'cr'=>''
					),
					array(
						'customer'=>'100',
						'invoice_no'=>'SI-3618',
						'dr_no'=>'',
						'type'=>'term',
						'amount'=>600,
						'cr'=>''
					),
					array(
						'customer'=>'099',
						'invoice_no'=>'SI-3617',
						'dr_no'=>'',
						'type'=>'term',
						'amount'=>10766.80,
						'cr'=>''
					),
				)
			),
		  array(
			'date'=>'03.05.21',
			'data'=>array(
				array(
					'customer'=>'020',
					'invoice_no'=>'C-1286',
					'dr_no'=>'',
					'type'=>'cash',
					'amount'=>400,
					'cr'=>''
				),
				array(
					'customer'=>'087',
					'invoice_no'=>'SI-3559',
					'dr_no'=>'',
					'type'=>'term',
					'amount'=>1251,
					'cr'=>''
				),
				array(
					'customer'=>'100',
					'invoice_no'=>'SI-3618',
					'dr_no'=>'',
					'type'=>'term',
					'amount'=>600,
					'cr'=>''
				),
				array(
					'customer'=>'099',
					'invoice_no'=>'SI-3617',
					'dr_no'=>'',
					'type'=>'chck',
					'amount'=>10766.80,
					'cr'=>''
				),
			)
			),
		  ),
		  "totals"=>array(
			"cash"=> 800,
			"check"=> 10766.8,
			"terms"=> 14468.8,
			"raitech"=> 0,
			"grand_total"=> 26035.6
		  )
		);
		//pr($data); exit();
		//$data = json_encode($data);
		if(isset($_GET)){
			$filter = json_decode($_GET['filter'],true);
			$data = $this->buildSalesMonitoring($filter);
		}
		

		$this->set(compact('data'));
		$this->set(compact('data'));
	}
	
	function vendor_po(){
		$data = array();
		$this->set(compact('data'));
	}
	function gen_stk_code(){
		$stockCodes = array();
		for($i=1;$i<=140;$i++):
			do{
				$code = $this->ProductStock->generateStockCode();
			}while(in_array($code, $stockCodes));
			array_push($stockCodes,$code);
		endfor;
		$this->set(compact('stockCodes'));
	}

	function soa(){
	}

	function orders(){
		$refNo=51; // Test RefNo;
		if(isset( $_POST['trnx_id'])):
			$TRNX_ID =  $_POST['trnx_id'];
			$TOKEN =  $_POST['token'];
			$TObj = $this->Transaction->findById($TRNX_ID);
			if($TObj):
				$refNo= $TObj['Transaction']['ref_no'];
			endif;
		elseif(isset( $_POST['ref_no'])):
			$refNo = $_POST['ref_no'];
		endif;
		
		$this->Order->recursive=2;
		$orderObj = $this->Order->findById($refNo);
		$order  = array();
		$order =  $orderObj['Order'];
		$order['order_date'] =  date('M d, Y',strtotime($order['order_date']));
		$order['total'] =  number_format($order['total'],2,'.',',');
		$details = array();
		//pr($orderObj);exit;
		foreach($orderObj['OrderDetail'] as $di=>$dtl){
			$no =  $di+1;
			$desc = $dtl['Product']['display_title'];
			$desc .= 'X '.$dtl['length'].'MM ';
			if($dtl['Product']['category_id']=='PLT8')
				$desc .= 'X '.$dtl['width'].'MM';
			$desc =  strtoupper($desc);
			$qty = $dtl['quantity'];
			$price = number_format($dtl['price'],2,'.',',');
			$amount = number_format($dtl['amount'],2,'.',',');
			$d = array(
				'no'=>$no,
				'desc'=>$desc,
				'qty'=>$qty,
				'price'=>$price,
				'amount'=>$amount,
				);
			array_push($details,$d);
		}
		$order['details'] =  $details;
		$this->set(compact('order'));
	}
}