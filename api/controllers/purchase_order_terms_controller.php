<?php
class PurchaseOrderTermsController extends AppController {

	var $name = 'PurchaseOrderTerms';

	function index() {
		$this->PurchaseOrderTerm->recursive = 0;
		$this->set('purchaseOrderTerms', $this->paginate());
	}

	function view($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid purchase order term', true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('purchaseOrderTerm', $this->PurchaseOrderTerm->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->PurchaseOrderTerm->create();
			if ($this->PurchaseOrderTerm->save($this->data)) {
				$this->Session->setFlash(__('The purchase order term has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The purchase order term could not be saved. Please, try again.', true));
			}
		}
		$purchaseOrders = $this->PurchaseOrderTerm->PurchaseOrder->find('list');
		$this->set(compact('purchaseOrders'));
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->Session->setFlash(__('Invalid purchase order term', true));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			if ($this->PurchaseOrderTerm->save($this->data)) {
				$this->Session->setFlash(__('The purchase order term has been saved', true));
				$this->redirect(array('action' => 'index'));
			} else {
				$this->Session->setFlash(__('The purchase order term could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->PurchaseOrderTerm->read(null, $id);
		}
		$purchaseOrders = $this->PurchaseOrderTerm->PurchaseOrder->find('list');
		$this->set(compact('purchaseOrders'));
	}

	function delete($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid id for purchase order term', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->PurchaseOrderTerm->delete($id)) {
			$this->Session->setFlash(__('Purchase order term deleted', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->Session->setFlash(__('Purchase order term was not deleted', true));
		$this->redirect(array('action' => 'index'));
	}
}
