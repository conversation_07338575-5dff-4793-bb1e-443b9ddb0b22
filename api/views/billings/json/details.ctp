<?php
// Check if customer_billing data exists
if (isset($customer_billing) && !empty($customer_billing)) {
	// Structure the response with customer billing information
	$data = array(
		'customer' => $customer_billing['Customer'],
		'previous_balance' => $customer_billing['previous_balance'],
		'unpaid_invoices' => $customer_billing['unpaid_invoices'],
		'billings' => array()
	);

	// Format the billings data
	if (isset($customer_billing['billings'])) {
		foreach($customer_billing['billings'] as $bill):
			$billObj = $bill['Billing'];
			$billObj['details'] = $bill['BillingDetail'];
			$billObj['customer'] = $bill['Customer'];
			$data['billings'][] = $billObj;
		endforeach;
	}
} else {
	// Fallback to original structure if customer_billing is not set
	$data = array();
	if (isset($billings)) {
		foreach($billings as $bill):
			$billObj = $bill['Billing'];
			$billObj['details'] = $bill['BillingDetail'];
			$billObj['customer'] = $bill['Customer'];
			$data[] = $billObj;
		endforeach;
	}
}

$json = array('status'=>0,'response'=>array('meta'=>$meta,'data'=>$data));
echo json_encode($json);