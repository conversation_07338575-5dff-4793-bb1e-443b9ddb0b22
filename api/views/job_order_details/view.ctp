<div class="jobOrderDetails view">
<h2><?php  __('Job Order Detail');?></h2>
	<dl><?php $i = 0; $class = ' class="altrow"';?>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Id'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['id']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Job Order'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $this->Html->link($jobOrderDetail['JobOrder']['id'], array('controller' => 'job_orders', 'action' => 'view', $jobOrderDetail['JobOrder']['id'])); ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Product'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $this->Html->link($jobOrderDetail['Product']['id'], array('controller' => 'products', 'action' => 'view', $jobOrderDetail['Product']['id'])); ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Length'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['length']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Length Actual'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['length_actual']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Width'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['width']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Width Actual'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['width_actual']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Thickness'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['thickness']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Thickness Actual'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['thickness_actual']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Quantity'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['quantity']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Quatity Actual'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrderDetail['JobOrderDetail']['quatity_actual']; ?>
			&nbsp;
		</dd>
	</dl>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('Edit Job Order Detail', true), array('action' => 'edit', $jobOrderDetail['JobOrderDetail']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('Delete Job Order Detail', true), array('action' => 'delete', $jobOrderDetail['JobOrderDetail']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $jobOrderDetail['JobOrderDetail']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('List Job Order Details', true), array('action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order Detail', true), array('action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Job Orders', true), array('controller' => 'job_orders', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order', true), array('controller' => 'job_orders', 'action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Products', true), array('controller' => 'products', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Product', true), array('controller' => 'products', 'action' => 'add')); ?> </li>
	</ul>
</div>
