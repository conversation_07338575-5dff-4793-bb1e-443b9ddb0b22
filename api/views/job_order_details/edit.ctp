<div class="jobOrderDetails form">
<?php echo $this->Form->create('JobOrderDetail');?>
	<fieldset>
		<legend><?php __('Edit Job Order Detail'); ?></legend>
	<?php
		echo $this->Form->input('id');
		echo $this->Form->input('job_order_id');
		echo $this->Form->input('product_id');
		echo $this->Form->input('length');
		echo $this->Form->input('length_actual');
		echo $this->Form->input('width');
		echo $this->Form->input('width_actual');
		echo $this->Form->input('thickness');
		echo $this->Form->input('thickness_actual');
		echo $this->Form->input('quantity');
		echo $this->Form->input('quatity_actual');
	?>
	</fieldset>
<?php echo $this->Form->end(__('Submit', true));?>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>

		<li><?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $this->Form->value('JobOrderDetail.id')), null, sprintf(__('Are you sure you want to delete # %s?', true), $this->Form->value('JobOrderDetail.id'))); ?></li>
		<li><?php echo $this->Html->link(__('List Job Order Details', true), array('action' => 'index'));?></li>
		<li><?php echo $this->Html->link(__('List Job Orders', true), array('controller' => 'job_orders', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order', true), array('controller' => 'job_orders', 'action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Products', true), array('controller' => 'products', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Product', true), array('controller' => 'products', 'action' => 'add')); ?> </li>
	</ul>
</div>