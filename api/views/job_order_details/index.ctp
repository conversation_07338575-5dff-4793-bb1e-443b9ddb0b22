<div class="jobOrderDetails index">
	<h2><?php __('Job Order Details');?></h2>
	<table cellpadding="0" cellspacing="0">
	<tr>
			<th><?php echo $this->Paginator->sort('id');?></th>
			<th><?php echo $this->Paginator->sort('job_order_id');?></th>
			<th><?php echo $this->Paginator->sort('product_id');?></th>
			<th><?php echo $this->Paginator->sort('length');?></th>
			<th><?php echo $this->Paginator->sort('length_actual');?></th>
			<th><?php echo $this->Paginator->sort('width');?></th>
			<th><?php echo $this->Paginator->sort('width_actual');?></th>
			<th><?php echo $this->Paginator->sort('thickness');?></th>
			<th><?php echo $this->Paginator->sort('thickness_actual');?></th>
			<th><?php echo $this->Paginator->sort('quantity');?></th>
			<th><?php echo $this->Paginator->sort('quatity_actual');?></th>
			<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
	$i = 0;
	foreach ($jobOrderDetails as $jobOrderDetail):
		$class = null;
		if ($i++ % 2 == 0) {
			$class = ' class="altrow"';
		}
	?>
	<tr<?php echo $class;?>>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['id']; ?>&nbsp;</td>
		<td>
			<?php echo $this->Html->link($jobOrderDetail['JobOrder']['id'], array('controller' => 'job_orders', 'action' => 'view', $jobOrderDetail['JobOrder']['id'])); ?>
		</td>
		<td>
			<?php echo $this->Html->link($jobOrderDetail['Product']['id'], array('controller' => 'products', 'action' => 'view', $jobOrderDetail['Product']['id'])); ?>
		</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['length']; ?>&nbsp;</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['length_actual']; ?>&nbsp;</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['width']; ?>&nbsp;</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['width_actual']; ?>&nbsp;</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['thickness']; ?>&nbsp;</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['thickness_actual']; ?>&nbsp;</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['quantity']; ?>&nbsp;</td>
		<td><?php echo $jobOrderDetail['JobOrderDetail']['quatity_actual']; ?>&nbsp;</td>
		<td class="actions">
			<?php echo $this->Html->link(__('View', true), array('action' => 'view', $jobOrderDetail['JobOrderDetail']['id'])); ?>
			<?php echo $this->Html->link(__('Edit', true), array('action' => 'edit', $jobOrderDetail['JobOrderDetail']['id'])); ?>
			<?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $jobOrderDetail['JobOrderDetail']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $jobOrderDetail['JobOrderDetail']['id'])); ?>
		</td>
	</tr>
<?php endforeach; ?>
	</table>
	<p>
	<?php
	echo $this->Paginator->counter(array(
	'format' => __('Page %page% of %pages%, showing %current% records out of %count% total, starting on record %start%, ending on %end%', true)
	));
	?>	</p>

	<div class="paging">
		<?php echo $this->Paginator->prev('<< ' . __('previous', true), array(), null, array('class'=>'disabled'));?>
	 | 	<?php echo $this->Paginator->numbers();?>
 |
		<?php echo $this->Paginator->next(__('next', true) . ' >>', array(), null, array('class' => 'disabled'));?>
	</div>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('New Job Order Detail', true), array('action' => 'add')); ?></li>
		<li><?php echo $this->Html->link(__('List Job Orders', true), array('controller' => 'job_orders', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order', true), array('controller' => 'job_orders', 'action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Products', true), array('controller' => 'products', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Product', true), array('controller' => 'products', 'action' => 'add')); ?> </li>
	</ul>
</div>