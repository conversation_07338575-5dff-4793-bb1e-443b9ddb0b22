<div class="jobOrders view">
<h2><?php  __('Job Order');?></h2>
	<dl><?php $i = 0; $class = ' class="altrow"';?>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Id'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrder['JobOrder']['id']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Po No'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrder['JobOrder']['po_no']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Jo Date'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrder['JobOrder']['jo_date']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('User'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrder['JobOrder']['user']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Remarks'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrder['JobOrder']['remarks']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Created'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $jobOrder['JobOrder']['created']; ?>
			&nbsp;
		</dd>
	</dl>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('Edit Job Order', true), array('action' => 'edit', $jobOrder['JobOrder']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('Delete Job Order', true), array('action' => 'delete', $jobOrder['JobOrder']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $jobOrder['JobOrder']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('List Job Orders', true), array('action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order', true), array('action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Job Order Details', true), array('controller' => 'job_order_details', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order Detail', true), array('controller' => 'job_order_details', 'action' => 'add')); ?> </li>
	</ul>
</div>
<div class="related">
	<h3><?php __('Related Job Order Details');?></h3>
	<?php if (!empty($jobOrder['JobOrderDetail'])):?>
	<table cellpadding = "0" cellspacing = "0">
	<tr>
		<th><?php __('Id'); ?></th>
		<th><?php __('Job Order Id'); ?></th>
		<th><?php __('Product Id'); ?></th>
		<th><?php __('Length'); ?></th>
		<th><?php __('Length Actual'); ?></th>
		<th><?php __('Width'); ?></th>
		<th><?php __('Width Actual'); ?></th>
		<th><?php __('Thickness'); ?></th>
		<th><?php __('Thickness Actual'); ?></th>
		<th><?php __('Quantity'); ?></th>
		<th><?php __('Quatity Actual'); ?></th>
		<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
		$i = 0;
		foreach ($jobOrder['JobOrderDetail'] as $jobOrderDetail):
			$class = null;
			if ($i++ % 2 == 0) {
				$class = ' class="altrow"';
			}
		?>
		<tr<?php echo $class;?>>
			<td><?php echo $jobOrderDetail['id'];?></td>
			<td><?php echo $jobOrderDetail['job_order_id'];?></td>
			<td><?php echo $jobOrderDetail['product_id'];?></td>
			<td><?php echo $jobOrderDetail['length'];?></td>
			<td><?php echo $jobOrderDetail['length_actual'];?></td>
			<td><?php echo $jobOrderDetail['width'];?></td>
			<td><?php echo $jobOrderDetail['width_actual'];?></td>
			<td><?php echo $jobOrderDetail['thickness'];?></td>
			<td><?php echo $jobOrderDetail['thickness_actual'];?></td>
			<td><?php echo $jobOrderDetail['quantity'];?></td>
			<td><?php echo $jobOrderDetail['quatity_actual'];?></td>
			<td class="actions">
				<?php echo $this->Html->link(__('View', true), array('controller' => 'job_order_details', 'action' => 'view', $jobOrderDetail['id'])); ?>
				<?php echo $this->Html->link(__('Edit', true), array('controller' => 'job_order_details', 'action' => 'edit', $jobOrderDetail['id'])); ?>
				<?php echo $this->Html->link(__('Delete', true), array('controller' => 'job_order_details', 'action' => 'delete', $jobOrderDetail['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $jobOrderDetail['id'])); ?>
			</td>
		</tr>
	<?php endforeach; ?>
	</table>
<?php endif; ?>

	<div class="actions">
		<ul>
			<li><?php echo $this->Html->link(__('New Job Order Detail', true), array('controller' => 'job_order_details', 'action' => 'add'));?> </li>
		</ul>
	</div>
</div>
