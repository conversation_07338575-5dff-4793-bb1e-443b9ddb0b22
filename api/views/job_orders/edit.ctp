<div class="jobOrders form">
<?php echo $this->Form->create('JobOrder');?>
	<fieldset>
		<legend><?php __('Edit Job Order'); ?></legend>
	<?php
		echo $this->Form->input('id');
		echo $this->Form->input('po_no');
		echo $this->Form->input('jo_date');
		echo $this->Form->input('user');
		echo $this->Form->input('remarks');
	?>
	</fieldset>
<?php echo $this->Form->end(__('Submit', true));?>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>

		<li><?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $this->Form->value('JobOrder.id')), null, sprintf(__('Are you sure you want to delete # %s?', true), $this->Form->value('JobOrder.id'))); ?></li>
		<li><?php echo $this->Html->link(__('List Job Orders', true), array('action' => 'index'));?></li>
		<li><?php echo $this->Html->link(__('List Job Order Details', true), array('controller' => 'job_order_details', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order Detail', true), array('controller' => 'job_order_details', 'action' => 'add')); ?> </li>
	</ul>
</div>