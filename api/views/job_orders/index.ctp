<div class="jobOrders index">
	<h2><?php __('Job Orders');?></h2>
	<table cellpadding="0" cellspacing="0">
	<tr>
			<th><?php echo $this->Paginator->sort('id');?></th>
			<th><?php echo $this->Paginator->sort('po_no');?></th>
			<th><?php echo $this->Paginator->sort('jo_date');?></th>
			<th><?php echo $this->Paginator->sort('user');?></th>
			<th><?php echo $this->Paginator->sort('remarks');?></th>
			<th><?php echo $this->Paginator->sort('created');?></th>
			<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
	$i = 0;
	foreach ($jobOrders as $jobOrder):
		$class = null;
		if ($i++ % 2 == 0) {
			$class = ' class="altrow"';
		}
	?>
	<tr<?php echo $class;?>>
		<td><?php echo $jobOrder['JobOrder']['id']; ?>&nbsp;</td>
		<td><?php echo $jobOrder['JobOrder']['po_no']; ?>&nbsp;</td>
		<td><?php echo $jobOrder['JobOrder']['jo_date']; ?>&nbsp;</td>
		<td><?php echo $jobOrder['JobOrder']['user']; ?>&nbsp;</td>
		<td><?php echo $jobOrder['JobOrder']['remarks']; ?>&nbsp;</td>
		<td><?php echo $jobOrder['JobOrder']['created']; ?>&nbsp;</td>
		<td class="actions">
			<?php echo $this->Html->link(__('View', true), array('action' => 'view', $jobOrder['JobOrder']['id'])); ?>
			<?php echo $this->Html->link(__('Edit', true), array('action' => 'edit', $jobOrder['JobOrder']['id'])); ?>
			<?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $jobOrder['JobOrder']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $jobOrder['JobOrder']['id'])); ?>
		</td>
	</tr>
<?php endforeach; ?>
	</table>
	<p>
	<?php
	echo $this->Paginator->counter(array(
	'format' => __('Page %page% of %pages%, showing %current% records out of %count% total, starting on record %start%, ending on %end%', true)
	));
	?>	</p>

	<div class="paging">
		<?php echo $this->Paginator->prev('<< ' . __('previous', true), array(), null, array('class'=>'disabled'));?>
	 | 	<?php echo $this->Paginator->numbers();?>
 |
		<?php echo $this->Paginator->next(__('next', true) . ' >>', array(), null, array('class' => 'disabled'));?>
	</div>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('New Job Order', true), array('action' => 'add')); ?></li>
		<li><?php echo $this->Html->link(__('List Job Order Details', true), array('controller' => 'job_order_details', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Job Order Detail', true), array('controller' => 'job_order_details', 'action' => 'add')); ?> </li>
	</ul>
</div>