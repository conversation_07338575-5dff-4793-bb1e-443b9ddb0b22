<div class="purchaseOrders view">
<h2><?php  __('Purchase Order');?></h2>
	<dl><?php $i = 0; $class = ' class="altrow"';?>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Id'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['id']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Po No'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['po_no']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Po Date'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['po_date']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Customer'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['customer']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Total'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['total']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Commission'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['commission']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Discount'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['discount']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Tax'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['tax']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Interest'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['interest']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Created'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrder['PurchaseOrder']['created']; ?>
			&nbsp;
		</dd>
	</dl>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('Edit Purchase Order', true), array('action' => 'edit', $purchaseOrder['PurchaseOrder']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('Delete Purchase Order', true), array('action' => 'delete', $purchaseOrder['PurchaseOrder']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $purchaseOrder['PurchaseOrder']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('List Purchase Orders', true), array('action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order', true), array('action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Purchase Order Details', true), array('controller' => 'purchase_order_details', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order Detail', true), array('controller' => 'purchase_order_details', 'action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Purchase Order Terms', true), array('controller' => 'purchase_order_terms', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order Term', true), array('controller' => 'purchase_order_terms', 'action' => 'add')); ?> </li>
	</ul>
</div>
<div class="related">
	<h3><?php __('Related Purchase Order Details');?></h3>
	<?php if (!empty($purchaseOrder['PurchaseOrderDetail'])):?>
	<table cellpadding = "0" cellspacing = "0">
	<tr>
		<th><?php __('Id'); ?></th>
		<th><?php __('Purchase Order Id'); ?></th>
		<th><?php __('Product Id'); ?></th>
		<th><?php __('Quantity'); ?></th>
		<th><?php __('Price'); ?></th>
		<th><?php __('Amount'); ?></th>
		<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
		$i = 0;
		foreach ($purchaseOrder['PurchaseOrderDetail'] as $purchaseOrderDetail):
			$class = null;
			if ($i++ % 2 == 0) {
				$class = ' class="altrow"';
			}
		?>
		<tr<?php echo $class;?>>
			<td><?php echo $purchaseOrderDetail['id'];?></td>
			<td><?php echo $purchaseOrderDetail['purchase_order_id'];?></td>
			<td><?php echo $purchaseOrderDetail['product_id'];?></td>
			<td><?php echo $purchaseOrderDetail['quantity'];?></td>
			<td><?php echo $purchaseOrderDetail['price'];?></td>
			<td><?php echo $purchaseOrderDetail['amount'];?></td>
			<td class="actions">
				<?php echo $this->Html->link(__('View', true), array('controller' => 'purchase_order_details', 'action' => 'view', $purchaseOrderDetail['id'])); ?>
				<?php echo $this->Html->link(__('Edit', true), array('controller' => 'purchase_order_details', 'action' => 'edit', $purchaseOrderDetail['id'])); ?>
				<?php echo $this->Html->link(__('Delete', true), array('controller' => 'purchase_order_details', 'action' => 'delete', $purchaseOrderDetail['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $purchaseOrderDetail['id'])); ?>
			</td>
		</tr>
	<?php endforeach; ?>
	</table>
<?php endif; ?>

	<div class="actions">
		<ul>
			<li><?php echo $this->Html->link(__('New Purchase Order Detail', true), array('controller' => 'purchase_order_details', 'action' => 'add'));?> </li>
		</ul>
	</div>
</div>
<div class="related">
	<h3><?php __('Related Purchase Order Terms');?></h3>
	<?php if (!empty($purchaseOrder['PurchaseOrderTerm'])):?>
	<table cellpadding = "0" cellspacing = "0">
	<tr>
		<th><?php __('Id'); ?></th>
		<th><?php __('Purchase Order Id'); ?></th>
		<th><?php __('Payment Type'); ?></th>
		<th><?php __('Detail'); ?></th>
		<th><?php __('Amount'); ?></th>
		<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
		$i = 0;
		foreach ($purchaseOrder['PurchaseOrderTerm'] as $purchaseOrderTerm):
			$class = null;
			if ($i++ % 2 == 0) {
				$class = ' class="altrow"';
			}
		?>
		<tr<?php echo $class;?>>
			<td><?php echo $purchaseOrderTerm['id'];?></td>
			<td><?php echo $purchaseOrderTerm['purchase_order_id'];?></td>
			<td><?php echo $purchaseOrderTerm['payment_type'];?></td>
			<td><?php echo $purchaseOrderTerm['detail'];?></td>
			<td><?php echo $purchaseOrderTerm['amount'];?></td>
			<td class="actions">
				<?php echo $this->Html->link(__('View', true), array('controller' => 'purchase_order_terms', 'action' => 'view', $purchaseOrderTerm['id'])); ?>
				<?php echo $this->Html->link(__('Edit', true), array('controller' => 'purchase_order_terms', 'action' => 'edit', $purchaseOrderTerm['id'])); ?>
				<?php echo $this->Html->link(__('Delete', true), array('controller' => 'purchase_order_terms', 'action' => 'delete', $purchaseOrderTerm['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $purchaseOrderTerm['id'])); ?>
			</td>
		</tr>
	<?php endforeach; ?>
	</table>
<?php endif; ?>

	<div class="actions">
		<ul>
			<li><?php echo $this->Html->link(__('New Purchase Order Term', true), array('controller' => 'purchase_order_terms', 'action' => 'add'));?> </li>
		</ul>
	</div>
</div>
