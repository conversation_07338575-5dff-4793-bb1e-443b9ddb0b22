<div class="purchaseOrders form">
<?php echo $this->Form->create('PurchaseOrder');?>
	<fieldset>
		<legend><?php __('Add Purchase Order'); ?></legend>
	<?php
		echo $this->Form->input('po_no');
		echo $this->Form->input('po_date');
		echo $this->Form->input('customer');
		echo $this->Form->input('total');
		echo $this->Form->input('commission');
		echo $this->Form->input('discount');
		echo $this->Form->input('tax');
		echo $this->Form->input('interest');
	?>
	</fieldset>
<?php echo $this->Form->end(__('Submit', true));?>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>

		<li><?php echo $this->Html->link(__('List Purchase Orders', true), array('action' => 'index'));?></li>
		<li><?php echo $this->Html->link(__('List Purchase Order Details', true), array('controller' => 'purchase_order_details', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order Detail', true), array('controller' => 'purchase_order_details', 'action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Purchase Order Terms', true), array('controller' => 'purchase_order_terms', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order Term', true), array('controller' => 'purchase_order_terms', 'action' => 'add')); ?> </li>
	</ul>
</div>