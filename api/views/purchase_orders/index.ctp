<div class="purchaseOrders index">
	<h2><?php __('Purchase Orders');?></h2>
	<table cellpadding="0" cellspacing="0">
	<tr>
			<th><?php echo $this->Paginator->sort('id');?></th>
			<th><?php echo $this->Paginator->sort('po_no');?></th>
			<th><?php echo $this->Paginator->sort('po_date');?></th>
			<th><?php echo $this->Paginator->sort('customer');?></th>
			<th><?php echo $this->Paginator->sort('total');?></th>
			<th><?php echo $this->Paginator->sort('commission');?></th>
			<th><?php echo $this->Paginator->sort('discount');?></th>
			<th><?php echo $this->Paginator->sort('tax');?></th>
			<th><?php echo $this->Paginator->sort('interest');?></th>
			<th><?php echo $this->Paginator->sort('created');?></th>
			<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
	$i = 0;
	foreach ($purchaseOrders as $purchaseOrder):
		$class = null;
		if ($i++ % 2 == 0) {
			$class = ' class="altrow"';
		}
	?>
	<tr<?php echo $class;?>>
		<td><?php echo $purchaseOrder['PurchaseOrder']['id']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['po_no']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['po_date']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['customer']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['total']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['commission']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['discount']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['tax']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['interest']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrder['PurchaseOrder']['created']; ?>&nbsp;</td>
		<td class="actions">
			<?php echo $this->Html->link(__('View', true), array('action' => 'view', $purchaseOrder['PurchaseOrder']['id'])); ?>
			<?php echo $this->Html->link(__('Edit', true), array('action' => 'edit', $purchaseOrder['PurchaseOrder']['id'])); ?>
			<?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $purchaseOrder['PurchaseOrder']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $purchaseOrder['PurchaseOrder']['id'])); ?>
		</td>
	</tr>
<?php endforeach; ?>
	</table>
	<p>
	<?php
	echo $this->Paginator->counter(array(
	'format' => __('Page %page% of %pages%, showing %current% records out of %count% total, starting on record %start%, ending on %end%', true)
	));
	?>	</p>

	<div class="paging">
		<?php echo $this->Paginator->prev('<< ' . __('previous', true), array(), null, array('class'=>'disabled'));?>
	 | 	<?php echo $this->Paginator->numbers();?>
 |
		<?php echo $this->Paginator->next(__('next', true) . ' >>', array(), null, array('class' => 'disabled'));?>
	</div>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('New Purchase Order', true), array('action' => 'add')); ?></li>
		<li><?php echo $this->Html->link(__('List Purchase Order Details', true), array('controller' => 'purchase_order_details', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order Detail', true), array('controller' => 'purchase_order_details', 'action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Purchase Order Terms', true), array('controller' => 'purchase_order_terms', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order Term', true), array('controller' => 'purchase_order_terms', 'action' => 'add')); ?> </li>
	</ul>
</div>