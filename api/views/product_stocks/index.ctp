<div class="productStocks index">
	<h2><?php __('Product Stocks');?></h2>
	<table cellpadding="0" cellspacing="0">
	<tr>
			<th><?php echo $this->Paginator->sort('id');?></th>
			<th><?php echo $this->Paginator->sort('product_id');?></th>
			<th><?php echo $this->Paginator->sort('type');?></th>
			<th><?php echo $this->Paginator->sort('area');?></th>
			<th><?php echo $this->Paginator->sort('notes');?></th>
			<th><?php echo $this->Paginator->sort('created');?></th>
			<th><?php echo $this->Paginator->sort('modified');?></th>
			<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
	$i = 0;
	foreach ($productStocks as $productStock):
		$class = null;
		if ($i++ % 2 == 0) {
			$class = ' class="altrow"';
		}
	?>
	<tr<?php echo $class;?>>
		<td><?php echo $productStock['ProductStock']['id']; ?>&nbsp;</td>
		<td>
			<?php echo $this->Html->link($productStock['Product']['id'], array('controller' => 'products', 'action' => 'view', $productStock['Product']['id'])); ?>
		</td>
		<td><?php echo $productStock['ProductStock']['type']; ?>&nbsp;</td>
		<td><?php echo $productStock['ProductStock']['area']; ?>&nbsp;</td>
		<td><?php echo $productStock['ProductStock']['notes']; ?>&nbsp;</td>
		<td><?php echo $productStock['ProductStock']['created']; ?>&nbsp;</td>
		<td><?php echo $productStock['ProductStock']['modified']; ?>&nbsp;</td>
		<td class="actions">
			<?php echo $this->Html->link(__('View', true), array('action' => 'view', $productStock['ProductStock']['id'])); ?>
			<?php echo $this->Html->link(__('Edit', true), array('action' => 'edit', $productStock['ProductStock']['id'])); ?>
			<?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $productStock['ProductStock']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $productStock['ProductStock']['id'])); ?>
		</td>
	</tr>
<?php endforeach; ?>
	</table>
	<p>
	<?php
	echo $this->Paginator->counter(array(
	'format' => __('Page %page% of %pages%, showing %current% records out of %count% total, starting on record %start%, ending on %end%', true)
	));
	?>	</p>

	<div class="paging">
		<?php echo $this->Paginator->prev('<< ' . __('previous', true), array(), null, array('class'=>'disabled'));?>
	 | 	<?php echo $this->Paginator->numbers();?>
 |
		<?php echo $this->Paginator->next(__('next', true) . ' >>', array(), null, array('class' => 'disabled'));?>
	</div>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('New Product Stock', true), array('action' => 'add')); ?></li>
		<li><?php echo $this->Html->link(__('List Products', true), array('controller' => 'products', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Product', true), array('controller' => 'products', 'action' => 'add')); ?> </li>
	</ul>
</div>