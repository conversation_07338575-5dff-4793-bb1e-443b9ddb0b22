<?php
	App::import('Vendor','receipts');
	
	//pr($data); exit();
	$chunk_data = array_chunk($data['settlements'],8,true);
	$total_page = count($chunk_data);
	$i=1;
	
	
	$receipt =  new ReceiptSheet();
	foreach($chunk_data as $dt){
		$total_per_page = 0;
		foreach($dt as $item){
			$total_per_page+=$item['amount'];
		}
		//pr($dt); exit();
		$data['total_per_page'] = $total_per_page;
		$receipt->hdr($data);
		$receipt->data($data,$dt,$total_page,$i);
		if(count($chunk_data)!=($i++)){
			$receipt->createSheet();
		}
	}
	//$receipt->test($data);
	$receipt->output();