<?php
	App::import('Vendor','monitoring');
	
	//pr($data); exit();
	$mat_count = 0;
	$i=1;
	$pages = 1;
	$page_data = array();
	$receipt = new Monitoring();
	$index = 0;
	$receipt->graph($data);
	foreach($data['details'] as $x=>$det){
		$page_data['details'][$x] = $det;
		$page_data['details'][$x]['materials'] = array();
		foreach($det['materials'] as $a=>$mat){
			array_push($page_data['details'][$x]['materials'],$mat);
			$mat_count++;
			if($mat_count>=30){
				$mat_count=0;
				$pages++;
				$receipt->hdr($data);
				$receipt->data($data,$page_data,$pages,$i);
				$i++;
				$receipt->createSheet();
				$page_data = array();
				$page_data['details'][$x] = $det;
				$page_data['details'][$x]['materials'] = array();
				$index = 0;
			}
		}
		$index++;
	}
	$receipt->hdr($data);
	$receipt->data($data,$page_data,$pages,$i,true);
	//$receipt->createSheet();
	//pr($page_data); 
	//exit();
	$receipt->output();