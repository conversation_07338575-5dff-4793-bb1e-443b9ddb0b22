<?php
	App::import('Vendor','charge_po');
	
	//pr($data); exit();
	$chunk_data = array_chunk($data['items'],10,true);
	$total_page = count($chunk_data);
	$i=1;
	
	
	$receipt =  new ChargePO();
	
	foreach($chunk_data as $dt){
		//pr($dt); exit();
		$total_per_page = 0;
		foreach($dt as $item){
			$total_per_page+=$item['amount'];
		}
		$data['total_per_page'] = $total_per_page;
		$receipt->hdr($data);
		$receipt->data($data,$dt,$total_per_page,$i);
		if(count($chunk_data)!=($i++)){
			$receipt->createSheet();
		}
	}
	//$receipt->test($data);
	$receipt->output();