<?php
	if($version=='2024-2'):
		App::import('Vendor','charge_invoice_2024_2');
	elseif($version=='2024'):
		App::import('Vendor','charge_invoice_2024');
	else:
		App::import('Vendor','charge_invoice');	
	endif;

	//pr($data); exit();
	//$chunk_data = array_chunk($data['items'],10,true);
	$total_page = count($data);
	$i=1;
	
	
	$receipt =  new ChargeInvoice();
	
	foreach($data as $index=>$dt){
		
		$total_per_page = $dt['vatable_sales'];
		
		$receipt->hdr($dt);
		
		$receipt->data($dt,$dt['items'],$total_per_page,$i);
		if($index<$total_page-1){
			$receipt->createSheet();
		}
	}
	//$receipt->test($data);
	$receipt->output();