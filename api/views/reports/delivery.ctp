<?php
	$version = '2024-2';
	if($version=='2024-2'):
		App::import('Vendor','delivery_receipts_2024_2');
	else:
		App::import('Vendor','delivery_receipts');
	endif;
	
	//pr($data); exit();
	//$chunk_data = array_chunk($data['items'],10,true);
	$total_page = count($data);
	$i=1;
	
	$receipt =  new DeliveryReceipt();
	foreach($data as $index=>$dt){
		//pr($dt); exit();

		$total_per_page =  0;
		
		$receipt->hdr($dt);
		$receipt->data($dt,$dt['items'],$total_per_page,$i);
		if($index<$total_page-1){
			$receipt->createSheet();
		}
	}
	//$receipt->test($data);
	$receipt->output();