<div class="purchaseOrderTerms form">
<?php echo $this->Form->create('PurchaseOrderTerm');?>
	<fieldset>
		<legend><?php __('Edit Purchase Order Term'); ?></legend>
	<?php
		echo $this->Form->input('id');
		echo $this->Form->input('purchase_order_id');
		echo $this->Form->input('payment_type');
		echo $this->Form->input('detail');
		echo $this->Form->input('amount');
	?>
	</fieldset>
<?php echo $this->Form->end(__('Submit', true));?>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>

		<li><?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $this->Form->value('PurchaseOrderTerm.id')), null, sprintf(__('Are you sure you want to delete # %s?', true), $this->Form->value('PurchaseOrderTerm.id'))); ?></li>
		<li><?php echo $this->Html->link(__('List Purchase Order Terms', true), array('action' => 'index'));?></li>
		<li><?php echo $this->Html->link(__('List Purchase Orders', true), array('controller' => 'purchase_orders', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order', true), array('controller' => 'purchase_orders', 'action' => 'add')); ?> </li>
	</ul>
</div>