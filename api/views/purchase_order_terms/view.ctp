<div class="purchaseOrderTerms view">
<h2><?php  __('Purchase Order Term');?></h2>
	<dl><?php $i = 0; $class = ' class="altrow"';?>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Id'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrderTerm['PurchaseOrderTerm']['id']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Purchase Order'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $this->Html->link($purchaseOrderTerm['PurchaseOrder']['id'], array('controller' => 'purchase_orders', 'action' => 'view', $purchaseOrderTerm['PurchaseOrder']['id'])); ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Payment Type'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrderTerm['PurchaseOrderTerm']['payment_type']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Detail'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrderTerm['PurchaseOrderTerm']['detail']; ?>
			&nbsp;
		</dd>
		<dt<?php if ($i % 2 == 0) echo $class;?>><?php __('Amount'); ?></dt>
		<dd<?php if ($i++ % 2 == 0) echo $class;?>>
			<?php echo $purchaseOrderTerm['PurchaseOrderTerm']['amount']; ?>
			&nbsp;
		</dd>
	</dl>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('Edit Purchase Order Term', true), array('action' => 'edit', $purchaseOrderTerm['PurchaseOrderTerm']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('Delete Purchase Order Term', true), array('action' => 'delete', $purchaseOrderTerm['PurchaseOrderTerm']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $purchaseOrderTerm['PurchaseOrderTerm']['id'])); ?> </li>
		<li><?php echo $this->Html->link(__('List Purchase Order Terms', true), array('action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order Term', true), array('action' => 'add')); ?> </li>
		<li><?php echo $this->Html->link(__('List Purchase Orders', true), array('controller' => 'purchase_orders', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order', true), array('controller' => 'purchase_orders', 'action' => 'add')); ?> </li>
	</ul>
</div>
