<div class="purchaseOrderTerms index">
	<h2><?php __('Purchase Order Terms');?></h2>
	<table cellpadding="0" cellspacing="0">
	<tr>
			<th><?php echo $this->Paginator->sort('id');?></th>
			<th><?php echo $this->Paginator->sort('purchase_order_id');?></th>
			<th><?php echo $this->Paginator->sort('payment_type');?></th>
			<th><?php echo $this->Paginator->sort('detail');?></th>
			<th><?php echo $this->Paginator->sort('amount');?></th>
			<th class="actions"><?php __('Actions');?></th>
	</tr>
	<?php
	$i = 0;
	foreach ($purchaseOrderTerms as $purchaseOrderTerm):
		$class = null;
		if ($i++ % 2 == 0) {
			$class = ' class="altrow"';
		}
	?>
	<tr<?php echo $class;?>>
		<td><?php echo $purchaseOrderTerm['PurchaseOrderTerm']['id']; ?>&nbsp;</td>
		<td>
			<?php echo $this->Html->link($purchaseOrderTerm['PurchaseOrder']['id'], array('controller' => 'purchase_orders', 'action' => 'view', $purchaseOrderTerm['PurchaseOrder']['id'])); ?>
		</td>
		<td><?php echo $purchaseOrderTerm['PurchaseOrderTerm']['payment_type']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrderTerm['PurchaseOrderTerm']['detail']; ?>&nbsp;</td>
		<td><?php echo $purchaseOrderTerm['PurchaseOrderTerm']['amount']; ?>&nbsp;</td>
		<td class="actions">
			<?php echo $this->Html->link(__('View', true), array('action' => 'view', $purchaseOrderTerm['PurchaseOrderTerm']['id'])); ?>
			<?php echo $this->Html->link(__('Edit', true), array('action' => 'edit', $purchaseOrderTerm['PurchaseOrderTerm']['id'])); ?>
			<?php echo $this->Html->link(__('Delete', true), array('action' => 'delete', $purchaseOrderTerm['PurchaseOrderTerm']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $purchaseOrderTerm['PurchaseOrderTerm']['id'])); ?>
		</td>
	</tr>
<?php endforeach; ?>
	</table>
	<p>
	<?php
	echo $this->Paginator->counter(array(
	'format' => __('Page %page% of %pages%, showing %current% records out of %count% total, starting on record %start%, ending on %end%', true)
	));
	?>	</p>

	<div class="paging">
		<?php echo $this->Paginator->prev('<< ' . __('previous', true), array(), null, array('class'=>'disabled'));?>
	 | 	<?php echo $this->Paginator->numbers();?>
 |
		<?php echo $this->Paginator->next(__('next', true) . ' >>', array(), null, array('class' => 'disabled'));?>
	</div>
</div>
<div class="actions">
	<h3><?php __('Actions'); ?></h3>
	<ul>
		<li><?php echo $this->Html->link(__('New Purchase Order Term', true), array('action' => 'add')); ?></li>
		<li><?php echo $this->Html->link(__('List Purchase Orders', true), array('controller' => 'purchase_orders', 'action' => 'index')); ?> </li>
		<li><?php echo $this->Html->link(__('New Purchase Order', true), array('controller' => 'purchase_orders', 'action' => 'add')); ?> </li>
	</ul>
</div>