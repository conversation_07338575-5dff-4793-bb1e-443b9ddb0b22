-- MySQL dump 10.13  Distrib 5.6.24, for osx10.8 (x86_64)
--
-- Host: *************    Database: inventta_210507
-- ------------------------------------------------------
-- Server version	5.7.33-0ubuntu0.18.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `modules`
--

DROP TABLE IF EXISTS `modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `modules` (
  `id` char(10) NOT NULL,
  `title` varchar(20) DEFAULT NULL,
  `link` varchar(50) DEFAULT NULL,
  `description` varchar(150) DEFAULT NULL,
  `icon` varchar(30) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `type` char(2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `modules`
--

LOCK TABLES `modules` WRITE;
/*!40000 ALTER TABLE `modules` DISABLE KEYS */;
INSERT INTO `modules` VALUES ('accnt','Accounts','accounts/homepage','Manage customer or supplier information and outstanding balances.','user',5,'BR'),('asssmnt','Assessment','inventory/assessment','Process trade in and return transaction from customers using the assessment module.','tags',3,'FD'),('bill','Billing','accounts/billing','Generate customer statement of account','usd',6,'BR'),('delvry','Deliveries','deliveries/homepage','Record incoming items using the delivery module.','log-in',4,'FD'),('invoice','Sales Invoice','sales/invoice','Complete PO with Sales Invoice','shopping-cart',2,'FD'),('invtry','Inventory','inventory/homepage','Keep track of your stocks and pricing using the','inbox',1,'BR'),('jo','Job Order','orders/job','Prepare items for cutting','scissors',3,'BR'),('ldgr','Ledgers','accounts/ledger','Review customer or supplier charges and payments.','th-list',8,'BR'),('montr','Monitoring','transactions/monitoring','Monitor Purchase Order and Sales','transfer',4,'BR'),('ordrs','Supplier Orders','orders/homepage','Request new inventory using the purchase order module.','log-out',5,'FD'),('po','Purchase Order','orders/purchase','Create PO from customers','folder-open',1,'FD'),('rtrnordr','Return Orders','inventory/returnorder','Send back items to supplier using the return.','repeat',6,'FD'),('sales','Sales','sales/homepage','Sell products using the point of sales module.','shopping-cart',0,'FD'),('stckrm','Stock Room','inventory/stockroom','Update your physical count using the stock room module.','object-align-bottom',2,'BR'),('trnx','Transactions','transactions/homepage','Review all inbound and outbound transactions.','transfer',8,'BR'),('users','Users','accounts/users','Manage staff\'s access using the user module.','pushpin',7,'BR');
/*!40000 ALTER TABLE `modules` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2021-05-10 13:46:41
