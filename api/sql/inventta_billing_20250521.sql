-- <PERSON><PERSON>B dump 10.19  Distrib 10.4.27-MariaD<PERSON>, for osx10.10 (x86_64)
--
-- Host: localhost    Database: inventta_250514
-- ------------------------------------------------------
-- Server version	10.4.27-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `billings`
--

DROP TABLE IF EXISTS `billings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `billings` (
  `id` char(20) NOT NULL COMMENT 'Format: RIC-SOA-MMYY-NNNN where MMYY is month/year and NNNN is sequence',
  `customer_id` int(11) DEFAULT NULL,
  `status` char(10) DEFAULT 'active',
  `due_date` date DEFAULT NULL,
  `terms` varchar(20) DEFAULT NULL,
  `due_amount` decimal(12,2) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `hash` varchar(64) DEFAULT NULL COMMENT 'SHA-256 hash for verification',
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `billing_details`
--

DROP TABLE IF EXISTS `billing_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `billing_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `billing_id` varchar(20) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL COMMENT 'Reference to invoice number',
  `invoice_date` date DEFAULT NULL,
  `invoice_amount` decimal(12,2) DEFAULT NULL,
  `status` char(10) DEFAULT 'active',
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_billing_details_billing_id` (`billing_id`),
  CONSTRAINT `fk_billing_details_billing_id` FOREIGN KEY (`billing_id`) REFERENCES `billings` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-21 12:28:17

ALTER TABLE customers ADD bill_ending_balance DECIMAL(12,2) NULL;
ALTER TABLE customers ADD bill_as_of_date DATE NULL;
ALTER TABLE customers ADD bill_mobile_no varchar(15) NULL;
ALTER TABLE customers ADD bill_email varchar(100) NULL;

ALTER TABLE customers ADD COLUMN account_no VARCHAR(10) DEFAULT NULL AFTER id;
