-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.5
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Feb 27, 2021 at 03:40 PM
-- Server version: 10.1.38-MariaDB
-- PHP Version: 5.6.40

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `inventta_210227`
--

-- --------------------------------------------------------

--
-- Table structure for table `cash_flows`
--

CREATE TABLE `cash_flows` (
  `id` char(36) NOT NULL,
  `particulars` varchar(25) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `flag` char(1) NOT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` char(4) NOT NULL,
  `name` varchar(30) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(150) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `last_bill` int(11) NOT NULL,
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `status`, `last_bill`, `begin_balance`, `current_balance`, `created`, `modified`) VALUES
(1, 'CASH', 'open', 0, '0.00', '0.00', NULL, NULL),
(2, 'Customer A', 'open', 0, '0.00', '0.00', '2021-02-27 22:39:20', '2021-02-27 22:39:20');

-- --------------------------------------------------------

--
-- Table structure for table `customer_ledgers`
--

CREATE TABLE `customer_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `customer_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `deliveries`
--

CREATE TABLE `deliveries` (
  `id` int(11) NOT NULL,
  `delivery_date` date DEFAULT NULL,
  `supplier` varchar(50) DEFAULT NULL,
  `doc_no` int(11) DEFAULT NULL,
  `source` char(10) DEFAULT 'delivery' COMMENT 'return,order,delivery',
  `total` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `delivery_details`
--

CREATE TABLE `delivery_details` (
  `id` int(11) NOT NULL,
  `delivery_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `delivery_payments`
--

CREATE TABLE `delivery_payments` (
  `id` int(11) NOT NULL,
  `delivery_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `inventory_adjustments`
--

CREATE TABLE `inventory_adjustments` (
  `id` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `inventory_logs`
--

CREATE TABLE `inventory_logs` (
  `id` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `old_quantity` int(11) DEFAULT NULL,
  `act_quantity` int(11) DEFAULT NULL,
  `new_quantity` int(11) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `created` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `id` int(11) NOT NULL,
  `invoice_date` date DEFAULT NULL,
  `customer` varchar(80) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `created` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `invoice_details`
--

CREATE TABLE `invoice_details` (
  `id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `invoice_payments`
--

CREATE TABLE `invoice_payments` (
  `id` int(11) NOT NULL,
  `invoice_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `modules`
--

CREATE TABLE `modules` (
  `id` char(10) NOT NULL,
  `title` varchar(20) DEFAULT NULL,
  `link` varchar(50) DEFAULT NULL,
  `description` varchar(150) DEFAULT NULL,
  `icon` varchar(30) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `type` char(2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `modules`
--

INSERT INTO `modules` (`id`, `title`, `link`, `description`, `icon`, `order`, `type`) VALUES
('accnt', 'Accounts', 'accounts/homepage', 'Manage customer or supplier information and outstanding balances.', 'user', 3, 'BR'),
('asssmnt', 'Assessment', 'inventory/assessment', 'Process trade in and return transaction from customers using the assessment module.', 'tags', 2, 'FD'),
('delvry', 'Deliveries', 'deliveries/homepage', 'Record incoming items using the delivery module.', 'log-in', 3, 'FD'),
('invtry', 'Inventory', 'inventory/homepage', 'Keep track of your stocks and pricing using the', 'inbox', 1, 'BR'),
('ldgr', 'Ledgers', 'accounts/ledger', 'Review customer or supplier charges and payments.', 'th-list', 4, 'BR'),
('ordrs', 'Orders', 'orders/homepage', 'Request new inventory using the purchase order module.', 'log-out', 4, 'FD'),
('rtrnordr', 'Return Orders', 'inventory/returnorder', 'Send back items to supplier using the return.', 'repeat', 5, 'FD'),
('sales', 'Sales', 'sales/homepage', 'Sell products using the point of sales module.', 'shopping-cart', 1, 'FD'),
('stckrm', 'Stock Room', 'inventory/stockroom', 'Update your physical count using the stock room module.', 'object-align-bottom', 6, 'BR'),
('trnx', 'Transactions', 'transactions/homepage', 'Review all inbound and outbound transactions.', 'transfer', 2, 'BR'),
('users', 'Users', 'accounts/users', 'Manage staff\'s access using the user module.', 'pushpin', 5, 'BR');

-- --------------------------------------------------------

--
-- Table structure for table `module_users`
--

CREATE TABLE `module_users` (
  `id` char(36) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `module_id` char(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `module_users`
--

INSERT INTO `module_users` (`id`, `user_id`, `module_id`) VALUES
('574fe2cb-001c-466c-8f4b-04d8c0a80107', 6, 'users'),
('574fe2cb-0d5c-463b-a870-04d8c0a80107', 6, 'ordrs'),
('574fe2cb-1a9c-4492-a90c-04d8c0a80107', 6, 'sales'),
('574fe2cb-6bd8-4887-8088-04d8c0a80107', 6, 'invtry'),
('574fe2cb-797c-4eef-bd3b-04d8c0a80107', 6, 'rtrnordr'),
('574fe2cb-7ad4-41e9-91f5-04d8c0a80107', 6, 'accnt'),
('574fe2cb-93fc-4b98-a9a6-04d8c0a80107', 6, 'asssmnt'),
('574fe2cb-a13c-4f61-8151-04d8c0a80107', 6, 'trnx'),
('574fe2cb-d7f8-4c09-92e2-04d8c0a80107', 6, 'ldgr'),
('574fe2cb-e538-4c02-8bd4-04d8c0a80107', 6, 'stckrm'),
('574fe2cb-f2dc-440f-8790-04d8c0a80107', 6, 'delvry'),
('5bff6078-162c-4838-9067-0850c0a8fe07', 5, 'ordrs'),
('5bff6078-162c-4b09-bf80-0850c0a8fe07', 5, 'rtrnordr'),
('5bff6078-23d0-44e2-b89f-0850c0a8fe07', 5, 'users'),
('5bff6078-23d0-45b8-b2e0-0850c0a8fe07', 5, 'accnt'),
('5bff6078-258c-49da-b1d3-0850c0a8fe07', 5, 'invtry'),
('5bff6078-3110-4557-bf06-0850c0a8fe07', 5, 'ldgr'),
('5bff6078-8f8c-4151-95da-0850c0a8fe07', 5, 'stckrm'),
('5bff6078-9d30-4965-b407-0850c0a8fe07', 5, 'asssmnt'),
('5bff6078-9d30-4994-b685-0850c0a8fe07', 5, 'sales'),
('5bff6078-aa70-42ed-948a-0850c0a8fe07', 5, 'delvry'),
('5bff6078-aa70-4f21-bbde-0850c0a8fe07', 5, 'trnx'),
('601753d5-0510-4773-a24b-076c68f89d23', 1, 'asssmnt'),
('601753d5-2ac8-44e3-947d-076c68f89d23', 1, 'trnx'),
('601753d5-2d8c-495d-b1a2-076c68f89d23', 1, 'ldgr'),
('601753d5-5284-41bf-a23e-076c68f89d23', 1, 'stckrm'),
('601753d5-6740-4491-9c19-076c68f89d23', 1, 'sales'),
('601753d5-7aec-4150-ada3-076c68f89d23', 1, 'delvry'),
('601753d5-89d8-496d-aa7d-076c68f89d23', 1, 'ordrs'),
('601753d5-98b8-4015-a7ae-076c68f89d23', 1, 'invtry'),
('601753d5-c1fc-4b7d-b040-076c68f89d23', 1, 'accnt'),
('601753d5-f5cc-4f50-9c4b-076c68f89d23', 1, 'users'),
('602a5ec9-1ac0-477a-a86c-745d68f89d23', 2, 'rtrnordr'),
('602a5ec9-40c0-436a-881f-745d68f89d23', 2, 'trnx'),
('602a5ec9-6ee0-4c9e-9677-745d68f89d23', 2, 'ordrs'),
('602a5ec9-71b8-45b6-a2b6-745d68f89d23', 2, 'invtry'),
('602a5ec9-9918-47de-9259-745d68f89d23', 2, 'asssmnt'),
('602a5ec9-ae4c-46ce-ac08-745d68f89d23', 2, 'users'),
('602a5ec9-b188-4322-b450-745d68f89d23', 2, 'sales'),
('602a5ec9-e4bc-4d0e-99d9-745d68f89d23', 2, 'delvry');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `supplier` varchar(50) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `order_details`
--

CREATE TABLE `order_details` (
  `id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `price_logs`
--

CREATE TABLE `price_logs` (
  `id` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `old_price` decimal(10,2) DEFAULT NULL,
  `new_price` decimal(10,2) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `ref_no` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `category_id` char(4) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'active',
  `particular` varchar(50) DEFAULT ' ',
  `length` decimal(7,3) DEFAULT NULL,
  `width` decimal(7,3) DEFAULT NULL,
  `thickness` decimal(7,3) NOT NULL,
  `part_no` varchar(30) DEFAULT ' ',
  `unit` varchar(10) DEFAULT ' ',
  `description` varchar(255) DEFAULT ' ',
  `capital` decimal(8,2) DEFAULT NULL,
  `markup` decimal(8,2) DEFAULT NULL,
  `srp` decimal(8,2) DEFAULT NULL,
  `tmp_srp` decimal(10,2) NOT NULL,
  `soh_quantity` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `min_quantity` int(11) DEFAULT NULL,
  `max_quantity` int(11) DEFAULT NULL,
  `discountable` tinyint(1) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `category_id`, `status`, `particular`, `length`, `width`, `thickness`, `part_no`, `unit`, `description`, `capital`, `markup`, `srp`, `tmp_srp`, `soh_quantity`, `tmp_quantity`, `min_quantity`, `max_quantity`, `discountable`, `created`, `modified`) VALUES
(1, 'PLT8', 'active', 'AS ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 2972897, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:23:19'),
(2, 'PLT8', 'active', 'AS ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 2972897, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:23:29'),
(3, 'PLT8', 'active', 'AS ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 2972897, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:23:33'),
(4, 'PLT8', 'active', 'AS ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 2972897, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:23:38'),
(5, 'PLT8', 'active', 'AS POLYCARBONATE  (CLEAR) ', '1219.200', '2438.400', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 2972897, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:23:42'),
(6, 'PLT8', 'active', 'AS POLYCARBONATE  (CLEAR) ', '1219.200', '2438.400', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(7, 'PLT8', 'active', 'AS POLYCARBONATE  (CLEAR) ', '1219.200', '2438.400', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(8, 'PLT8', 'active', 'AS POLYCARBONATE  (CLEAR) ', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(9, 'PLT8', 'active', 'AS POLYCARBONATE  (CLEAR) ', '1219.200', '2438.400', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(10, 'PLT8', 'active', 'AS POLYCARBONATE  (CLEAR) ', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(11, 'PLT8', 'active', 'PET  (CLEAR)', '1200.000', '1200.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(12, 'PLT8', 'active', 'PET  (CLEAR)', '1200.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(13, 'PLT8', 'active', 'PET  (CLEAR)', '1200.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(14, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(15, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(16, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(17, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(18, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(19, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(20, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(21, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(22, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(23, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(24, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(25, 'PLT8', 'active', 'POLYCARBONATE   (CLEAR) ', '1219.200', '2438.400', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(26, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(27, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(28, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(29, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(30, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(31, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(32, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(33, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(34, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(35, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(36, 'PLT8', 'active', 'PVC  (CLEAR)', '1219.200', '2438.400', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(37, 'PLT8', 'active', 'PVC  (CLEAR)', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(38, 'PLT8', 'active', 'PVC  (CLEAR)', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(39, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(40, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(41, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(42, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(43, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(44, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(45, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(46, 'PLT8', 'active', 'ABS  (IVORY) ', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(47, 'PLT8', 'active', 'ABS  (IVORY) ', '1200.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(48, 'PLT8', 'active', 'ABS  (IVORY) ', '1200.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(49, 'PLT8', 'active', 'ABS  (IVORY) ', '1200.000', '2000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(50, 'PLT8', 'active', 'ABS  (IVORY) ', '1200.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(51, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(52, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(53, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(54, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '4.600', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(55, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 2972897, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:28:34'),
(56, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(57, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(58, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(59, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(60, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '18.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(61, 'PLT8', 'active', 'ACRYLIC  (CLEAR) ', '1219.200', '2438.400', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(62, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(63, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(64, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(65, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(66, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(67, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(68, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(69, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(70, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(71, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(72, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(73, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(74, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(75, 'PLT8', 'active', 'BAKELITE  (ORANGE)', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(76, 'PLT8', 'active', 'CDM  (BLACK) ', '1219.200', '2438.400', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(77, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(78, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(79, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(80, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(81, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(82, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(83, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(84, 'PLT8', 'active', 'CDM  (BLACK) ', '1150.000', '1250.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(85, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(86, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(87, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '7.500', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(88, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(89, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(90, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '16.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(91, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(92, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(93, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(94, 'PLT8', 'active', 'MC501  CDR6  (BLACK) ', '600.000', '1200.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(95, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:35:47'),
(96, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:35:58'),
(97, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:35:53'),
(98, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:36:02'),
(99, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:36:07'),
(100, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:36:11'),
(101, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:36:16'),
(102, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:36:21'),
(103, 'RODS', 'active', 'MC501 CDR6', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2021-02-27 22:36:26'),
(104, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(105, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(106, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(107, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(108, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(109, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(110, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(111, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(112, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(113, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(114, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(115, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(116, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(117, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(118, 'PLT8', 'active', 'DELRIN  (WHITE) ', '1000.000', '2000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(119, 'PLT8', 'active', 'DELRIN  (WHITE) ', '600.000', '1200.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(120, 'PLT8', 'active', 'DELRIN  (WHITE) ', '600.000', '1200.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(121, 'PLT8', 'active', 'DELRIN  (WHITE) ', '600.000', '1200.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(122, 'PLT8', 'active', 'DELRIN  (WHITE) ', '600.000', '1219.200', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(123, 'PLT8', 'active', 'DELRIN  (WHITE) ', '600.000', '1219.200', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(124, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(125, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(126, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(127, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(128, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(129, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(130, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(131, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(132, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(133, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(134, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(135, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(136, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(137, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(138, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '65.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(139, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(140, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(141, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(142, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '85.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(143, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(144, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '95.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(145, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(146, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(147, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(148, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(149, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(150, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '145.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(151, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(152, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(153, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(154, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(155, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(156, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(157, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(158, 'ROD', 'active', 'DELRIN (WHITE) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(159, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(160, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(161, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(162, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(163, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(164, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(165, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(166, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(167, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(168, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(169, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(170, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(171, 'PLT8', 'active', 'DELRIN  (BLACK) ', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(172, 'PLT8', 'active', 'DELRIN  (BLACK) ', '600.000', '1219.200', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(173, 'PLT8', 'active', 'DELRIN  (BLACK) ', '600.000', '1219.200', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(174, 'PLT8', 'active', 'DELRIN  (BLACK) ', '600.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(175, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(176, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(177, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(178, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(179, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(180, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(181, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(182, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(183, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(184, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(185, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(186, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(187, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(188, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '65.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(189, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(190, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(191, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(192, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '85.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(193, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(194, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '95.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(195, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(196, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(197, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(198, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(199, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(200, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '145.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(201, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(202, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(203, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(204, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(205, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(206, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(207, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(208, 'ROD', 'active', 'DELRIN (BLACK) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(209, 'PLT8', 'active', 'DELRIN  (BLUE)', '600.000', '1200.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(210, 'PLT8', 'active', 'DELRIN  (BLUE)', '600.000', '1200.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(211, 'PLT8', 'active', 'DELRIN  (BLUE)', '600.000', '1200.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(212, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '0.500', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(213, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '0.800', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(214, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '1.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(215, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '0.900', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(216, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(217, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(218, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '4.500', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(219, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(220, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(221, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(222, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(223, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(224, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(225, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(226, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(227, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(228, 'PLT8', 'active', 'FR4 G10 (GREEN) ', '1000.000', '1200.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(229, 'PLT8', 'active', 'G11 (YELLOW) ', '1200.000', '1200.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(230, 'PLT8', 'active', 'G11 (YELLOW) ', '1200.000', '1200.000', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(231, 'PLT8', 'active', 'G11 (YELLOW) ', '1200.000', '1200.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(232, 'PLT8', 'active', 'G11 (YELLOW) ', '1200.000', '1200.000', '5.500', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(233, 'PLT8', 'active', 'G11 (YELLOW) ', '1200.000', '1200.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(234, 'PLT8', 'active', 'G11 (YELLOW) ', '1200.000', '1200.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(235, 'PLT8', 'active', 'G11 (YELLOW) ', '1000.000', '1200.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(236, 'PLT8', 'active', 'G11 (YELLOW) ', '1000.000', '1200.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(237, 'PLT8', 'active', 'G11 (YELLOW) ', '1000.000', '1200.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(238, 'PLT8', 'active', 'G11 (YELLOW) ', '1000.000', '1200.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(239, 'PLT8', 'active', 'G11 (YELLOW) ', '1000.000', '1200.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(240, 'PLT8', 'active', 'G11 (YELLOW) ', '1000.000', '1200.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(241, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(242, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(243, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(244, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(245, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(246, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(247, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(248, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(249, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(250, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(251, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(252, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(253, 'PLT8', 'active', 'NYLON  (BLUE) ', '1000.000', '2000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(254, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(255, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(256, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(257, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(258, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(259, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(260, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(261, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(262, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(263, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(264, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(265, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(266, 'ROD', 'active', 'NYLON  (BLUE) ', '600.000', '0.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(267, 'ROD', 'active', 'NYLON  (BLUE) ', '500.000', '0.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(268, 'ROD', 'active', 'NYLON  (BLUE) ', '600.000', '0.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(269, 'ROD', 'active', 'NYLON  (BLUE) ', '600.000', '0.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(270, 'ROD', 'active', 'NYLON  (BLUE) ', '600.000', '0.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45');
INSERT INTO `products` (`id`, `category_id`, `status`, `particular`, `length`, `width`, `thickness`, `part_no`, `unit`, `description`, `capital`, `markup`, `srp`, `tmp_srp`, `soh_quantity`, `tmp_quantity`, `min_quantity`, `max_quantity`, `discountable`, `created`, `modified`) VALUES
(271, 'ROD', 'active', 'NYLON  (BLUE) ', '600.000', '0.000', '65.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(272, 'ROD', 'active', 'NYLON  (BLUE) ', '600.000', '0.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(273, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(274, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(275, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(276, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(277, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(278, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(279, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '115.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(280, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(281, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(282, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(283, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(284, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(285, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(286, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(287, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(288, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(289, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '210.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(290, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(291, 'ROD', 'active', 'NYLON  (BLUE) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(292, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(293, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(294, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(295, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(296, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(297, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(298, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(299, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(300, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(301, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(302, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(303, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(304, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(305, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(306, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(307, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(308, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(309, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(310, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(311, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(312, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(313, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(314, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(315, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(316, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(317, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(318, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(319, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(320, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(321, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(322, 'ROD', 'active', 'NYLON IVORY ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(323, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(324, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(325, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(326, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(327, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(328, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(329, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(330, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(331, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(332, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(333, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(334, 'PLT8', 'active', 'NYLON (ivory)', '1000.000', '2000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(335, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(336, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(337, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(338, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(339, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(340, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(341, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(342, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(343, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(344, 'PLT8', 'active', 'NYLON  (BLACK)', '1000.000', '2000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(345, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(346, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(347, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(348, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(349, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(350, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(351, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(352, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(353, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(354, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(355, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(356, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(357, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(358, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(359, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(360, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(361, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(362, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(363, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(364, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(365, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(366, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(367, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(368, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(369, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(370, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(371, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(372, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(373, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(374, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(375, 'ROD', 'active', 'NYLON (BLACK) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(376, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(377, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(378, 'PLT8', 'active', 'PE  (WHITE) ', '1219.200', '2438.400', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(379, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(380, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(381, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(382, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(383, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(384, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(385, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(386, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(387, 'PLT8', 'active', 'PE  (WHITE) ', '1000.000', '2000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(388, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(389, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(390, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(391, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(392, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(393, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(394, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(395, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(396, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(397, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(398, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(399, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(400, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(401, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(402, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(403, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(404, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(405, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(406, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(407, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(408, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(409, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(410, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(411, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(412, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(413, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(414, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(415, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(416, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(417, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(418, 'ROD', 'active', 'PE  (WHITE) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(419, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(420, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(421, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(422, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(423, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(424, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(425, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(426, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(427, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(428, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(429, 'PLT8', 'active', 'PE  (BLACK) ', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(430, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(431, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(432, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(433, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(434, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(435, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(436, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(437, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(438, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(439, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(440, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(441, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(442, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(443, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(444, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(445, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(446, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(447, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '85.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(448, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(449, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(450, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(451, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(452, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(453, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(454, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(455, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(456, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(457, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(458, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(459, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(460, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(461, 'ROD', 'active', 'PE (BLACK) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(462, 'PLT8', 'active', 'PE  (GREEN)', '1000.000', '2000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(463, 'PLT8', 'active', 'PE  (GREEN)', '1000.000', '2000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(464, 'PLT8', 'active', 'PE  (GREEN)', '1000.000', '2000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(465, 'PLT8', 'active', 'PE  (GREEN)', '1000.000', '2000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(466, 'PLT8', 'active', 'PE  (GREEN)', '1000.000', '2000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(467, 'PLT8', 'active', 'PE  (GREEN)', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(468, 'PLT8', 'active', 'PE  (GREEN)', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(469, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(470, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(471, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(472, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(473, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(474, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(475, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '16.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(476, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(477, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '22.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(478, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(479, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(480, 'PLT8', 'active', 'PEEK  (BEIGE) ', '600.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(481, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(482, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(483, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(484, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(485, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(486, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(487, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(488, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(489, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(490, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(491, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(492, 'ROD', 'active', 'PEEK  (BEIGE) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(493, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(494, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(495, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(496, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(497, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(498, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(499, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(500, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(501, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(502, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(503, 'PLT8', 'active', 'PP  (NAT WHITE) ', '1219.200', '2438.400', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(504, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(505, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(506, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(507, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(508, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(509, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(510, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(511, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(512, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(513, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(514, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(515, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(516, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(517, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '65.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(518, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(519, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(520, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(521, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(522, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '85.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(523, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(524, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '95.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(525, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(526, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(527, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(528, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(529, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(530, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(531, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(532, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(533, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(534, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(535, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(536, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(537, 'ROD', 'active', 'PP  (NAT WHITE) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(538, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '2.700', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(539, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(540, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(541, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(542, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(543, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45');
INSERT INTO `products` (`id`, `category_id`, `status`, `particular`, `length`, `width`, `thickness`, `part_no`, `unit`, `description`, `capital`, `markup`, `srp`, `tmp_srp`, `soh_quantity`, `tmp_quantity`, `min_quantity`, `max_quantity`, `discountable`, `created`, `modified`) VALUES
(544, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(545, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(546, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(547, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(548, 'PLT8', 'active', 'PVC  (GRAY) ', '1219.200', '2438.400', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(549, 'PLT8', 'active', 'PVC  (GRAY) ', '1000.000', '2000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(550, 'PLT8', 'active', 'PVC  (GRAY) ', '1000.000', '2000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(551, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(552, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(553, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(554, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(555, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(556, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(557, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(558, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(559, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(560, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(561, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(562, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(563, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(564, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(565, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(566, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(567, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(568, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '1000.000', '85.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(569, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(570, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(571, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(572, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(573, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(574, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(575, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(576, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(577, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(578, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(579, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(580, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(581, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(582, 'ROD', 'active', 'PVC  (GRAY) ', '1000.000', '0.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(583, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(584, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '4.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(585, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '4.500', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(586, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(587, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(588, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(589, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(590, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(591, 'PLT8', 'active', 'RICOCEL  (BLACK)  ', '1200.000', '1200.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(592, 'PLT8', 'active', 'ULTEM  (AMBER) ', '600.000', '1200.000', '9.525', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(593, 'PLT8', 'active', 'ULTEM  (AMBER) ', '600.000', '1200.000', '9.525', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(594, 'PLT8', 'active', 'ULTEM  (AMBER) ', '600.000', '1200.000', '12.700', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(595, 'PLT8', 'active', 'ULTEM  (AMBER) ', '600.000', '1200.000', '12.700', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(596, 'PLT8', 'active', 'ULTEM  (AMBER) ', '600.000', '1200.000', '15.875', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(597, 'PLT8', 'active', 'ULTEM  (AMBER) ', '600.000', '1200.000', '25.400', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(598, 'PLT8', 'active', 'ULTEM  (AMBER) ', '600.000', '1200.000', '31.750', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(599, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '6.350', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(600, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '9.525', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(601, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '12.700', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(602, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '15.875', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(603, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '19.050', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(604, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '25.400', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(605, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '31.750', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(606, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '38.100', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(607, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '44.450', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(608, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '63.750', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(609, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '76.200', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(610, 'ROD', 'active', 'ULTEM  (AMBER) ', '1000.000', '1000.000', '69.850', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(611, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '6.350', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(612, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '9.525', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(613, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '12.700', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(614, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '15.875', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(615, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '19.050', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(616, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '22.220', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(617, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '25.400', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(618, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '31.750', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(619, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '38.100', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(620, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '44.450', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(621, 'ROD', 'active', 'SEMITRON ESD225   (IVORY) ', '1000.000', '1000.000', '50.800', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(622, 'ROD', 'active', 'POLYCARBONATE   (CLEAR) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(623, 'ROD', 'active', 'POLYCARBONATE   (CLEAR) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(624, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(625, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(626, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(627, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(628, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(629, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(630, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(631, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(632, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(633, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(634, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(635, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(636, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(637, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(638, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(639, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(640, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(641, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(642, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(643, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(644, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(645, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(646, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(647, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(648, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(649, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '160.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(650, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '170.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(651, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '180.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(652, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '190.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(653, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '200.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(654, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '220.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(655, 'ROD', 'active', 'TEFLON  (WHITE) ', '1000.000', '1000.000', '250.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(656, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '1.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(657, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(658, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(659, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(660, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(661, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(662, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(663, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(664, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(665, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(666, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(667, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(668, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(669, 'PLT8', 'active', 'TEFLON  (WHITE) ', '1200.000', '1200.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(670, 'PLT8', 'active', 'TORLON  4203   (YELLOW)', '300.000', '300.000', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(671, 'PLT8', 'active', 'TORLON  4203   (YELLOW)', '300.000', '300.000', '7.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(672, 'PLT8', 'active', 'TORLON  4203   (YELLOW)', '300.000', '300.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(673, 'PLT8', 'active', 'TORLON  4203   (YELLOW)', '300.000', '300.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(674, 'PLT8', 'active', 'TORLON  4203   (YELLOW)', '300.000', '300.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(675, 'PLT8', 'active', 'TORLON  4203   (YELLOW)', '300.000', '300.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(676, 'PLT8', 'active', 'TORLON  4203   (YELLOW)', '300.000', '300.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(677, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '6.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(678, 'ROD', 'active', 'PU (YELLOW) PLATE ', '300.000', '0.000', '8.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(679, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(680, 'ROD', 'active', 'PU (YELLOW) PLATE ', '300.000', '0.000', '12.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(681, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(682, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(683, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '25.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(684, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(685, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '35.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(686, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(687, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '45.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(688, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(689, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '55.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(690, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '60.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(691, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '65.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(692, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '70.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(693, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '75.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(694, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '80.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(695, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '85.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(696, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '90.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(697, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '100.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(698, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '110.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(699, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '120.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(700, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '130.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(701, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '140.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(702, 'ROD', 'active', 'PU (YELLOW) PLATE ', '500.000', '0.000', '150.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(703, 'PLT8', 'active', 'PU YELLOW', '1219.200', '2438.400', '2.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(704, 'PLT8', 'active', 'PU YELLOW', '1219.200', '2438.400', '3.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(705, 'PLT8', 'active', 'PU YELLOW', '1219.200', '2438.400', '5.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(706, 'PLT8', 'active', 'PU YELLOW', '1219.200', '2438.400', '10.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(707, 'PLT8', 'active', 'PU YELLOW', '1219.200', '2438.400', '15.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(708, 'PLT8', 'active', 'PU YELLOW', '1219.200', '2438.400', '20.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(709, 'PLT8', 'active', 'PU YELLOW', '1219.200', '2438.400', '30.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(710, 'PLT8', 'active', 'PU YELLOW', '1000.000', '1000.000', '40.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45'),
(711, 'PLT8', 'active', 'PU YELLOW', '1000.000', '1000.000', '50.000', '', 'mm', ' ', '0.00', '0.00', '0.00', '0.00', 0, 0, 0, 0, 0, '2020-02-27 01:23:45', '2020-02-27 01:23:45');

-- --------------------------------------------------------

--
-- Table structure for table `suppliers`
--

CREATE TABLE `suppliers` (
  `id` int(11) NOT NULL,
  `name` varchar(150) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `last_bill` int(11) NOT NULL,
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `supplier_ledgers`
--

CREATE TABLE `supplier_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `supplier_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` char(36) NOT NULL DEFAULT '',
  `user` varchar(10) DEFAULT NULL,
  `type` char(10) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `entity_type` char(10) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `ref_no` varchar(25) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `transaction_details`
--

CREATE TABLE `transaction_details` (
  `id` int(11) NOT NULL,
  `transaction_id` char(36) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `transaction_payments`
--

CREATE TABLE `transaction_payments` (
  `id` int(11) NOT NULL,
  `transaction_id` char(36) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `username` varchar(10) DEFAULT NULL,
  `password` varchar(150) DEFAULT NULL,
  `status` varchar(10) DEFAULT 'active',
  `type` varchar(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `username`, `password`, `status`, `type`) VALUES
(1, 'Admin', 'admin', 'admin', '827ccb0eea8a706c4c34a16891f84e7b', 'active', 'admin'),
(2, 'Clerk', 'Clerk', 'clerk', 'd5149ff36488f4afbf4e299cb148fa1d', 'active', 'staff'),
(3, 'Ynah', 'Caponpon', '', '88344f7319a42946829ceee26a454bc2', 'active', 'staff'),
(4, 'Cindy', 'Calompad', '', '528e46a711df9f45dfe8b43163f34772', 'active', 'staff'),
(5, 'Juan', 'Dela Cruz', 'juan', '560be8dc3a54da82f550b1891b8c2fdf', 'active', 'staff'),
(6, 'Jane', 'Dela Cruz', 'jane', '91a7d673725c855dfa508df0b11d5dc4', 'active', 'staff'),
(7, 'Noel', 'Calinagan', '', '1148102789ec783ccf6db99710163173', 'active', 'staff'),
(8, 'Roi', 'BASAYA', 'Roi', '827ccb0eea8a706c4c34a16891f84e7b', 'active', 'staff'),
(9, 'Johnny', 'Amahan', 'johnny', '827ccb0eea8a706c4c34a16891f84e7b', 'active', 'staff'),
(10, 'abegail', 'dohenias', '', '827ccb0eea8a706c4c34a16891f84e7b', 'active', 'staff'),
(11, NULL, NULL, 'hardin', 'd5149ff36488f4afbf4e299cb148fa1d', 'active', 'staff');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customer_ledgers`
--
ALTER TABLE `customer_ledgers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `deliveries`
--
ALTER TABLE `deliveries`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `delivery_details`
--
ALTER TABLE `delivery_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `delivery_payments`
--
ALTER TABLE `delivery_payments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `inventory_adjustments`
--
ALTER TABLE `inventory_adjustments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `inventory_logs`
--
ALTER TABLE `inventory_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoice_details`
--
ALTER TABLE `invoice_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoice_payments`
--
ALTER TABLE `invoice_payments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `modules`
--
ALTER TABLE `modules`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `module_users`
--
ALTER TABLE `module_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `order_details`
--
ALTER TABLE `order_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `price_logs`
--
ALTER TABLE `price_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `suppliers`
--
ALTER TABLE `suppliers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `supplier_ledgers`
--
ALTER TABLE `supplier_ledgers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transaction_details`
--
ALTER TABLE `transaction_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transaction_payments`
--
ALTER TABLE `transaction_payments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `deliveries`
--
ALTER TABLE `deliveries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `delivery_details`
--
ALTER TABLE `delivery_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `delivery_payments`
--
ALTER TABLE `delivery_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_adjustments`
--
ALTER TABLE `inventory_adjustments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `inventory_logs`
--
ALTER TABLE `inventory_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `invoice_details`
--
ALTER TABLE `invoice_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `invoice_payments`
--
ALTER TABLE `invoice_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `order_details`
--
ALTER TABLE `order_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `price_logs`
--
ALTER TABLE `price_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=712;

--
-- AUTO_INCREMENT for table `suppliers`
--
ALTER TABLE `suppliers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `transaction_details`
--
ALTER TABLE `transaction_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `transaction_payments`
--
ALTER TABLE `transaction_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
