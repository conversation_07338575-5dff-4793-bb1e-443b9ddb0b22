-- MySQL dump 10.16  Distrib 10.1.13-MariaD<PERSON>, for Win32 (AMD64)
--
-- Host: 127.0.0.1    Database: inventta_210508
-- ------------------------------------------------------
-- Server version	10.1.13-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `cash_flows`
--

DROP TABLE IF EXISTS `cash_flows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cash_flows` (
  `id` char(36) NOT NULL,
  `particulars` varchar(25) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `flag` char(1) NOT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cash_flows`
--

LOCK TABLES `cash_flows` WRITE;
/*!40000 ALTER TABLE `cash_flows` DISABLE KEYS */;
INSERT INTO `cash_flows` VALUES ('60b5b269-40b4-477f-b4c1-320cc0a8006a','sales-6',500.00,'c','2021-06-01 12:07:05','2021-06-01 12:07:05','2021-06-01 12:07:05'),('60b5b501-1a80-455b-b072-320c82e7a674','sales-7',500.00,'c','2021-06-01 12:18:09','2021-06-01 12:18:09','2021-06-01 12:18:09'),('60b5b50b-d258-45d6-9d3e-320cc0a8006a','sales-8',1540.00,'c','2021-06-01 12:18:19','2021-06-01 12:18:19','2021-06-01 12:18:19'),('60b5d1c9-c918-468e-9888-320cc0a8006a','sales-12',155.00,'c','2021-06-01 14:20:57','2021-06-01 14:20:57','2021-06-01 14:20:57'),('60b60580-e408-498e-879d-320cc0a8006a','sales-13',880.00,'c','2021-06-01 18:01:36','2021-06-01 18:01:36','2021-06-01 18:01:36'),('60b6e737-4f78-47ab-be89-3318c0a8006a','sales-18',8700.00,'c','2021-06-02 10:04:39','2021-06-02 10:04:39','2021-06-02 10:04:39'),('60b6f885-73ec-417d-991c-3318c0a8006a','sales-21',130.00,'c','2021-06-02 11:18:29','2021-06-02 11:18:29','2021-06-02 11:18:29'),('60b700d7-12f0-4257-928d-3318c0a8006a','sales-22',435.00,'c','2021-06-02 11:53:59','2021-06-02 11:53:59','2021-06-02 11:53:59');
/*!40000 ALTER TABLE `cash_flows` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `id` char(4) NOT NULL,
  `name` varchar(30) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `categories`
--

LOCK TABLES `categories` WRITE;
/*!40000 ALTER TABLE `categories` DISABLE KEYS */;
/*!40000 ALTER TABLE `categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customer_ledgers`
--

DROP TABLE IF EXISTS `customer_ledgers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `customer_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer_ledgers`
--

LOCK TABLES `customer_ledgers` WRITE;
/*!40000 ALTER TABLE `customer_ledgers` DISABLE KEYS */;
INSERT INTO `customer_ledgers` VALUES ('6096455f-d748-4255-a4dc-02dcc0a80105',3,'SI-1245','30',1500.00,'c','2021-05-08 16:01:35','2021-05-08 16:01:35','2021-05-08 16:01:35'),('60ac84dd-de48-41fd-82c9-2fb482e7a674',135,'SI-123','30',415.00,'c','2021-05-25 13:02:20','2021-05-25 13:02:21','2021-05-25 13:02:21'),('60ac85df-e1ec-4789-b9e6-2fb4c0a8006a',6,'SI-SI-1223','30',1000.00,'c','2021-05-25 13:06:39','2021-05-25 13:06:39','2021-05-25 13:06:39'),('60ac98cd-43d0-4c55-ae73-2fb4c0a8006a',87,'SI-SI-4532','30',425.00,'c','2021-05-25 14:27:25','2021-05-25 14:27:25','2021-05-25 14:27:25'),('60ac9cb3-0c58-4ff8-9b0c-2fb4c0a8006a',87,'SI-SI-1535','30',700.00,'c','2021-05-25 14:44:03','2021-05-25 14:44:03','2021-05-25 14:44:03'),('60b5ba15-3b9c-4ce4-9e29-320cc0a8006a',106,'SI-SI-4785','30',640.00,'c','2021-06-01 12:39:49','2021-06-01 12:39:49','2021-06-01 12:39:49'),('60b5bc43-8fd8-418c-8373-320cc0a8006a',83,'SI-SI-4786','30',840.00,'c','2021-06-01 12:49:07','2021-06-01 12:49:07','2021-06-01 12:49:07'),('60b5cac8-3adc-41df-b8f2-320cc0a8006a',7,'SI-SI-4787','30',1260.00,'c','2021-06-01 13:51:04','2021-06-01 13:51:04','2021-06-01 13:51:04'),('60b6dfe3-0ee8-4194-9fc4-3318c0a8006a',124,'SI-SI-4789','30',2075.00,'c','2021-06-02 09:33:23','2021-06-02 09:33:23','2021-06-02 09:33:23'),('60b6e0e3-b430-498b-b72c-3318c0a8006a',31,'SI-SI-4790','30',2480.00,'c','2021-06-02 09:37:39','2021-06-02 09:37:39','2021-06-02 09:37:39'),('60b6e1fe-5d88-45f5-bd7c-3318c0a8006a',146,'SI-SI-4791','30',4910.00,'c','2021-06-02 09:42:22','2021-06-02 09:42:22','2021-06-02 09:42:22'),('60b6e495-71a0-4ad3-b7fa-3318c0a8006a',147,'SI-SI-4792','30',12880.00,'c','2021-06-02 09:53:25','2021-06-02 09:53:25','2021-06-02 09:53:25'),('60b6e87c-45ec-4369-aaef-3318c0a8006a',49,'SI-SI-4794','30',1290.00,'c','2021-06-02 10:10:04','2021-06-02 10:10:04','2021-06-02 10:10:04'),('60b6f72d-b9e8-460f-8161-3318c0a8006a',12,'SI-SI-4795','30',3745.00,'c','2021-06-02 11:12:45','2021-06-02 11:12:45','2021-06-02 11:12:45'),('60b701a8-45d0-4936-9873-3318c0a8006a',4,'SI-SI-4796','30',9150.00,'c','2021-06-02 11:57:27','2021-06-02 11:57:28','2021-06-02 11:57:28');
/*!40000 ALTER TABLE `customer_ledgers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) DEFAULT NULL,
  `alias` varchar(20) NOT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `tin` varchar(20) NOT NULL,
  `address` varchar(150) NOT NULL,
  `business_style` varchar(150) NOT NULL,
  `tax_type` char(3) NOT NULL COMMENT 'VAT - VATable, ZRO -  Non-Vat',
  `unit_system` char(3) NOT NULL COMMENT 'ENG - English, MET - Metric',
  `last_bill` int(11) DEFAULT '0',
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES (1,'CASH','CASH','open','','','','','',0,0.00,0.00,NULL,'2021-05-07 14:14:05'),(2,'Customer A','C.A.','open','1232342','Sto Tomas Batangas','Construction','ZRO','ENG',0,0.00,0.00,'2021-02-27 22:39:20','2021-05-08 07:37:07'),(3,'Raitech','Raitech','open','12344','Cabuyao Laguna','Engineering Services','VAT','MET',0,0.00,0.00,'2021-05-07 14:14:37','2021-05-08 10:44:33'),(4,'Lark Seimitsu Tooling Corporation','LARK','open','010-042-009-000','Canlalay, Biñan, Laguna','Lark Seimitsu Tooling Corporation','VAT','MET',0,0.00,0.00,'2021-05-08 12:16:34','2021-05-08 12:17:16'),(5,'AAT Technologies Trading & Fabrication Corp.','AAT','open','009-691-930-000','Unit 704 The infinity Tower 26th St, BGC, Taguig','AAT Technologies Trading & Fabrication Corp.','VAT','MET',0,0.00,0.00,'2021-05-14 11:47:43','2021-05-14 11:47:43'),(6,'Accura Mechanical Parts & Components Corp.','ACCURA','open','009-559-899-000','Brgy. Biga 1, Silang, Cavite','Accura Mechanical Parts & Components Corp.','VAT','MET',0,0.00,0.00,'2021-05-14 11:48:53','2021-05-14 11:48:53'),(7,'Adtech Metal Precision','ADTECH','open','008-802-456-000','Mayapa, Calamba City, Laguna','Adtech Metal Precision','VAT','MET',0,0.00,0.00,'2021-05-14 11:52:41','2021-05-14 11:52:41'),(8,'EAN PRECICSION SERVICES','EAN','open','***********-000','Unit 6 Topaz Bldg. Metrogate Estate Sampalo 1 Dasma City, Cavite','EAN PRECICSION SERVICES','VAT','MET',0,0.00,0.00,'2021-05-14 11:53:42','2021-05-14 11:53:42'),(9,'Aim Tooling Technologies & Metal Fabrication','AIM TOOLING','open','***********-000','San Pedro, Laguna','Aim Tooling Technologies & Metal Fabrication','VAT','MET',0,0.00,0.00,'2021-05-14 11:55:29','2021-05-14 11:55:29'),(10,'Almendrala Industrial Services Company','AISCO','open','***********-000','San Vicente, San Pedro, Laguna','Almendrala Industrial Services Company','VAT','MET',0,0.00,0.00,'2021-05-14 11:56:31','2021-05-14 11:56:31'),(11,'AJO Fabrication & Mdse Inc.','AJO','open','***********-000','Gov\'s Drive, Bancal, Carmona, Cavite','AJO Fabrication & Mdse Inc.','VAT','MET',0,0.00,0.00,'2021-05-14 11:57:24','2021-05-14 11:57:24'),(12,'Alevro Precision Tooling Services','ALEVRO','open','***********-000','Sitio Tamak, Brgy. Munting Ilog, Silang, Cavite','Alevro Precision Tooling Services','VAT','MET',0,0.00,0.00,'2021-05-14 11:58:25','2021-05-14 11:58:25'),(13,'Alliance Precision','ALLIANCE','open','***********-000','Magat St. Brgy. Sauyo, Quezon City','Alliance Precision','VAT','MET',0,0.00,0.00,'2021-05-14 12:02:39','2021-05-14 12:02:39'),(14,'Amicitia Technologies Corporation','AMICITIA','open','***********-000','Brgy. Malitlit, Sta. Rosa, Laguna','Amicitia Technologies Corporation','VAT','MET',0,0.00,0.00,'2021-05-14 12:03:35','2021-05-14 12:03:35'),(15,'Atom Tooling Technology Inc,','ATOM','open','***********-000','Bancal, Carmona, Cavite','Atom Tooling Technology Inc,','','',0,0.00,0.00,'2021-05-14 12:04:15','2021-05-14 12:04:15'),(16,'Avesco Precision Tools & Eng\'g  Services','AVESCO','open','138-307-768-000','Darasa, Tanauan City, Batangas','Avesco Precision Tools & Eng\'g  Services','VAT','MET',0,0.00,0.00,'2021-05-14 12:05:09','2021-05-14 12:05:09'),(17,'Bending Master Metal Works & Fabrication','BENDING','open','285-605-380-001','Carmona, Cavite','Bending Master Metal Works & Fabrication','VAT','MET',0,0.00,0.00,'2021-05-14 12:06:36','2021-05-14 12:06:46'),(18,'Bernatech Precision Toolings & Ind\'l Services','BERNATECH','open','262-903-314-000','Banlic, Cabuyao, Laguna','Bernatech Precision Toolings & Ind\'l Services','VAT','MET',0,0.00,0.00,'2021-05-14 12:07:35','2021-05-14 12:07:35'),(19,'Bigears Solutions, Inc.','BIGEARS','open','009-168-916-000','Malagasang, Imus, Cavite','Bigears Solutions, Inc.','VAT','MET',0,0.00,0.00,'2021-05-14 12:08:19','2021-05-14 12:08:19'),(20,'Bigmind Engineering And Industrial Supply Co.','BIGMIND','open','009-392-045-000','Banlic, Cabuyao, Laguna','Bigmind Engineering And Industrial Supply Co.','VAT','MET',0,0.00,0.00,'2021-05-14 12:09:05','2021-05-14 12:09:05'),(21,'Bipol Tech Steel Fabrication','BIPOL','open','140-130-276-000','Mayapa, Calamba, Laguna','Bipol Tech Steel Fabrication','VAT','MET',0,0.00,0.00,'2021-05-14 12:10:28','2021-05-14 12:10:28'),(22,'BLH Technical Services','BLH','open','176-291-608-000','104 VIñalon St, Cupang Muntinlupa City','BLH Technical Services','VAT','MET',0,0.00,0.00,'2021-05-14 12:20:50','2021-05-14 12:20:50'),(23,'Brairwin Technology','BRAIRWIN','open','209-262-883-000','Sabang Hills Compound Mabini Ext. #8  Brgy 5A San Pablo City','Brairwin Technology','VAT','MET',0,0.00,0.00,'2021-05-14 12:21:48','2021-05-14 12:21:48'),(24,'Caiquin Machine Shop','CAIQUIN','open','255-885-876-000','Tirona Road Brgy. San Agustin 2 Dasma Cavite,','Caiquin Machine Shop','VAT','MET',0,0.00,0.00,'2021-05-14 12:22:37','2021-05-14 12:22:51'),(25,'Cals Tech Industrial Supply','CALSTECH','open','232-677-162-000','Arabella Commercial, Pulo, Cabuyao, Laguna','Cals Tech Industrial Supply','VAT','MET',0,0.00,0.00,'2021-05-14 12:23:36','2021-05-14 12:23:36'),(26,'Cebu Yushin, Inc.','C.YUSHIN','open','241-037-791-000','Sabang, Cebu City','Cebu Yushin, Inc.','ZRO','MET',0,0.00,0.00,'2021-05-14 12:24:24','2021-05-14 12:24:24'),(27,'Cerehe Engineering Works','CEREHE','open','009-691-211-000','Blk 8 Lot 5 Tartaria Road Brgy. Tartaria Siliang Cavite','Cerehe Engineering Works','VAT','MET',0,0.00,0.00,'2021-05-14 12:25:05','2021-05-14 12:25:05'),(28,'CJC Trading & Engineering Services','CJC','open','***********-000','Silang, Cavite','CJC Trading & Engineering Services','VAT','MET',0,0.00,0.00,'2021-05-14 12:26:06','2021-05-14 12:26:06'),(29,'Cosreq Metal Fabrication and Industrial Supply','COSREQ','open','187-648-719-000','P1 Brgy. Mamplasan, Biñan Laguna','Cosreq Metal Fabrication and Industrial Supply','VAT','MET',0,0.00,0.00,'2021-05-14 12:26:51','2021-05-14 12:26:57'),(30,'Cresttech Engineering Supply','CRESTTECH','open','314-859-265-000','Hongkong Village Banay Banay City Of Cabuyao, Laguna','Cresttech Engineering Supply','VAT','MET',0,0.00,0.00,'2021-05-14 12:27:52','2021-05-14 12:27:52'),(31,'Critomo Precision And Industrial Services','CRITOMO','open','242-203-492-000','Mamatid Rd. Cabuyao, Laguna','Critomo Precision And Industrial Services','VAT','MET',0,0.00,0.00,'2021-05-14 13:03:16','2021-05-14 13:03:16'),(32,'Crutech And Tooling Precision','CRUTECH','open','131-204-190-000','Brgy. Sala, Cabuyao, Laguna','Crutech And Tooling Precision','VAT','MET',0,0.00,0.00,'2021-05-14 13:04:02','2021-05-14 13:04:02'),(33,'Dae Sung Precision Tech., Inc.','DAESUNG','open','009-855-370-000','San Vicente, San Pedro, Laguna','Dae Sung Precision Tech., Inc.','VAT','MET',0,0.00,0.00,'2021-05-14 13:15:49','2021-05-14 13:15:49'),(34,'DJEMS ACCUMETAL','DJEMS','open','','San Miguel Sto. Tomas Batangas','DJEMS ACCUMETAL','VAT','MET',0,0.00,0.00,'2021-05-14 13:42:35','2021-05-14 13:42:35'),(35,'DMK Technology','DMK','open','DMK Technology','B74 L5 Riverside, San Pedro, Laguna','DMK Technology','VAT','MET',0,0.00,0.00,'2021-05-14 13:43:29','2021-05-14 13:43:29'),(36,'DNY Tech Precision Tooling & Eng\'g Works','DNY','open','910-302-099-000','910-302-099-000','DNY Tech Precision Tooling & Eng\'g Works','VAT','MET',0,0.00,0.00,'2021-05-14 13:44:18','2021-05-14 13:44:18'),(37,'Domson Trading','DOMSON','open','161-325-409-000','Marquez Ave. Salitran 1, Cavite','Domson Trading','VAT','MET',0,0.00,0.00,'2021-05-14 13:44:56','2021-05-14 13:44:56'),(38,'Donglan Trading Eng\'g Services','DONGLAN','open','111-801-933-000','Sta. Rosa, Laguna','Donglan Trading Eng\'g Services','VAT','MET',0,0.00,0.00,'2021-05-14 13:48:11','2021-05-14 13:48:11'),(39,'DONYLTECH IND\'L AND ENGINEERING SERV.','DONYLTECH','open','113-913-310-000','0068 BRGY SIRANG LUPA CALAMBA LAGUNA','DONYLTECH IND\'L AND ENGINEERING SERV.','VAT','MET',0,0.00,0.00,'2021-05-14 13:48:54','2021-05-14 13:48:54'),(40,'Dynamic Int\'l Precision & Tooling Inc.','DYNAMIC','open','008-855-170-000','Salawag, Dasmariñas, Cavite','Dynamic Int\'l Precision & Tooling Inc.','VAT','MET',0,0.00,0.00,'2021-05-14 13:49:44','2021-05-14 13:49:44'),(41,'Dynamicro Precision Toolings & Fab Inc.','DYNAMICRO','open','006-515-435-000','Emilio Aguinaldo, Silang, Cavite','Dynamicro Precision Toolings & Fab Inc.','VAT','MET',0,0.00,0.00,'2021-05-14 14:02:31','2021-05-14 14:02:31'),(42,'EETECH Innovation Trading and Electronic Services','EETECH','open','299-384-840-000','BLK11 LOT30 Vera Cruz Homes, Bgry. Malitlit Sta Rosa Laguna','EETECH Innovation Trading and Electronic Services','VAT','MET',0,0.00,0.00,'2021-05-14 14:03:19','2021-05-14 14:03:19'),(43,'EGV Primetech Ventures','EGV','open','242-596-233-000','Bancal, Carmona, Cavite','EGV Primetech Ventures','VAT','MET',0,0.00,0.00,'2021-05-14 14:16:57','2021-05-14 14:16:57'),(44,'EJJ INDUSTRIAL SUPPLY','EJJ','open','701-668-526-000','Uruguay Drive, FranceVille Subd. Bulihan Silang, Cavite','EJJ INDUSTRIAL SUPPLY','VAT','MET',0,0.00,0.00,'2021-05-14 14:17:38','2021-05-14 14:17:38'),(45,'ELF Precision Tooling Services','ELF','open','907-430-735-000','Pacita Complex, San Pedro, Laguna','ELF Precision Tooling Services','VAT','MET',0,0.00,0.00,'2021-05-14 14:18:15','2021-05-14 14:18:15'),(46,'Efficient Maschinentechniks Inc.','EMAC','open','247-841-461-001','Mayon Mt View Ind\'l Complex, Carmona, Cavite','Efficient Maschinentechniks Inc.','VAT','MET',0,0.00,0.00,'2021-05-15 13:11:58','2021-05-15 13:11:58'),(47,'EMC Machine Works','EMC','open','246-374-575-000','Bancal, Carmona, Cavite','EMC Machine Works','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:22:39','2021-05-19 10:22:39'),(48,'Enopre Machine Shop & Engineering Works & Trading','ENOPRE','open','215-741-367-000','GATXUWWYDFFHN4SK64F6H3X6UVUCRGMR6BXJ4JAPT2MMG5QI5VRQLQNE','Enopre Machine Shop & Engineering Works & Trading','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:23:34','2021-05-19 10:23:34'),(49,'ESJ Precision Toolings','ESJ','open','179-213-419-001','4731 National Highway, Brgy, Macabling Sta Rosa, Laguna','ESJ Precision Toolings','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:24:21','2021-05-19 10:24:21'),(50,'Optimus Defence & Sports Enterprices','OPTIMUS','open','457-024-276-000','PUROK 2 Brgy Sampaguita Lipa.','Optimus Defence & Sports Enterprices','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:26:32','2021-05-19 10:26:32'),(51,'Esther Technologies Eng\'g Supply & Services','ESTHERTECH','open','305-735-802-000','Gulod, Cabuyao, Laguna','Esther Technologies Eng\'g Supply & Services','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:27:52','2021-05-19 10:27:52'),(52,'Ficatech Precision Metal Fabrication','FICATECH','open','901-660-344-000','Mabuhay, Mamatid, Cabuyao, Laguna','Ficatech Precision Metal Fabrication','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:41:55','2021-05-19 10:41:55'),(53,'Friendworks Engineering Technology & Merchandise','FRIENDSWORKS','open','301-951-910-000','Batino, Calamba, Laguna','Friendworks Engineering Technology & Merchandise','ZRO','MET',NULL,0.00,0.00,'2021-05-19 10:42:50','2021-05-19 10:42:50'),(54,'Fuji Industries Manila Corporation','FUJI','open','006 -033-096-000','FPIP, Sepz, Sta Anastacia, Sto. Tomas, Batangas','Fuji Industries Manila Corporation','ZRO','MET',NULL,0.00,0.00,'2021-05-19 10:43:32','2021-05-19 10:43:32'),(55,'FULITECH','FULITECH','open','007-187-937-000','Lower Ground Flr. Brusmick Estates Balibago Rd. Sta Tosa Laguna','FULITECH','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:44:26','2021-05-19 10:44:26'),(56,'FULLERTECH','FULLERTECH','open','009-215-266-000','Hi-Lon Compound Mayapa Calamba, Laguna','FULLERTECH','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:45:14','2021-05-19 10:45:14'),(57,'G-12 Prime Source Inc.,','G-12','open','','People\'s Technology Complex Special Eco., Zone Carmona Cavite','G-12 Prime Source Inc.,','ZRO','MET',NULL,0.00,0.00,'2021-05-19 10:51:35','2021-05-19 10:51:35'),(58,'Gate 24 Manufacturing Co.','GATE-24','open','010-471-696-000','Unit Emmanual SJB Compound Brgy. San Isidro, Calamba Laguna','Gate 24 Manufacturing Co.','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:57:42','2021-05-19 10:57:42'),(59,'Gelsen Enterprises','GELSEN','open','904-051-392-000','Sta. Rosa, Laguna','Gelsen Enterprises','VAT','MET',NULL,0.00,0.00,'2021-05-19 10:58:56','2021-05-19 10:58:56'),(60,'GJ Precision Technology, Inc.','GJ PRECISION','open','008-424-305-000','Liip, Mamplasan, Biñan, Laguna','GJ Precision Technology, Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-19 11:00:11','2021-05-19 11:00:55'),(61,'GREATECH PHILIPPINNES, INC.','GREATECH','open','005-646-029-000','(FCIE) 4126, Brgy. Langkaan, Dasmarias, Cavite, Philippines','GREATECH PHILIPPINNES, INC.','ZRO','MET',NULL,0.00,0.00,'2021-05-19 11:14:21','2021-05-19 11:14:21'),(62,'GRT Corporation','GRT','open','010-044-411-000','Maguyam, Silang, Cavite','GRT Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:00:47','2021-05-19 15:00:47'),(63,'GST TECHNOLOGY, INC.','GST','open','010-099-342-000','5 Laguna Techno Park Ph7 Poblacion, City of Biñan, Laguna','GST TECHNOLOGY, INC.','ZRO','MET',NULL,0.00,0.00,'2021-05-19 15:01:30','2021-05-19 15:03:23'),(64,'Hantverk Technologies','HANTVERK','open','209-892-862-000','Sala, Cabuyao, Laguna','Hantverk Technologies','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:02:20','2021-05-19 15:02:20'),(65,'H-Five Metal Fabrication & Industrial Supplies','H-FIVE','open','166-129-927-000','B1 L 143 Congressional Rd Brgy. H2, Dasmariñas, Cavite','H-Five Metal Fabrication & Industrial Supplies','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:03:14','2021-05-19 15:03:14'),(66,'Hi-Rtech Eng\'g Supply Services','HI-RTECH','open','301-131-283-000','Majada Out, Canlubang, Calamba, Laguna','Hi-Rtech Eng\'g Supply Services','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:04:27','2021-05-19 15:04:27'),(67,'Hisynergy Enterprises','HISYNERGY','open','156-942-722-000','Spring Ville Exec 2 Molino 4, Bacoor, Cavite','Hisynergy Enterprises','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:06:01','2021-05-19 15:08:31'),(68,'Hongtai Technology, Inc.','HONGTAI','open','008-832-867-000','Carmelray 2 Brgy. Punta, Calamba, Laguna','Hongtai Technology, Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-19 15:07:29','2021-05-19 15:07:29'),(69,'Index, Inc.','INDEX','open','206-324-486-000','San Benito, Alaminos, Laguna','Index, Inc.','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:08:25','2021-05-19 15:08:25'),(70,'Iomni Precision Inc.','I-OMNI','open','207-458-959-000','LISP 2, Brgy. La Mesa, Calamba City','Iomni Precision Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-19 15:09:41','2021-05-19 15:09:41'),(71,'IXUS Technology and Industries Inc.','IXUS','open','009-891-653-000','149 Sambat Tanauan City, Batangas','IXUS Technology and Industries Inc.','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:13:12','2021-05-19 15:13:12'),(72,'JABEZTECH CORPORATION','JABEZTECH','open','009-594-548-000','Carmona Cavite','JABEZTECH CORPORATION','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:31:47','2021-05-19 15:31:47'),(73,'Jademicron Philippines, Inc.','JADEMICRON','open','008-027-086-000','2F Bldg 3, Fastech Mfg Complex, LISP I, Cabuyao, Laguna','Jademicron Philippines, Inc.','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:32:47','2021-05-19 15:32:47'),(74,'TOP JAO TECH TECHNOLOGY INC','JAOTECH','open','009-316-584-000','TANAUAN CITY BATANGAS','TOP JAO TECH TECHNOLOGY INC','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:34:04','2021-05-19 16:29:37'),(75,'Java-Tech Ind\'l Supply Eng\'g Services','JAVATECH','open','215-709-663-000','Purok 6, Brgy Lawa, Calamba, Laguna','Java-Tech Ind\'l Supply Eng\'g Services','VAT','MET',NULL,0.00,0.00,'2021-05-19 15:41:22','2021-05-19 15:41:22'),(76,'Michen Toolings','MICHEN','open','000-057-936-000','504 Halcon St. Boni Ave, Mandaluyong City','Michen Toolings','VAT','MET',NULL,0.00,0.00,'2021-05-19 16:17:20','2021-05-19 16:17:20'),(77,'Midtown Mfg. Philippines, Inc.','MIDTOWN','open','006-747-803-000','Batino, Calamba, Laguna','Midtown Mfg. Philippines, Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-19 16:18:54','2021-05-19 16:18:54'),(78,'Millenium Toolings & Fabrication Services Inc.','MILLENIUM','open','009-888-063-000','Paciano, Calamba, Laguna','Millenium Toolings & Fabrication Services Inc.','VAT','MET',NULL,0.00,0.00,'2021-05-19 16:19:42','2021-05-19 16:19:42'),(79,'MMRA Engineering Services','MMRA','open','257-719-232-000','Brgy. Pulo, City  of Laguna','MMRA Engineering Services','VAT','MET',NULL,0.00,0.00,'2021-05-19 16:27:56','2021-05-19 16:27:56'),(80,'Micro-Mechanics Technology Internatinal Inc.','MICRO-MICHANICS','open','207-182-717-000','LOTB2-1C Carmelray Industrial Park 2 Brgy. Tulo Calamba Laguna','Micro-Mechanics Technology Internatinal Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-19 16:29:19','2021-05-19 16:29:19'),(81,'Mondabel Machine Shop','MONDABEL','open','256-428-035-000','Calamba, Laguna','Mondabel Machine Shop','VAT','MET',NULL,0.00,0.00,'2021-05-19 16:31:12','2021-05-19 16:31:12'),(82,'MAM Precision Tooling','MAM','open','146-205-675-000','#658 National Hiway Brgy. Nueva San Pedro Laaguna','MAM Precision Tooling','VAT','MET',NULL,0.00,0.00,'2021-05-19 16:44:14','2021-05-19 16:44:14'),(83,'Maretech Precision Toolings & Industrial Services','MARETECH','open','222-854-762-000','Pulo- Diezmo Rd, Pulo, Cabuyao, Laguna','Maretech Precision Toolings & Industrial Services','VAT','MET',NULL,0.00,0.00,'2021-05-19 16:45:04','2021-05-19 16:45:04'),(84,'Markkos Stainless Steel Trading','MARKKOS','open','240-256-119-000','Brgy. Darasa Tanauan Batangas','Markkos Stainless Steel Trading','VAT','MET',NULL,0.00,0.00,'2021-05-20 15:12:35','2021-05-20 15:12:35'),(85,'Maruano Enterprises','MARUANO','open','151-456-500-000','Dasmariñas Cavite','Maruano Enterprises','VAT','MET',NULL,0.00,0.00,'2021-05-20 15:13:15','2021-05-20 15:13:15'),(86,'Mechanical Component Manufacturing','MCM','open','010-301-574-000','Maguyam 7 Silang Cavite','Mechanical Component Manufacturing','VAT','MET',NULL,0.00,0.00,'2021-05-20 15:13:55','2021-05-20 15:13:55'),(87,'Meraki-Tech Precision Corporation','MERAKI','open','009-560-388-000','Bancal, Carmona, Cavite','Meraki-Tech Precision Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-20 15:15:15','2021-05-25 15:04:24'),(88,'MTE TECHNOLOGY  INC.','MTE','open','203-803-658-000','Calmelry 11 Calamba Laguna, Philippines 4027','MTE TECHNOLOGY  INC.','ZRO','MET',NULL,0.00,0.00,'2021-05-20 15:15:56','2021-05-20 15:23:56'),(89,'M Nobora Electromechanical Services','M.NOBORA','open','246-229-585-000','Platinum Commercial Bldg., Brgy. Sampaloc I, Dasmariñas City, Cavite','M Nobora Electromechanical Services','VAT','MET',NULL,0.00,0.00,'2021-05-20 15:17:51','2021-05-20 15:17:51'),(90,'NAB  Engineering Services','NAB','open','902-910-055-000','B64 L38 P1C San Lorenzo South','NAB  Engineering Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:06:55','2021-05-21 10:06:55'),(91,'NE Machine Shop','NE MACHINE','open','128-602-338-000','Calamba Laguna','NE Machine Shop','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:09:22','2021-05-21 10:09:22'),(92,'New Sun Machining','NEWSUN','open','910-203-489-000','San Pioquinto, Malvar, Batangas','New Sun Machining','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:10:22','2021-05-21 10:10:22'),(93,'NSB Engineering Services','NSB','open','165-226-163-000','Bicutan Taguig City','NSB Engineering Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:10:56','2021-05-21 10:10:56'),(94,'NW1 Construction & Fabrication Services','NW1','open','263-297-414-000','Salawag, Dasmariñas, Cavite','NW1 Construction & Fabrication Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:13:59','2021-05-21 10:13:59'),(95,'Obiena Enterprises Co.','OBIENA','open','008-203-778-000','Sampaloc 2, Dasmariñas, Cavite','Obiena Enterprises Co.','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:14:45','2021-05-21 10:14:45'),(96,'Ohsung Precision Philippines, Inc.','OHSUNG','open','007-196-115-000','San Pedro, Laguna','Ohsung Precision Philippines, Inc.','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:15:52','2021-05-21 10:15:52'),(97,'Orbis Precision Tech','ORBIS','open','007-854-873-000','LIIP, Mamplasan, Biñan, Laguna','Orbis Precision Tech','ZRO','MET',NULL,0.00,0.00,'2021-05-21 10:16:59','2021-05-21 10:16:59'),(98,'Paatronix Technology Corp','PAATRONIX','open','241-776-221-000','Unit C, RLI bdg3, Sount Point Subd., Cabuyao Laguna','Paatronix Technology Corp','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:33:35','2021-05-21 10:33:35'),(99,'Primateknica Corporation','PRIMATEKNICA','open','281-641-717-000','Veterans Center, Western Bicutan, Taguig City','Primateknica Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:43:53','2021-05-21 10:43:53'),(100,'Prov3 Toolings','PROV3','open','007-073-485-000','Balibago, Sta. Rosa, Laguna','Prov3 Toolings','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:53:10','2021-05-21 10:53:10'),(101,'PAYTON Spring and Industrial Services Corp.','PAYTON','open','208-584-004-000','0130 Purok 4, Saimsim, Calamba City, Laguna','PAYTON Spring and Industrial Services Corp.','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:54:48','2021-05-21 10:54:48'),(102,'RA Technologies Corp.','RA TECH','open','006-914-055-000','Mabuhay, Carmona, Cavite','RA Technologies Corp.','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:55:37','2021-05-21 10:55:37'),(103,'Rads Tooling & Precision Services','RADS','open','130-578-652-000','358 Piela Sampaloc 3, Dasmariñas, Cavite','Rads Tooling & Precision Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:56:25','2021-05-21 10:56:25'),(104,'RADical  Steel Technology','RADICAL','open','138-203-376-000','GATXUWWYDFFHN4SK64F6H3X6UVUCRGMR6BXJ4JAPT2MMG5QI5VRQLQNE','RADical  Steel Technology','VAT','MET',NULL,0.00,0.00,'2021-05-21 10:57:08','2021-05-21 10:57:08'),(105,'RF Maru Industrial Design','RF MARU','open','***********-000','Brgy. Malitlit, Sta. Rosa, Laguna','RF Maru Industrial Design','VAT','MET',NULL,0.00,0.00,'2021-05-21 14:54:28','2021-05-21 14:54:28'),(106,'RGG Stainless Steel & Metal Fabrication','RGG','open','***********-000','Landayan, San Pedro, Laguna','RGG Stainless Steel & Metal Fabrication','VAT','MET',NULL,0.00,0.00,'2021-05-21 14:55:20','2021-05-21 14:55:20'),(107,'Richtech Industrial Supply Company','RICHTECH','open','***********-000','Parian, Calamba, Laguna','Richtech Industrial Supply Company','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:00:57','2021-05-21 15:00:57'),(108,'RPT Fabrication and Insulation Services','RPT','open','***********-000','#71 Purok 2, Majada Out, Calamba City Laguna','RPT Fabrication and Insulation Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:01:49','2021-05-21 15:01:49'),(109,'RTS Tool Maker','RTS','open','***********-000','Brgy.San Pioking Malvar Batangas','RTS Tool Maker','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:02:47','2021-05-21 15:02:47'),(110,'Saint Steve Enterprises','ST,STEVE','open','***********-000','National Hi-way, Sta. Rosa, Laguna','Saint Steve Enterprises','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:06:08','2021-05-21 15:06:08'),(111,'Sams Precision Eng\'g & Construction Services','CJC','open','***********-000','Silang, Cavite','Sams Precision Eng\'g & Construction Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:07:07','2021-05-21 15:07:07'),(112,'Simcru Industrial Supply','SIMCRU','open','***********-000','San Andres, Alaminos, Laguna','Simcru Industrial Supply','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:22:28','2021-05-21 15:35:25'),(113,'Silver Jem Corporation','SILVER JEM','open','008-678-829-000','Cabuyao, Laguna','Silver Jem Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:24:10','2021-05-21 15:58:10'),(114,'Sigmamet Precision Corp.','SIGMAMET','open','008-164-817-000','Brgy Tubigan Biñan Laguna','Sigmamet Precision Corp.','VAT','MET',NULL,0.00,0.00,'2021-05-21 15:31:39','2021-05-21 15:31:39'),(115,'Smart Probe, Inc.','SMART PROBE','open','245-116-474-000','Batino, Calamba, Laguna','Smart Probe, Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-21 15:41:33','2021-05-21 15:41:33'),(116,'SMMC PRECISION TRADING','SMMC','open','116-333-853-000','11193 JM LOYOLA ST. MABUHAY CARMONA CAVITE','SMMC PRECISION TRADING','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:02:01','2021-05-21 16:02:01'),(117,'South Tools & Trading Services','S.TOOL','open','189-816-670-000','Molino IV, Bacoor, Cavite','South Tools & Trading Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:03:06','2021-05-21 16:03:06'),(118,'Susalum Corporation','SUSALUM','open','236-837-186-000','Puting Kahoy, Silang, Cavite','Susalum Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:04:05','2021-05-21 16:04:05'),(119,'Technoalloy Tooling Technologies','TECHNOALLOY','open','216-683-081-000','Lancaan, Dasmariñas, Cavite','Technoalloy Tooling Technologies','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:05:03','2021-05-21 16:05:03'),(120,'TGC Engineering Services','TGC','open','412-908-934-000','B2 L3  BELLA SUBD., BRGY SALA, CABUYAO, LAGUNA','TGC Engineering Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:06:05','2021-05-21 16:06:05'),(121,'TGM Precision Tool Fabrication','TGM','open','907-039-173-000','Bucal, Calamba, Laguna','TGM Precision Tool Fabrication','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:06:59','2021-05-21 16:06:59'),(122,'Toolworqs Fabrication Systems','TOOLWORQS','open','219-926-687','unit A1 #3 BANTAYAN 1ST BULIHAN MALOLOS BULACAN','Toolworqs Fabrication Systems','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:07:47','2021-05-21 16:07:47'),(123,'Trans Orient Microsystem Corporation','Trans Orient','open','009-109-593-000','Mabuhay, Carmona, Cavite','Trans Orient Microsystem Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:12:51','2021-05-21 16:12:51'),(124,'Tri-Fusion Precision Corporation','TRIFUSION','open','008-152-355-000','Brgy. Tagapo, Sta. Rosa City, Laguna','Tri-Fusion Precision Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:33:51','2021-05-21 16:33:51'),(125,'Tri-M Industrial Trading & Gen Services','TRI-M','open','234-668-440-000','270 Mahogany St. Brgy San Roaque, Sto. Tomas, Batangas','Tri-M Industrial Trading & Gen Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 16:56:30','2021-05-21 16:58:28'),(126,'Tritronics Technology Phils., Inc','TRITRONICS','open','201-749-646-000','Brgy. Maduya, Carmona, Cavite','Tritronics Technology Phils., Inc','ZRO','MET',NULL,0.00,0.00,'2021-05-21 16:59:08','2021-05-21 16:59:08'),(127,'Tochigi-Ken technologies International Inc.','TOCHIGI-KEN','open','007-886-177-000','BLK7 LT6 Golden Mile Business Park Maduya Carmona Cavite','Tochigi-Ken technologies International Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-21 17:00:24','2021-05-21 17:00:24'),(128,'Ubana Plastic & Metal Fabrication Works','UBANA','open','276-638-931-000','Anabu, Imus, Cvite','Ubana Plastic & Metal Fabrication Works','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:01:10','2021-05-21 17:01:10'),(129,'Ure-shii Technologies Inc.','URE-SHII','open','007-000-500-000','LSVSNCs4arBXAcfhVn9J87rXswQrRijvKG','Ure-shii Technologies Inc.','ZRO','MET',NULL,0.00,0.00,'2021-05-21 17:11:36','2021-05-21 17:11:36'),(130,'Vanp Tooling Metal Fabrication','VANP','open','719-734-267-000','Paliparan, Dasmariñas, Cavite','Vanp Tooling Metal Fabrication','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:14:04','2021-05-21 17:14:04'),(131,'Vestech Industrial Industries','VESTECH','open','231-811-359-001','Bulihan, Malvar, Batangas','Vestech Industrial Industries','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:15:04','2021-05-21 17:15:04'),(132,'Victowin Engineering Services & Trading','VICTOWIN','open','309-010-167-000','Pulo, Cabuyao, Laguna','Victowin Engineering Services & Trading','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:16:11','2021-05-21 17:16:11'),(133,'Vineusal Trading','VINEUSAL','open','489-525-200-000','Molave Ind\'l Estate, Paliparan 2, Dasmariñas, Cavite','Vineusal Trading','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:17:46','2021-05-21 17:17:46'),(134,'VJF Precision Toolings Corp.','VJF PRECISION','open','234-344-525-000','Magsaysay, San Pedro, Laguna','VJF Precision Toolings Corp.','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:20:14','2021-05-21 17:20:14'),(135,'VJF Toolmaster Corporation','VJF TM','open','008-034-296-000','Hi-lon Compound, Brgy. Mayapa, Calamba, Laguna','VJF Toolmaster Corporation','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:21:49','2021-05-21 17:21:49'),(136,'Wann Engineering Services','WANN','open','247-673-740-000','Brgy. Real, Calamba, Laguna','Wann Engineering Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:22:56','2021-05-21 17:22:56'),(137,'Weltech Enterprises','WELTECH','open','221-550-234-000','Ibayong Ilat, Kaong, Silang, Cavite','Weltech Enterprises','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:25:23','2021-05-21 17:25:23'),(138,'Wellvise  Technology, Inc.','WELLVISE','open','008 968 223 000','Governor\'s Drive, Mabuhay, Carmona, Cavite','Wellvise  Technology, Inc.','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:26:07','2021-05-21 17:26:07'),(139,'WFL Equipment Integration Corp.','WFL','open','007-003-135-000','Paliparan, Dasmariñas, Cavite','WFL Equipment Integration Corp.','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:26:54','2021-05-21 17:26:54'),(140,'Wintech Toolings & Services','WINTECH','open','196-195-200-000','Salasad, Magdalena, Laguna','Wintech Toolings & Services','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:27:31','2021-05-21 17:27:31'),(141,'YXL Enterprises','YXL','open','224-538-388-000','GMA , CAVITE','YXL Enterprises','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:53:14','2021-05-21 17:53:14'),(142,'Zenith Metal Industries, Inc.','ZENITH','open','225-185-687-000','Brgy. Sun Valey, Parañaque City','Zenith Metal Industries, Inc.','VAT','MET',NULL,0.00,0.00,'2021-05-21 17:54:12','2021-05-21 17:54:12'),(143,'Kobaishina Enterprise','kobaishina','open','149-472-270-000','B5 L5 A3 St, Reavilla Subd. Bryg. San Antonio, San Pedro Laguna','Kobaishina Enterprise','ZRO','MET',NULL,0.00,0.00,'2021-06-01 11:40:13','2021-06-01 11:40:13'),(144,'SPH Industrial Supply and Services','SPH','open','405-332-527-000','Blk 6 Lot 29 Gran Seville Vill. Banlic, Cabuyao Laguna','SPH Industrial Supply and Services','VAT','MET',NULL,0.00,0.00,'2021-06-01 12:11:30','2021-06-01 12:11:30'),(145,'Petrineans General Merchandise','PETRINEANS','open','214-212-876-000','Macabling Sta. Rosa Laguna','Petrineans General Merchandise','VAT','MET',NULL,0.00,0.00,'2021-06-01 15:57:19','2021-06-01 15:57:19'),(146,'JFS Precision Technology Corporation','JFS','open','225-709-638-000','Daiichi Ind\'l Park SEZ, Maguyam, Silang, Cavite','JFS Precision Technology Corporation','ZRO','MET',NULL,0.00,0.00,'2021-06-01 16:48:41','2021-06-01 16:48:41'),(147,'Key Automation Unit, Inc.','KAU','open','008-855-355-000','Technopark SEZ, Biñan, Laguna','Key Automation Unit, Inc.','ZRO','MET',NULL,0.00,0.00,'2021-06-01 16:56:55','2021-06-01 16:56:55'),(148,'JMS Metal Fabrication & Precision Toolings Services','JMS','open','252-921-878-000','Ibayong Ilat, Kaong, Silang, Cavite','JMS Metal Fabrication & Precision Toolings Services','VAT','MET',NULL,0.00,0.00,'2021-06-02 12:22:56','2021-06-02 12:22:56');
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `deliveries`
--

DROP TABLE IF EXISTS `deliveries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `deliveries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_date` date DEFAULT NULL,
  `supplier` varchar(50) DEFAULT NULL,
  `doc_no` int(11) DEFAULT NULL,
  `source` char(10) DEFAULT 'delivery' COMMENT 'return,order,delivery',
  `total` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `deliveries`
--

LOCK TABLES `deliveries` WRITE;
/*!40000 ALTER TABLE `deliveries` DISABLE KEYS */;
/*!40000 ALTER TABLE `deliveries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_details`
--

DROP TABLE IF EXISTS `delivery_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_details`
--

LOCK TABLES `delivery_details` WRITE;
/*!40000 ALTER TABLE `delivery_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_payments`
--

DROP TABLE IF EXISTS `delivery_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_payments`
--

LOCK TABLES `delivery_payments` WRITE;
/*!40000 ALTER TABLE `delivery_payments` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_adjustments`
--

DROP TABLE IF EXISTS `inventory_adjustments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_adjustments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=317 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_adjustments`
--

LOCK TABLES `inventory_adjustments` WRITE;
/*!40000 ALTER TABLE `inventory_adjustments` DISABLE KEYS */;
INSERT INTO `inventory_adjustments` VALUES (1,1,500,'2021-05-27 11:36:51'),(2,1,500,'2021-05-27 11:36:51'),(3,396,1000,'2021-06-01 11:13:40'),(4,396,1000,'2021-06-01 11:13:40'),(5,515,1000,'2021-06-01 11:17:31'),(6,515,1000,'2021-06-01 11:17:31'),(7,502,1000,'2021-06-01 11:19:59'),(8,502,1000,'2021-06-01 11:19:59'),(9,518,1000,'2021-06-01 11:21:49'),(10,518,1000,'2021-06-01 11:21:49'),(11,501,1000,'2021-06-01 11:24:17'),(12,501,1000,'2021-06-01 11:24:18'),(13,95,0,'2021-06-01 11:28:31'),(14,95,0,'2021-06-01 11:28:46'),(15,501,2000,'2021-06-01 11:31:28'),(16,501,2000,'2021-06-01 11:31:28'),(17,501,3000,'2021-06-01 11:32:35'),(18,501,3000,'2021-06-01 11:32:36'),(19,501,4000,'2021-06-01 11:33:17'),(20,501,4000,'2021-06-01 11:33:17'),(21,501,5000,'2021-06-01 11:34:03'),(22,501,5000,'2021-06-01 11:34:03'),(23,501,6000,'2021-06-01 11:34:48'),(24,501,6000,'2021-06-01 11:34:48'),(25,1,500,'2021-06-01 11:35:46'),(26,1,500,'2021-06-01 11:35:46'),(27,501,7000,'2021-06-01 11:36:24'),(28,501,7000,'2021-06-01 11:36:24'),(29,501,8000,'2021-06-01 11:37:01'),(30,501,8000,'2021-06-01 11:37:01'),(31,1,500,'2021-06-01 11:37:11'),(32,1,500,'2021-06-01 11:37:11'),(33,501,9000,'2021-06-01 11:37:37'),(34,501,9000,'2021-06-01 11:37:37'),(35,501,10000,'2021-06-01 11:38:18'),(36,501,10000,'2021-06-01 11:38:19'),(37,1,500,'2021-06-01 11:38:43'),(38,1,500,'2021-06-01 11:38:43'),(39,501,11000,'2021-06-01 11:38:54'),(40,501,11000,'2021-06-01 11:38:54'),(41,501,12000,'2021-06-01 11:39:32'),(42,501,12000,'2021-06-01 11:39:33'),(43,1,500,'2021-06-01 11:40:11'),(44,1,500,'2021-06-01 11:40:12'),(45,501,13000,'2021-06-01 11:40:32'),(46,501,13000,'2021-06-01 11:40:33'),(47,501,14000,'2021-06-01 11:41:07'),(48,501,14000,'2021-06-01 11:41:07'),(49,501,15000,'2021-06-01 11:41:40'),(50,501,15000,'2021-06-01 11:41:41'),(51,1,500,'2021-06-01 11:41:44'),(52,1,500,'2021-06-01 11:41:44'),(53,501,16000,'2021-06-01 11:42:22'),(54,501,16000,'2021-06-01 11:42:22'),(55,501,17000,'2021-06-01 11:43:24'),(56,501,17000,'2021-06-01 11:43:24'),(57,501,18000,'2021-06-01 11:44:01'),(58,501,18000,'2021-06-01 11:44:01'),(59,501,19000,'2021-06-01 11:44:46'),(60,501,19000,'2021-06-01 11:44:46'),(61,501,20000,'2021-06-01 11:47:41'),(62,501,20000,'2021-06-01 11:47:41'),(63,501,20000,'2021-06-01 11:48:46'),(64,501,20000,'2021-06-01 11:48:47'),(65,501,21000,'2021-06-01 11:49:26'),(66,501,21000,'2021-06-01 11:49:26'),(67,501,22000,'2021-06-01 11:51:05'),(68,501,22000,'2021-06-01 11:51:05'),(69,501,23000,'2021-06-01 11:51:56'),(70,501,23000,'2021-06-01 11:51:56'),(71,501,24000,'2021-06-01 11:53:42'),(72,501,24000,'2021-06-01 11:53:42'),(73,502,2000,'2021-06-01 11:54:39'),(74,502,2000,'2021-06-01 11:54:39'),(75,502,3000,'2021-06-01 11:55:27'),(76,502,3000,'2021-06-01 11:55:27'),(77,502,3000,'2021-06-01 11:57:17'),(78,502,3000,'2021-06-01 11:57:17'),(79,502,4000,'2021-06-01 12:07:04'),(80,502,4000,'2021-06-01 12:07:04'),(81,502,5000,'2021-06-01 12:07:52'),(82,502,5000,'2021-06-01 12:07:52'),(83,502,7000,'2021-06-01 12:09:52'),(84,502,7000,'2021-06-01 12:09:52'),(85,502,8000,'2021-06-01 12:10:39'),(86,502,8000,'2021-06-01 12:10:39'),(87,502,9000,'2021-06-01 12:11:17'),(88,502,9000,'2021-06-01 12:11:17'),(89,502,10000,'2021-06-01 12:11:56'),(90,502,10000,'2021-06-01 12:11:56'),(91,502,12000,'2021-06-01 12:14:32'),(92,502,12000,'2021-06-01 12:14:32'),(93,502,13000,'2021-06-01 12:15:01'),(94,502,13000,'2021-06-01 12:15:01'),(95,502,14000,'2021-06-01 12:15:40'),(96,502,14000,'2021-06-01 12:15:40'),(97,502,15000,'2021-06-01 12:16:21'),(98,502,15000,'2021-06-01 12:16:21'),(99,502,17000,'2021-06-01 12:18:34'),(100,502,17000,'2021-06-01 12:18:34'),(101,502,18000,'2021-06-01 12:19:16'),(102,502,18000,'2021-06-01 12:19:16'),(103,502,20000,'2021-06-01 12:20:22'),(104,502,20000,'2021-06-01 12:20:22'),(105,502,21000,'2021-06-01 12:20:55'),(106,502,21000,'2021-06-01 12:20:56'),(107,502,22000,'2021-06-01 12:21:28'),(108,502,22000,'2021-06-01 12:21:28'),(109,502,23000,'2021-06-01 12:22:05'),(110,502,23000,'2021-06-01 12:22:05'),(111,502,24000,'2021-06-01 12:22:37'),(112,502,24000,'2021-06-01 12:22:37'),(113,502,25000,'2021-06-01 12:23:05'),(114,502,25000,'2021-06-01 12:23:05'),(115,502,26000,'2021-06-01 12:24:34'),(116,502,26000,'2021-06-01 12:24:34'),(117,502,27000,'2021-06-01 12:25:47'),(118,502,27000,'2021-06-01 12:25:47'),(119,502,28000,'2021-06-01 12:26:19'),(120,502,28000,'2021-06-01 12:26:19'),(121,502,29000,'2021-06-01 12:27:00'),(122,502,29000,'2021-06-01 12:27:00'),(123,502,30000,'2021-06-01 12:27:48'),(124,502,30000,'2021-06-01 12:27:48'),(125,502,31000,'2021-06-01 12:28:36'),(126,502,31000,'2021-06-01 12:28:36'),(127,502,32000,'2021-06-01 12:29:50'),(128,502,32000,'2021-06-01 12:29:51'),(129,502,33000,'2021-06-01 12:30:33'),(130,502,33000,'2021-06-01 12:30:33'),(131,502,34000,'2021-06-01 12:32:13'),(132,502,34000,'2021-06-01 12:32:13'),(133,502,35000,'2021-06-01 12:32:59'),(134,502,35000,'2021-06-01 12:32:59'),(135,502,36000,'2021-06-01 12:34:11'),(136,502,36000,'2021-06-01 12:34:12'),(137,502,36000,'2021-06-01 12:36:12'),(138,502,36000,'2021-06-01 12:36:12'),(139,502,37000,'2021-06-01 12:37:43'),(140,502,37000,'2021-06-01 12:37:43'),(141,502,38000,'2021-06-01 12:38:24'),(142,502,38000,'2021-06-01 12:38:24'),(143,502,39000,'2021-06-01 12:39:07'),(144,502,39000,'2021-06-01 12:39:08'),(145,502,40000,'2021-06-01 12:42:13'),(146,502,40000,'2021-06-01 12:42:14'),(147,502,41000,'2021-06-01 12:44:12'),(148,502,41000,'2021-06-01 12:44:12'),(149,502,42000,'2021-06-01 12:52:47'),(150,502,42000,'2021-06-01 12:52:47'),(151,502,43000,'2021-06-01 12:53:34'),(152,502,43000,'2021-06-01 12:53:34'),(153,502,44000,'2021-06-01 12:54:17'),(154,502,44000,'2021-06-01 12:54:17'),(155,502,45000,'2021-06-01 12:55:09'),(156,502,45000,'2021-06-01 12:55:10'),(157,502,46000,'2021-06-01 12:55:55'),(158,502,46000,'2021-06-01 12:55:56'),(159,502,47000,'2021-06-01 12:56:40'),(160,502,47000,'2021-06-01 12:56:40'),(161,502,48000,'2021-06-01 12:57:18'),(162,502,48000,'2021-06-01 12:57:18'),(163,502,49000,'2021-06-01 12:58:16'),(164,502,49000,'2021-06-01 12:58:17'),(165,502,50000,'2021-06-01 13:00:19'),(166,502,50000,'2021-06-01 13:00:19'),(167,502,51000,'2021-06-01 14:21:26'),(168,502,51000,'2021-06-01 14:21:27'),(169,502,52000,'2021-06-01 14:22:55'),(170,502,52000,'2021-06-01 14:22:55'),(171,502,53000,'2021-06-01 14:23:36'),(172,502,53000,'2021-06-01 14:23:36'),(173,502,55000,'2021-06-01 14:24:13'),(174,502,55000,'2021-06-01 14:24:13'),(175,502,56000,'2021-06-01 14:24:46'),(176,502,56000,'2021-06-01 14:24:46'),(177,502,57000,'2021-06-01 14:25:14'),(178,502,57000,'2021-06-01 14:25:15'),(179,502,58000,'2021-06-01 14:25:44'),(180,502,58000,'2021-06-01 14:25:44'),(181,502,59000,'2021-06-01 14:26:07'),(182,502,59000,'2021-06-01 14:26:08'),(183,502,60000,'2021-06-01 14:26:40'),(184,502,60000,'2021-06-01 14:26:40'),(185,502,61000,'2021-06-01 14:28:30'),(186,502,61000,'2021-06-01 14:28:30'),(187,502,62000,'2021-06-01 14:28:59'),(188,502,62000,'2021-06-01 14:29:00'),(189,502,63000,'2021-06-01 14:29:21'),(190,502,63000,'2021-06-01 14:29:21'),(191,502,64000,'2021-06-01 14:29:51'),(192,502,64000,'2021-06-01 14:29:51'),(193,502,65000,'2021-06-01 14:30:22'),(194,502,65000,'2021-06-01 14:30:23'),(195,502,66000,'2021-06-01 14:30:56'),(196,502,66000,'2021-06-01 14:30:56'),(197,502,67000,'2021-06-01 14:31:42'),(198,502,67000,'2021-06-01 14:31:42'),(199,502,68000,'2021-06-01 14:32:12'),(200,502,68000,'2021-06-01 14:32:12'),(201,502,69000,'2021-06-01 14:32:38'),(202,502,69000,'2021-06-01 14:32:39'),(203,502,70000,'2021-06-01 14:33:19'),(204,502,70000,'2021-06-01 14:33:20'),(205,502,71000,'2021-06-01 14:33:53'),(206,502,71000,'2021-06-01 14:33:53'),(207,502,72000,'2021-06-01 14:34:43'),(208,502,72000,'2021-06-01 14:34:44'),(209,502,73000,'2021-06-01 14:35:15'),(210,502,73000,'2021-06-01 14:35:15'),(211,502,74000,'2021-06-01 14:43:14'),(212,502,74000,'2021-06-01 14:43:14'),(213,502,75000,'2021-06-01 14:44:02'),(214,502,75000,'2021-06-01 14:44:03'),(215,502,76000,'2021-06-01 14:44:32'),(216,502,76000,'2021-06-01 14:44:32'),(217,502,77000,'2021-06-01 14:45:06'),(218,502,77000,'2021-06-01 14:45:07'),(219,502,78000,'2021-06-01 14:45:32'),(220,502,78000,'2021-06-01 14:45:33'),(221,502,79000,'2021-06-01 14:46:06'),(222,502,79000,'2021-06-01 14:46:06'),(223,502,80000,'2021-06-01 14:46:49'),(224,502,80000,'2021-06-01 14:46:50'),(225,502,82000,'2021-06-01 14:48:26'),(226,502,82000,'2021-06-01 14:48:26'),(227,502,84000,'2021-06-01 14:48:57'),(228,502,84000,'2021-06-01 14:48:58'),(229,502,86000,'2021-06-01 14:49:26'),(230,502,86000,'2021-06-01 14:49:27'),(231,502,88000,'2021-06-01 14:50:21'),(232,502,88000,'2021-06-01 14:50:21'),(233,502,90000,'2021-06-01 14:50:55'),(234,502,90000,'2021-06-01 14:50:55'),(235,502,92000,'2021-06-01 14:51:33'),(236,502,92000,'2021-06-01 14:51:34'),(237,502,94000,'2021-06-01 14:52:01'),(238,502,94000,'2021-06-01 14:52:01'),(239,502,96000,'2021-06-01 14:52:34'),(240,502,96000,'2021-06-01 14:52:34'),(241,502,98000,'2021-06-01 14:53:17'),(242,502,98000,'2021-06-01 14:53:17'),(243,502,100000,'2021-06-01 14:54:08'),(244,502,100000,'2021-06-01 14:54:09'),(245,502,102000,'2021-06-01 14:54:40'),(246,502,102000,'2021-06-01 14:54:40'),(247,502,104000,'2021-06-01 14:55:12'),(248,502,104000,'2021-06-01 14:55:13'),(249,502,107000,'2021-06-01 14:56:01'),(250,502,107000,'2021-06-01 14:56:01'),(251,502,109000,'2021-06-01 14:57:12'),(252,502,109000,'2021-06-01 14:57:12'),(253,502,111000,'2021-06-01 14:57:41'),(254,502,111000,'2021-06-01 14:57:41'),(255,502,113000,'2021-06-01 14:58:10'),(256,502,113000,'2021-06-01 14:58:11'),(257,502,115000,'2021-06-01 15:02:02'),(258,502,115000,'2021-06-01 15:02:02'),(259,502,126000,'2021-06-01 15:04:25'),(260,502,126000,'2021-06-01 15:04:25'),(261,502,128000,'2021-06-01 15:06:21'),(262,502,128000,'2021-06-01 15:06:22'),(263,502,130000,'2021-06-01 15:06:52'),(264,502,130000,'2021-06-01 15:06:53'),(265,502,132000,'2021-06-01 15:07:22'),(266,502,132000,'2021-06-01 15:07:23'),(267,502,134000,'2021-06-01 15:08:36'),(268,502,134000,'2021-06-01 15:08:36'),(269,502,136000,'2021-06-01 15:11:15'),(270,502,136000,'2021-06-01 15:11:16'),(271,502,138000,'2021-06-01 15:11:52'),(272,502,138000,'2021-06-01 15:11:52'),(273,502,140000,'2021-06-01 15:12:42'),(274,502,140000,'2021-06-01 15:12:43'),(275,502,142000,'2021-06-01 15:13:35'),(276,502,142000,'2021-06-01 15:13:35'),(277,502,144000,'2021-06-01 15:14:13'),(278,502,144000,'2021-06-01 15:14:14'),(279,502,146000,'2021-06-01 15:14:49'),(280,502,146000,'2021-06-01 15:14:50'),(281,502,148000,'2021-06-01 15:15:27'),(282,502,148000,'2021-06-01 15:15:27'),(283,502,151000,'2021-06-01 15:16:24'),(284,502,151000,'2021-06-01 15:16:25'),(285,502,154000,'2021-06-01 15:17:10'),(286,502,154000,'2021-06-01 15:17:10'),(287,502,157000,'2021-06-01 15:17:48'),(288,502,157000,'2021-06-01 15:17:48'),(289,502,160000,'2021-06-01 15:18:24'),(290,502,160000,'2021-06-01 15:18:25'),(291,502,163000,'2021-06-01 15:18:53'),(292,502,163000,'2021-06-01 15:18:54'),(293,502,166000,'2021-06-01 15:19:52'),(294,502,166000,'2021-06-01 15:19:53'),(295,502,169000,'2021-06-01 15:20:34'),(296,502,169000,'2021-06-01 15:20:35'),(297,502,171000,'2021-06-01 15:20:58'),(298,502,171000,'2021-06-01 15:20:59'),(299,502,174000,'2021-06-01 15:21:53'),(300,502,174000,'2021-06-01 15:21:54'),(301,502,177000,'2021-06-01 15:22:29'),(302,502,177000,'2021-06-01 15:22:29'),(303,502,180000,'2021-06-01 15:23:18'),(304,502,180000,'2021-06-01 15:23:18'),(305,502,183000,'2021-06-01 15:24:32'),(306,502,183000,'2021-06-01 15:24:33'),(307,502,187000,'2021-06-01 15:29:25'),(308,502,187000,'2021-06-01 15:29:26'),(309,502,192000,'2021-06-01 15:35:12'),(310,502,192000,'2021-06-01 15:35:13'),(311,502,216000,'2021-06-01 15:46:13'),(312,502,NULL,'2021-06-01 15:46:14'),(313,395,1000,'2021-06-02 14:48:03'),(314,395,1000,'2021-06-02 14:48:03'),(315,395,1000,'2021-06-02 14:48:36'),(316,395,1000,'2021-06-02 14:48:36');
/*!40000 ALTER TABLE `inventory_adjustments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_logs`
--

DROP TABLE IF EXISTS `inventory_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `old_quantity` int(11) DEFAULT NULL,
  `act_quantity` int(11) DEFAULT NULL,
  `new_quantity` int(11) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_logs`
--

LOCK TABLES `inventory_logs` WRITE;
/*!40000 ALTER TABLE `inventory_logs` DISABLE KEYS */;
INSERT INTO `inventory_logs` VALUES (1,40,0,129500,-129500,'JOB','2021-05-08 15:59:11'),(2,1,2867107,3600,2863507,'JOB','2021-05-08 16:09:37'),(3,1,2863507,100,2863407,'JOB','2021-05-08 16:09:37'),(4,244,-1,5400,-5401,'JOB','2021-05-25 13:01:59'),(5,260,0,215,-215,'JOB','2021-05-25 13:01:59'),(6,10,6,309000,-308994,'JOB','2021-05-25 13:06:08'),(7,71,0,6180,-6180,'JOB','2021-05-25 14:26:50'),(8,69,0,13904,-13904,'JOB','2021-05-25 14:26:50'),(9,68,0,2024,-2024,'JOB','2021-05-25 14:26:50'),(10,68,-2024,3289,-5313,'JOB','2021-05-25 14:26:50'),(11,115,0,61509,-61509,'JOB','2021-05-25 14:43:07'),(12,485,0,80,-80,'JOB','2021-06-01 12:05:31'),(13,132,0,1540,-1540,'JOB','2021-06-01 12:17:30'),(14,704,0,38500,-38500,'JOB','2021-06-01 12:39:07'),(15,71,-6180,17500,-23680,'JOB','2021-06-01 12:39:07'),(16,801,NULL,67100,-67100,'JOB','2021-06-01 12:48:38'),(17,71,-23680,1000000,-1023680,'JOB','2021-06-01 13:46:21'),(18,68,-5313,1000000,-1005313,'JOB','2021-06-01 13:46:21'),(19,558,0,3000,-3000,'JOB','2021-06-01 13:50:11'),(20,802,NULL,45,-45,'JOB','2021-06-01 14:17:22'),(21,292,0,1000,-1000,'JOB','2021-06-01 14:17:53'),(22,805,NULL,1000,-1000,'JOB','2021-06-01 14:17:53'),(23,128,0,1000,-1000,'JOB','2021-06-01 14:18:25'),(24,55,2972897,14400000,-11427103,'JOB','2021-06-01 14:19:26'),(25,814,NULL,176,-176,'JOB','2021-06-01 16:20:47'),(26,802,-45,160,-205,'JOB','2021-06-01 16:20:47'),(27,720,NULL,7200,-7200,'JOB','2021-06-01 17:03:09'),(28,804,NULL,2880000,-2880000,'JOB','2021-06-01 17:04:06'),(29,659,0,330000,-330000,'JOB','2021-06-01 17:04:33'),(30,60,0,21504,-21504,'JOB','2021-06-01 18:00:41'),(31,70,0,20000,-20000,'JOB','2021-06-01 18:06:47'),(32,72,0,20000,-20000,'JOB','2021-06-01 18:06:47'),(33,74,0,20000,-20000,'JOB','2021-06-01 18:06:47'),(34,143,0,30,-30,'JOB','2021-06-01 18:06:47'),(35,143,-30,148,-178,'JOB','2021-06-01 18:06:47'),(36,143,-178,170,-348,'JOB','2021-06-01 18:06:47'),(37,134,0,90,-90,'JOB','2021-06-01 18:06:47'),(38,164,0,4428,-4428,'JOB','2021-06-01 18:07:31'),(39,471,0,11200,-11200,'JOB','2021-06-01 18:07:31'),(40,88,0,188496,-188496,'JOB','2021-06-02 09:52:44'),(41,813,NULL,14840,-14840,'JOB','2021-06-02 10:08:16'),(42,667,0,8250,-8250,'JOB','2021-06-02 11:11:37'),(43,666,0,2640,-2640,'JOB','2021-06-02 11:11:37'),(44,667,-8250,2688,-10938,'JOB','2021-06-02 11:11:37'),(45,666,-2640,2106,-4746,'JOB','2021-06-02 11:11:37'),(46,663,0,4950,-4950,'JOB','2021-06-02 11:11:37'),(47,663,-4950,4422,-9372,'JOB','2021-06-02 11:11:37'),(48,663,-9372,1584,-10956,'JOB','2021-06-02 11:11:37'),(49,663,-10956,1650,-12606,'JOB','2021-06-02 11:11:37'),(50,663,-12606,1485,-14091,'JOB','2021-06-02 11:11:37'),(51,821,NULL,3200,-3200,'JOB','2021-06-02 11:17:29'),(52,397,0,500,-500,'JOB','2021-06-02 11:53:15'),(53,474,0,760,-760,'JOB','2021-06-02 11:56:49'),(54,472,0,12800,-12800,'JOB','2021-06-02 11:56:49'),(55,472,-12800,14400,-27200,'JOB','2021-06-02 11:56:49');
/*!40000 ALTER TABLE `inventory_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice_details`
--

DROP TABLE IF EXISTS `invoice_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `description` varchar(50) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_details`
--

LOCK TABLES `invoice_details` WRITE;
/*!40000 ALTER TABLE `invoice_details` DISABLE KEYS */;
INSERT INTO `invoice_details` VALUES (1,1,40,'ABS  (IVORY) 5mm × 350mm × 370mm',1,1500.00,1500.00),(2,2,244,'NYLON  BLUE 12mm × 16mm × 42mm',6,50.00,300.00),(3,2,260,'NYLON  BLUE 25mm × 215mm',1,115.00,115.00),(4,3,10,'AS POLYCARBONATE  CLEAR 10mm × 510mm × 600mm',1,1000.00,1000.00),(5,4,71,'BAKELITE  OR20mm × 57mm × 100mm',1,140.00,140.00),(6,4,69,'BAKELITE  OR12mm × 85mm × 155mm',1,185.00,185.00),(7,4,68,'BAKELITE  OR10mm × 20mm × 85mm',1,50.00,50.00),(8,4,68,'BAKELITE  OR10mm × 20mm × 140mm',1,50.00,50.00),(9,5,115,'DELRIN  WHITE 30mm × 200mm × 300mm',1,700.00,700.00),(10,6,485,'PEEK  BEIGE 15mm × 80mm',1,500.00,500.00),(11,7,485,'PEEK  BEIGE 15mm × 80mm',1,500.00,500.00),(12,8,132,'DELRIN WHITE 35mm × 770mm',2,770.00,1540.00),(13,9,704,'PU YELLOW3mm × 175mm × 220mm',1,200.00,200.00),(14,9,71,'BAKELITE  OR20mm × 35mm × 250mm',2,220.00,440.00),(15,10,801,'AL 50836MM × 220MM × 305MM',1,840.00,840.00),(16,11,558,'PVC  GRAY 30mm × 1000mm',3,420.00,1260.00),(17,12,128,'DELRIN (WH 15mm × 1000mm',1,155.00,155.00),(18,13,60,'ACRYLIC  CLR 18mm × 28mm × 48mm',16,55.00,880.00),(19,14,814,'SUS 304 50.8MM × 176MM',1,1000.00,1000.00),(20,14,802,'SUS 304 57.15MM × 160MM',1,1075.00,1075.00),(21,15,164,'DELRIN  BLK  15mm × 27mm × 82mm',2,55.00,110.00),(22,15,471,'PEEK  (BEI 6mm × 70mm × 160mm',1,2370.00,2370.00),(23,16,70,'BAKELITE  OR 15mm × 100mm × 200mm',1,350.00,350.00),(24,16,72,'BAKELITE  OR 25mm × 100mm × 200mm',1,580.00,580.00),(25,16,74,'BAKELITE  OR 40mm × 100mm × 200mm',1,930.00,930.00),(26,16,143,'DELRIN (WH 90mm × 15mm',2,145.00,290.00),(27,16,143,'DELRIN (WH 90mm × 37mm',4,305.00,1220.00),(28,16,143,'DELRIN (WH 90mm × 85mm',2,655.00,1310.00),(29,16,134,'DELRIN (WH 45mm × 45mm',2,115.00,230.00),(30,17,88,'CDR6  BLK 10mm × 88mm × 153mm',14,920.00,12880.00),(31,18,659,'TEFLON 5mm × 110mm × 600mm',5,1740.00,8700.00),(32,19,813,'DELRIN BLACK 70MM × 106MM × 140MM',1,1290.00,1290.00),(33,20,667,'TEFLON 30mm × 55mm × 75mm',2,720.00,1440.00),(34,20,666,'TEFLON 25mm × 48mm × 55mm',1,400.00,400.00),(35,20,667,'TEFLON 30mm × 48mm × 56mm',1,485.00,485.00),(36,20,666,'TEFLON 25mm × 27mm × 39mm',2,180.00,360.00),(37,20,663,'TEFLON 12mm × 33mm × 75mm',2,185.00,370.00),(38,20,663,'TEFLON 12mm × 33mm × 67mm',2,165.00,330.00),(39,20,663,'TEFLON 12mm × 33mm × 48mm',1,120.00,120.00),(40,20,663,'TEFLON 12mm × 33mm × 50mm',1,125.00,125.00),(41,20,663,'TEFLON 12mm × 33mm × 45mm',1,115.00,115.00),(42,21,821,'ACRYLIC CLR 20MM × 40MM × 40MM',2,65.00,130.00),(43,22,397,'PE  WHITE 40mm × 500mm',1,435.00,435.00),(44,23,474,'PEEK  (BEI 12mm × 19mm × 40mm',1,435.00,435.00),(45,23,472,'PEEK  (BEI 8mm × 32mm × 200mm',2,2040.00,4080.00),(46,23,472,'PEEK  (BEI 8mm × 32mm × 150mm',3,1545.00,4635.00);
/*!40000 ALTER TABLE `invoice_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice_payments`
--

DROP TABLE IF EXISTS `invoice_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_payments`
--

LOCK TABLES `invoice_payments` WRITE;
/*!40000 ALTER TABLE `invoice_payments` DISABLE KEYS */;
INSERT INTO `invoice_payments` VALUES (1,1,'CHRG','30',1500.00),(2,2,'CHRG','30',415.00),(3,3,'CHRG','30',1000.00),(4,4,'CHRG','30',425.00),(5,5,'CHRG','30',700.00),(6,6,'CASH','COD',500.00),(7,7,'CASH','COD',500.00),(8,8,'CASH','COD',1540.00),(9,9,'CHRG','30',640.00),(10,10,'CHRG','30',840.00),(11,11,'CHRG','30',1260.00),(12,12,'CASH','COD',155.00),(13,13,'CASH','COD',880.00),(14,14,'CHRG','30',2075.00),(15,15,'CHRG','30',2480.00),(16,16,'CHRG','30',4910.00),(17,17,'CHRG','30',12880.00),(18,18,'CASH','COD',8700.00),(19,19,'CHRG','30',1290.00),(20,20,'CHRG','30',3745.00),(21,21,'CASH','COD',130.00),(22,22,'CASH','COD',435.00),(23,23,'CHRG','30',9150.00);
/*!40000 ALTER TABLE `invoice_payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `invoice_date` date DEFAULT NULL,
  `terms` int(11) NOT NULL,
  `term_date` date NOT NULL,
  `po_no` varchar(20) NOT NULL COMMENT 'Purchase Order No',
  `po_date` date NOT NULL COMMENT 'PO Date',
  `si_no` varchar(20) NOT NULL COMMENT 'Sales Invoice No',
  `si_date` date NOT NULL COMMENT 'SI Date',
  `dr_no` varchar(20) NOT NULL COMMENT 'Delivery Receipt No',
  `dr_date` date NOT NULL COMMENT 'DR Date',
  `cr_no` varchar(20) NOT NULL COMMENT 'Collection Receipt No',
  `cr_date` date NOT NULL COMMENT 'CR Date',
  `customer` varchar(80) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `created` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

LOCK TABLES `invoices` WRITE;
/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
INSERT INTO `invoices` VALUES (1,1,'2021-05-08',30,'0000-00-00','123','2021-05-08','1245','2021-05-08','4524','2021-05-08','','2021-05-08','Raitech',1500.00,0.00,0.00,0.00,0.00,'2021-05-08'),(2,7,'2021-05-25',30,'0000-00-00','VTC-RM-21-363A','2021-05-25','123','2021-05-25','','0000-00-00','','2021-05-25','VJF Toolmaster Corporation',415.00,0.00,0.00,49.80,0.00,'2021-05-25'),(3,8,'2021-05-25',30,'0000-00-00','112','2021-05-25','SI-1223','2021-05-25','','0000-00-00','','2021-05-25','Accura Mechanical Parts & Components Corp.',1000.00,0.00,0.00,120.00,0.00,'2021-05-25'),(4,9,'2021-05-25',30,'0000-00-00','21-6808','2021-05-25','SI-4532','2021-05-25','','0000-00-00','','2021-05-25','Meraki-Tech Precision Corporation',425.00,0.00,0.00,51.00,0.00,'2021-05-25'),(5,10,'2021-05-25',30,'0000-00-00','12356','2021-05-25','SI-1535','2021-05-25','','0000-00-00','','2021-05-25','Meraki-Tech Precision Corporation',700.00,0.00,0.00,84.00,0.00,'2021-05-25'),(6,16,'2021-06-01',0,'0000-00-00','THRU PHONE','2021-06-01','C-1614','2021-06-01','','0000-00-00','','2021-06-01','EMC Machine Works',500.00,0.00,0.00,60.00,0.00,'2021-06-01'),(7,16,'2021-06-01',0,'0000-00-00','THRU PHONE','2021-06-01','C-1614','2021-06-01','','0000-00-00','','2021-06-01','EMC Machine Works',500.00,0.00,0.00,53.57,0.00,'2021-06-01'),(8,17,'2021-06-01',0,'0000-00-00','THRU PHONE','2021-06-01','C-1613','2021-06-01','','0000-00-00','','2021-06-01','SPH Industrial Supply and Services',1540.00,0.00,0.00,184.80,0.00,'2021-06-01'),(9,13,'2021-06-01',30,'0000-00-00','021-001798','2021-06-01','SI-4785','2021-06-01','','0000-00-00','','2021-06-01','RGG Stainless Steel & Metal Fabrication',640.00,0.00,0.00,76.80,0.00,'2021-06-01'),(10,19,'2021-06-01',30,'0000-00-00','THRU PHONE','2021-06-01','SI-4786','2021-06-01','','0000-00-00','','2021-06-01','Maretech Precision Toolings & Industrial Services',840.00,0.00,0.00,90.00,0.00,'2021-06-01'),(11,14,'2021-06-01',30,'0000-00-00','THRU PHONE','2021-06-01','SI-4787','2021-06-01','','0000-00-00','','2021-06-01','Adtech Metal Precision',1260.00,0.00,0.00,135.00,0.00,'2021-06-01'),(12,20,'2021-06-01',0,'0000-00-00','4422','2021-06-01','C-1615','2021-06-01','','0000-00-00','','2021-06-01','Almendrala Industrial Services Company',155.00,0.00,0.00,16.61,0.00,'2021-06-01'),(13,33,'2021-06-01',0,'0000-00-00','THRU PHONE','2021-06-01','C-1617','2021-06-01','','0000-00-00','','2021-06-01','FULLERTECH',880.00,0.00,0.00,94.29,0.00,'2021-06-01'),(14,27,'2021-06-02',30,'0000-00-00','720','2021-06-01','SI-4789','2021-06-02','','0000-00-00','','2021-06-02','Tri-Fusion Precision Corporation',2075.00,0.00,0.00,222.32,0.00,'2021-06-02'),(15,31,'2021-06-02',30,'0000-00-00','THRU PHONE','2021-06-01','SI-4790','2021-06-02','','0000-00-00','','2021-06-02','Critomo Precision And Industrial Services',2480.00,0.00,0.00,265.71,0.00,'2021-06-02'),(16,32,'2021-06-02',30,'0000-00-00','MT05621-21','2021-06-01','SI-4791','2021-06-02','DR-1526','2021-06-02','','2021-06-02','JFS Precision Technology Corporation',4910.00,0.00,0.00,0.00,0.00,'2021-06-02'),(17,29,'2021-06-02',30,'0000-00-00','107783','2021-06-01','SI-4792','2021-06-02','DR-1527','2021-06-02','','2021-06-02','Key Automation Unit, Inc.',12880.00,0.00,0.00,0.00,0.00,'2021-06-02'),(18,26,'2021-06-02',0,'0000-00-00','PF21-031','2021-06-01','SI-4793','2021-06-02','','0000-00-00','','2021-06-02','Petrineans General Merchandise',8700.00,0.00,0.00,932.14,0.00,'2021-06-02'),(19,25,'2021-06-02',30,'0000-00-00','21-05-0197','2021-06-01','SI-4794','2021-06-02','','0000-00-00','','2021-06-02','ESJ Precision Toolings',1290.00,0.00,0.00,138.21,0.00,'2021-06-02'),(20,28,'2021-06-02',30,'0000-00-00','69','2021-06-01','SI-4795','2021-06-02','','0000-00-00','','2021-06-02','Alevro Precision Tooling Services',3745.00,0.00,0.00,401.25,0.00,'2021-06-02'),(21,35,'2021-06-02',0,'0000-00-00','2021-026','2021-06-02','C-1618','2021-06-02','','0000-00-00','','2021-06-02','Victowin Engineering Services & Trading',130.00,0.00,0.00,13.93,0.00,'2021-06-02'),(22,37,'2021-06-02',0,'0000-00-00','THRU PHONE','2021-06-02','C-1619','2021-06-02','','0000-00-00','','2021-06-02','Silver Jem Corporation',435.00,0.00,0.00,46.61,0.00,'2021-06-02'),(23,36,'2021-06-02',30,'0000-00-00','RM210602011','2021-06-02','SI-4796','2021-06-02','','0000-00-00','','2021-06-02','Lark Seimitsu Tooling Corporation',9150.00,0.00,0.00,980.36,0.00,'2021-06-02');
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `job_order_details`
--

DROP TABLE IF EXISTS `job_order_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `length` int(11) NOT NULL,
  `length_actual` int(11) NOT NULL,
  `width` int(11) NOT NULL,
  `width_actual` int(11) NOT NULL,
  `thickness` int(11) NOT NULL,
  `thickness_actual` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `quantity_actual` int(11) NOT NULL,
  `quantity_area` int(11) NOT NULL,
  `quantity_area_actual` int(11) NOT NULL,
  `po_price` decimal(8,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `job_order_details`
--

LOCK TABLES `job_order_details` WRITE;
/*!40000 ALTER TABLE `job_order_details` DISABLE KEYS */;
INSERT INTO `job_order_details` VALUES (1,1,40,370,370,350,350,0,0,1,1,129500,129500,1500.00),(2,2,1,10,12,10,12,0,0,25,25,2500,3600,500.00),(3,2,1,10,10,10,10,0,0,1,1,100,100,500.00),(4,3,244,42,45,16,20,0,0,6,6,4032,5400,50.00),(5,3,260,215,215,1,1,0,0,1,1,215,215,115.00),(6,4,10,600,600,510,515,0,0,1,1,306000,309000,1000.00),(7,5,71,100,103,57,60,0,0,1,1,5700,6180,140.00),(8,5,69,155,158,85,88,0,0,1,1,13175,13904,185.00),(9,5,68,85,88,20,23,0,0,1,1,1700,2024,50.00),(10,5,68,140,143,20,23,0,0,1,1,2800,3289,50.00),(11,6,115,300,303,200,203,0,0,1,1,60000,61509,700.00),(12,7,485,80,80,1,1,0,0,1,1,80,80,500.00),(13,8,132,770,770,1,1,0,0,2,2,1540,1540,770.00),(14,9,704,220,220,175,175,0,0,1,1,38500,38500,200.00),(15,9,71,250,250,35,35,0,0,2,2,17500,17500,220.00),(16,10,801,305,305,220,220,0,0,1,1,67100,67100,840.00),(17,11,71,1000,1000,1000,1000,0,0,1,1,1000000,1000000,17500.00),(18,11,68,1000,1000,1000,1000,0,0,1,1,1000000,1000000,9000.00),(19,12,558,1000,1000,1,1,0,0,3,3,3000,3000,420.00),(20,13,802,45,45,1,1,0,0,1,1,45,45,385.00),(21,14,292,1000,1000,1,1,0,0,1,1,1000,1000,80.00),(22,14,805,1000,1000,1,1,0,0,1,1,1000,1000,75.00),(23,15,128,1000,1000,1,1,0,0,1,1,1000,1000,155.00),(24,16,55,2400,2400,1200,1200,0,0,5,5,14400000,14400000,5100.00),(25,17,814,176,176,1,1,0,0,1,1,176,176,1000.00),(26,17,802,160,160,1,1,0,0,1,1,160,160,1075.00),(27,18,720,80,80,45,45,0,0,2,2,7200,7200,135.00),(28,19,804,2400,2400,1200,1200,0,0,1,1,2880000,2880000,4000.00),(29,20,659,600,600,110,110,0,0,5,5,330000,330000,1740.00),(30,21,60,48,48,28,28,0,0,16,16,21504,21504,55.00),(31,22,70,200,200,100,100,0,0,1,1,20000,20000,350.00),(32,22,72,200,200,100,100,0,0,1,1,20000,20000,580.00),(33,22,74,200,200,100,100,0,0,1,1,20000,20000,930.00),(34,22,143,15,15,1,1,0,0,2,2,30,30,145.00),(35,22,143,37,37,1,1,0,0,4,4,148,148,305.00),(36,22,143,85,85,1,1,0,0,2,2,170,170,655.00),(37,22,134,45,45,1,1,0,0,2,2,90,90,115.00),(38,23,164,82,82,27,27,0,0,2,2,4428,4428,55.00),(39,23,471,160,160,70,70,0,0,1,1,11200,11200,2370.00),(40,24,88,153,153,88,88,0,0,14,14,188496,188496,920.00),(41,25,813,140,140,106,106,0,0,1,1,14840,14840,1290.00),(42,26,667,75,75,55,55,0,0,2,2,8250,8250,720.00),(43,26,666,55,55,48,48,0,0,1,1,2640,2640,400.00),(44,26,667,56,56,48,48,0,0,1,1,2688,2688,485.00),(45,26,666,39,39,27,27,0,0,2,2,2106,2106,180.00),(46,26,663,75,75,33,33,0,0,2,2,4950,4950,185.00),(47,26,663,67,67,33,33,0,0,2,2,4422,4422,165.00),(48,26,663,48,48,33,33,0,0,1,1,1584,1584,120.00),(49,26,663,50,50,33,33,0,0,1,1,1650,1650,125.00),(50,26,663,45,45,33,33,0,0,1,1,1485,1485,115.00),(51,27,821,40,40,40,40,0,0,2,2,3200,3200,65.00),(52,28,397,500,500,1,1,0,0,1,1,500,500,435.00),(53,29,474,40,40,19,19,0,0,1,1,760,760,435.00),(54,29,472,200,200,32,32,0,0,2,2,12800,12800,2040.00),(55,29,472,150,150,32,32,0,0,3,3,14400,14400,1545.00);
/*!40000 ALTER TABLE `job_order_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `job_orders`
--

DROP TABLE IF EXISTS `job_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `po_no` varchar(20) NOT NULL,
  `po_date` date NOT NULL,
  `jo_date` date DEFAULT NULL,
  `user` varchar(80) DEFAULT NULL,
  `remarks` varchar(150) DEFAULT NULL,
  `created` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `job_orders`
--

LOCK TABLES `job_orders` WRITE;
/*!40000 ALTER TABLE `job_orders` DISABLE KEYS */;
INSERT INTO `job_orders` VALUES (1,1,'123','2021-05-08','2021-05-08','admin',NULL,'2021-05-08'),(2,3,'1234','2021-05-08','2021-05-08','admin',NULL,'2021-05-08'),(3,7,'VTC-RM-21-363A','2021-05-25','2021-05-25',NULL,NULL,'2021-05-25'),(4,8,'112','2021-05-25','2021-05-25','angelie',NULL,'2021-05-25'),(5,9,'21-6808','2021-05-25','2021-05-25','mhoneth',NULL,'2021-05-25'),(6,10,'12356','2021-05-25','2021-05-25','mhoneth',NULL,'2021-05-25'),(7,16,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(8,17,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(9,13,'021-001798','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(10,19,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(11,11,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(12,14,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(13,23,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(14,22,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(15,20,'4422','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(16,12,'DM21-329','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(17,27,'720','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(18,24,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(19,21,'3TI-1053-21','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(20,26,'PF21-031','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(21,33,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(22,32,'MT05621-21','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(23,31,'THRU PHONE','2021-06-01','2021-06-01','mhoneth',NULL,'2021-06-01'),(24,29,'107783','2021-06-01','2021-06-02','mhoneth',NULL,'2021-06-02'),(25,25,'21-05-0197','2021-06-01','2021-06-02','mhoneth',NULL,'2021-06-02'),(26,28,'69','2021-06-01','2021-06-02','mhoneth',NULL,'2021-06-02'),(27,35,'2021-026','2021-06-02','2021-06-02','mhoneth',NULL,'2021-06-02'),(28,37,'THRU PHONE','2021-06-02','2021-06-02','mhoneth',NULL,'2021-06-02'),(29,36,'RM210602011','2021-06-02','2021-06-02','mhoneth',NULL,'2021-06-02');
/*!40000 ALTER TABLE `job_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `module_users`
--

DROP TABLE IF EXISTS `module_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `module_users` (
  `id` char(36) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `module_id` char(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `module_users`
--

LOCK TABLES `module_users` WRITE;
/*!40000 ALTER TABLE `module_users` DISABLE KEYS */;
INSERT INTO `module_users` VALUES ('60a1e656-0820-47f1-a066-32e8c0a80069',3,'invtry'),('60a1f89a-eb58-4e88-873f-2bdc82e7a674',5,'invtry'),('60ac6fdb-2900-4558-9ebc-2fb482e7a674',1,'montr'),('60ac6fdb-2940-4b0a-adf5-2fb482e7a674',1,'invoice'),('60ac6fdb-4924-4c35-9a8e-2fb482e7a674',1,'users'),('60ac6fdb-4c20-4b1b-9b40-2fb482e7a674',1,'invtry'),('60ac6fdb-8dec-462a-a8bd-2fb482e7a674',1,'stckrm'),('60ac6fdb-ab34-4943-8663-2fb482e7a674',1,'accnt'),('60ac6fdb-abdc-4296-9e42-2fb482e7a674',1,'ldgr'),('60ac6fdb-c774-4e0b-9be2-2fb482e7a674',1,'po'),('60ac6fdb-cb38-47ba-bbef-2fb482e7a674',1,'trnx'),('60ac6fdb-d5f0-4868-985c-2fb482e7a674',1,'jo'),('60ac7019-b4a4-4ca2-9854-2fb482e7a674',6,'stckrm'),('60ac8300-1614-4428-84d3-2fb482e7a674',4,'accnt'),('60ac8300-6dd4-47bf-af25-2fb482e7a674',4,'po'),('60ac8300-c708-4265-95fd-2fb482e7a674',4,'montr'),('60ac8300-e0ac-411a-aaaf-2fb482e7a674',4,'invoice'),('60ac8300-f36c-49c4-9d87-2fb482e7a674',4,'jo'),('60af145b-0e68-42e6-a7c5-313882e7a674',7,'accnt'),('60af145b-6378-404e-b79e-313882e7a674',7,'jo'),('60af145b-79fc-4af5-950d-313882e7a674',7,'montr'),('60af145b-ae38-4287-92f4-313882e7a674',7,'po'),('60af145b-f84c-438c-b2df-313882e7a674',7,'invoice'),('60af145b-fa1c-4dad-a31e-313882e7a674',7,'invtry'),('60b5e532-009c-48ba-a023-320c82e7a674',2,'po'),('60b5e533-3bac-47da-aef2-320c82e7a674',2,'montr'),('60b5e533-43fc-45b5-a2c4-320c82e7a674',2,'stckrm'),('60b5e533-45b0-4115-989a-320c82e7a674',2,'invtry'),('60b5e533-7f00-422b-87e2-320c82e7a674',2,'invoice'),('60b5e533-bb68-4a76-9bb9-320c82e7a674',2,'users'),('60b5e533-c56c-4347-8ba7-320c82e7a674',2,'jo');
/*!40000 ALTER TABLE `module_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `modules`
--

DROP TABLE IF EXISTS `modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `modules` (
  `id` char(10) NOT NULL,
  `title` varchar(20) DEFAULT NULL,
  `link` varchar(50) DEFAULT NULL,
  `description` varchar(150) DEFAULT NULL,
  `icon` varchar(30) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `type` char(2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `modules`
--

LOCK TABLES `modules` WRITE;
/*!40000 ALTER TABLE `modules` DISABLE KEYS */;
INSERT INTO `modules` VALUES ('accnt','Accounts','accounts/homepage','Manage customer or supplier information and outstanding balances.','user',5,'BR'),('asssmnt','Assessment','inventory/assessment','Process trade in and return transaction from customers using the assessment module.','tags',3,'FD'),('bill','Billing','accounts/billing','Generate customer statement of account','usd',6,'BR'),('delvry','Deliveries','deliveries/homepage','Record incoming items using the delivery module.','log-in',4,'FD'),('invoice','Sales Invoice','sales/invoice','Complete PO with Sales Invoice','shopping-cart',2,'FD'),('invtry','Inventory','inventory/homepage','Keep track of your stocks and pricing using the','inbox',1,'BR'),('jo','Job Order','orders/job','Prepare items for cutting','scissors',3,'BR'),('ldgr','Ledgers','accounts/ledger','Review customer or supplier charges and payments.','th-list',8,'BR'),('montr','Monitoring','transactions/monitoring','Monitor Purchase Order and Sales','transfer',4,'BR'),('ordrs','Supplier Orders','orders/homepage','Request new inventory using the purchase order module.','log-out',5,'FD'),('po','Purchase Order','orders/purchase','Create PO from customers','folder-open',1,'FD'),('rtrnordr','Return Orders','inventory/returnorder','Send back items to supplier using the return.','repeat',6,'FD'),('sales','Sales','sales/homepage','Sell products using the point of sales module.','shopping-cart',0,'FD'),('stckrm','Stock Room','inventory/stockroom','Update your physical count using the stock room module.','object-align-bottom',2,'BR'),('trnx','Transactions','transactions/homepage','Review all inbound and outbound transactions.','transfer',8,'BR'),('users','Users','accounts/users','Manage staff\'s access using the user module.','pushpin',7,'BR');
/*!40000 ALTER TABLE `modules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_details`
--

DROP TABLE IF EXISTS `order_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_details`
--

LOCK TABLES `order_details` WRITE;
/*!40000 ALTER TABLE `order_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier` varchar(50) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `price_logs`
--

DROP TABLE IF EXISTS `price_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `old_price` decimal(10,2) DEFAULT NULL,
  `new_price` decimal(10,2) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `ref_no` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `price_logs`
--

LOCK TABLES `price_logs` WRITE;
/*!40000 ALTER TABLE `price_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `price_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_stocks`
--

DROP TABLE IF EXISTS `product_stocks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_stocks` (
  `id` char(5) NOT NULL,
  `product_id` int(11) NOT NULL,
  `type` char(2) NOT NULL,
  `area` decimal(8,2) NOT NULL,
  `notes` text NOT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_stocks`
--

LOCK TABLES `product_stocks` WRITE;
/*!40000 ALTER TABLE `product_stocks` DISABLE KEYS */;
INSERT INTO `product_stocks` VALUES ('13B84',1,'LC',500.00,'Scrap','2021-05-27 11:33:45','2021-06-01 11:41:44'),('F6968',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('73C83',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E58E9',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('451E1',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('4793D',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('D12F3',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('EA4EB',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('02C67',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('AC6EE',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('6CD4D',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('097E2',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('429A4',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('F40EE',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('C512B',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('FF969',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('AD5DB',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E9287',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('A1D33',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('0530E',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('8EEC1',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('17E7D',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E0ECB',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('270B6',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('71E09',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('9DBC5',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('BF945',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B6084',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('C1234',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('1CC3A',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('3FE89',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('85EA6',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('8CE6F',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('4217E',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('54FF9',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('0BDFA',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E1C13',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E81FB',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('52947',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('5D790',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('4011B',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B91F9',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('99744',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('1E3E1',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('76C53',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('9EED8',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('38838',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('C1927',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B0263',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('9380E',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('EB844',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('DBFFF',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('F417D',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('9EF76',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('A6CE9',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('9DB4F',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('9E827',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('86D43',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('83820',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('AFF2E',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('D5807',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('885FE',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('773E3',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('87807',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('51624',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('07314',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('9EC51',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('C4E21',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('1FA19',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('331CC',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('11348',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('0B071',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('238F1',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('A921D',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('03058',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('7742B',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('26337',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('7F46C',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B3746',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('6213A',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('6F8A1',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('F4CFA',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('45D66',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('75062',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('157E2',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('A706B',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('CEFAB',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('45F6A',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('41BE6',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E9ED9',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('01788',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('C4127',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('211ED',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B9490',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('0EAD0',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('81B66',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('53B88',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('56457',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('7FAD1',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('0AF78',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('D9EB0',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('EC038',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E8D2F',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('0832F',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('90165',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('533D2',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('57208',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('A76C0',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('DC554',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('AB22E',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('D2ED4',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('117CD',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('FEF56',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('4547D',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B1FB3',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('03227',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('51A9C',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('34883',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('F1E70',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('7F272',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('50435',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('5A2B8',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('0A343',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('807F3',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('84E9F',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E0AB5',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('F490D',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('DE26C',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('74564',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('53F13',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('8991C',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B5847',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('69344',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('E5CB9',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('4B698',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('C6D4E',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('13D83',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('EEB50',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('500D2',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('B0436',0,'',0.00,'','2021-05-27 11:33:45','2021-05-27 11:33:45'),('F1781',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('1BA4D',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('7184C',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('A122A',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('AF78D',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('22866',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('19300',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('D2319',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('902C7',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('848CE',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('30B1A',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('BDDAD',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('BB5D0',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('B1B20',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('BCFCD',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('CE2F2',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('646E6',0,'',0.00,'','2021-05-27 12:29:05','2021-05-27 12:29:05'),('C814D',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('D8281',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7F367',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('3CDAC',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A5A01',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B9CFE',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('89EFD',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('E5646',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('1543C',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('CA87E',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('70644',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('133B5',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('4C46B',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('10A40',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('4D1D7',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('88877',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('8BD3E',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('C7BF0',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('818E9',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('CBB68',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B20BB',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('90F1F',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('55B9D',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('98FB2',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B8742',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('0680E',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('0006D',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('27EF3',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('4B23F',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('91CF0',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('91129',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('8D0E8',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('E68D6',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('BCFA8',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('007FF',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('6AFB6',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('925A1',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('CA2DE',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('077FD',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('77535',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('80C37',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('F8539',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B7125',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B5724',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A89B9',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('16D8A',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('0120E',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('BD6C2',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('02F03',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('706BF',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('D9ECA',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('33823',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('F34C1',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('ECC55',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('55C11',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('75BD5',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('C35BC',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A3EB0',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A6248',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('82F29',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('CC809',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('C67BA',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B3F25',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('0EC15',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('30219',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('975FF',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('D736B',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('AE581',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A71F9',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('0B8AF',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('2478F',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('9F12F',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('2EDC8',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('54640',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('216F4',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('1CA18',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('8C722',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('AF5A9',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('4CF54',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7AD87',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A93DD',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('F7AB1',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('BF40F',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A4CD9',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('47C00',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A98BF',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('DE11D',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('14770',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7B929',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('59390',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('FB4D6',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('1AC4D',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('54A62',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('09DBC',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('38298',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('E903C',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('3EC59',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('02843',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B0D3E',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('1CD13',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('3AD7C',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('66968',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7C019',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('6B00B',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('29656',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('4B73C',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7D3D9',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('A0046',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B5845',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7B266',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('E38E3',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('33F68',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7C103',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('D0CBF',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('11352',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('96D15',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('71962',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('60CAB',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('36AC8',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('93106',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('115C5',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('20E7F',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('B3E3E',0,'',0.00,'','2021-05-27 12:29:06','2021-05-27 12:29:06'),('7AF62',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('5C64D',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('DC3F9',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E2F92',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('0AE3F',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('C75C4',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('444B0',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('47602',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('EA06E',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('4752B',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('0B080',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('B67FB',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('F0E1F',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('10703',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('8D420',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('8BBA7',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E4DA3',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('FC5F8',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E074A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('714C8',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('FEF6F',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('A5771',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('AB64C',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('298F9',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('F1828',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('97772',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('70C8F',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('AD0EF',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('16B0A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E3C52',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('AE696',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('BB838',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('1FD1D',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('B9546',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('37658',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('97FB7',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('6A639',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('D1A14',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('7C996',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('9A83E',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('91944',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('81947',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('3CF56',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('446AC',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('9E3A7',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('5B4D3',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('6C973',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('2C75C',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('7FDCA',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('66AD4',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('743DB',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('43BB7',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('F599A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('3CF25',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('97788',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('2639C',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('36885',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('18426',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('FC325',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('619CF',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('83F97',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('3F79A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E0EC4',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('80B91',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('F40FE',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('D07D8',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('8EF03',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('F4612',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('B599E',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('5349B',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('B4403',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('05A70',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('69930',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('380C5',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('4049F',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('2BC8A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('434EC',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('5939C',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('5F1D7',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('BACF3',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('4EBE7',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('53C16',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('16D45',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('D6C2E',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('FDF2A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('788C0',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('92B3A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('04925',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('EB5A2',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('C8769',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('8AB91',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('ACAF5',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('01BD9',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('8A16C',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('82B06',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('73A2E',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('D9D38',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('DC1F1',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('71103',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('CEEE6',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('004DB',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('26D6E',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('D3957',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('B4E62',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('168A0',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('54EAA',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('024D2',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('4B3F1',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('523B9',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('3C192',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E0381',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('ED383',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('9191B',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('02432',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E0AFD',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('F0A1A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('88213',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('26FD4',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('4B759',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('AB2C4',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('DB31E',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('77E86',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('06669',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('C8305',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('2A912',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('2A44F',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('BF201',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('10CAA',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('F2EA6',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('EB95A',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('8F1FA',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('1455F',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('E9470',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('ECD07',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('832F6',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('0D4C8',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('D0E75',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('16977',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('0D022',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('ACB95',0,'',0.00,'','2021-05-29 15:07:55','2021-05-29 15:07:55'),('A3AB4',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('124C3',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('32594',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('9C694',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('AAA97',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('7DA32',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('003A8',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('F1ADA',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('59965',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('4ECCA',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('2C2FB',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('37F65',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('90E22',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('72CA9',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('CC714',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('4021E',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('1A00D',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('879BC',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('11920',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('A13F8',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('6F942',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('883E8',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('2693B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('34300',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3B2F3',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('F073C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('902B2',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('8929C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3A511',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('E0272',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3E195',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('7DC64',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('0D318',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('D5B3D',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3C476',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('5E07B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('E3423',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('CA1E7',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('FAF02',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('481F0',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('9873E',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('E7C99',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('AB248',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('BBACA',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('1B11B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('81AA5',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('DE3A1',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('A0057',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('0B3C4',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('95973',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('893F9',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('0FB60',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('FCC5B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('9F482',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('7C220',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3C8BE',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('C366C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('0E639',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B1DA5',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B2A3C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('84272',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('8D55C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('C4A21',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('68CE1',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('25047',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('EE188',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('9729C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B9D21',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('0CB65',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B3CAA',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('5E4DF',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('CD950',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('6D70C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('DD4E2',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('92D1E',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('FF8D9',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B2D48',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('80B7B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('968D1',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('CAF1A',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('96A93',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('404A9',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('C003C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('F93F4',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('2BF0C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('2C7DC',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('93F52',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('37D79',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('8A50B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('91831',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('F7B02',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('5EF89',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('F3F22',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('18A90',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('4F164',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('EBBB5',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('92945',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3B5B8',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B0AB4',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('BE0BB',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('1CC41',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('52CAF',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('DDE49',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('A7DC8',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('A9CF4',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('5D130',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('F4A5D',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3EAE6',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('2A761',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B4755',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('443CB',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('57ED1',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('9828C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('5D5E9',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('E1924',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('4A850',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('4A33E',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('378A0',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('8383F',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('E25F5',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('521EA',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('20906',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('7A71B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('7A540',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('2C6DA',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('A91ED',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('3133F',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('77744',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('2649B',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('1C536',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('84F2E',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('34AC4',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('7486C',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('20EF1',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('680A3',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('EAAC4',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('B1256',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('76330',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('61B42',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('FB8E9',0,'',0.00,'','2021-05-31 11:16:27','2021-05-31 11:16:27'),('AC56F',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('BF74C',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('35296',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('8B604',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('7CC23',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('4452B',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('DB16F',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('2354E',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('228E3',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 14:46:06'),('6DBBE',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('4028A',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('3276D',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('47A85',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('C49B3',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('88FCF',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('6E013',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:14:50'),('5B619',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('1FF6E',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('0068A',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('23350',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('8B0BB',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('A787F',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('6D2F4',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('43CC2',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('53A9F',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('CD87B',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('55563',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('258E1',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 14:54:40'),('569D0',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('1A69E',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('39D92',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('C7079',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('34F52',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('E97EE',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('C37AE',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('17456',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('FEBD4',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('1EE9B',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('6318D',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('D034A',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('C95FD',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('E5AFB',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('C92D9',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('112C3',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('A7BA5',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('BBECA',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('806A1',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('D111F',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('585B1',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('C8758',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('A26E0',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('20125',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('BB03E',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('C2B8E',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('4BAF5',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('69748',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('2EF35',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('73983',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('3AB2E',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('44E6B',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('F56D8',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('9E876',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('6C571',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('89F0F',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('32584',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('FEBB7',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:13'),('D45B6',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:14'),('440F6',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:14'),('871EE',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('BEB3E',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('013CE',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('AE0E0',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('14ACA',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('7A37A',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('139C3',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('C15E1',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('F0E52',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('4AECF',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('1B7D0',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('D5A6C',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:14'),('A5E0F',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('AC4E7',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:14'),('C7529',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('A03FA',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('10FFB',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('A46E1',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('2A474',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('12171',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('1C510',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('F5532',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('03FCB',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('D3630',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('F26B5',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('809C3',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('FB89F',502,'FL',1000.00,'','2021-05-31 11:18:51','2021-06-01 15:46:14'),('A1AFC',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('9E952',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('4ABEF',0,'',0.00,'','2021-05-31 11:18:51','2021-05-31 11:18:51'),('DBDC6',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('0600A',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('98337',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('7E8C3',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('04511',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('0B49A',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('648F4',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('63CD9',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('9FFBF',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('B8E60',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('F883E',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('47686',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('5E056',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('8295E',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('DA56D',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('DE03B',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('F250D',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('5F8D8',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('235D3',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('B7BB3',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('39E10',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('1B8E8',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('ED8F5',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('D8068',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('019FA',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('53FA2',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('C667D',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('7BA3A',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('3FF50',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('1F79C',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('E5AE7',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('2A271',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('40250',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('E4F26',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('8F4C7',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('FA1DC',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('263D3',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('991DE',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('C379A',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('688FF',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('4FFCE',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('B3E59',0,'',0.00,'','2021-05-31 11:18:52','2021-05-31 11:18:52'),('E6798',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('E43A5',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('9BCB1',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('7E628',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('08E54',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('04C32',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('F0E6B',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('B58F7',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('08931',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('CBA2E',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('3FE78',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('A40CD',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('88024',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('4D8BD',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('372F8',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('5B48C',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('FC133',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('A3FC3',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('1B80B',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('840B3',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('959A5',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('B686A',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('46E0E',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('FD824',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('C1BC7',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('7061E',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('D2C1E',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('C2733',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('282B7',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('45E44',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('17254',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('6DC4A',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('B464D',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('47E2A',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('248E8',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('BF424',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('A5C7B',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('0A716',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('2B8EB',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('57106',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('F60CE',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('196F4',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('E2812',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('04992',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('34A95',0,'',0.00,'','2021-05-31 11:26:44','2021-05-31 11:26:44'),('17C5D',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('636EF',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('2AC24',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('DF2DB',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('B2904',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('44544',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('1B9E5',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('E51BA',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('BC0C0',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('DD608',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('AB41F',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('13019',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('7C412',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('B337E',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('5FC34',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('01064',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('D9AA7',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('57A33',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('02996',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('CBB6C',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('77E8D',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('FA306',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('D9976',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('4E504',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('67F3C',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('A1337',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('7EC49',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('FF237',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('7A006',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('DEF78',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('74DB8',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('231D8',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('41BCF',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('EB283',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('48DB7',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('D6D83',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('9B1C9',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('649AD',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('68A97',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('97A61',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('35868',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('C05A3',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('F7320',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('0FCEE',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('C6018',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('FB949',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('2E6D0',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('D1D0C',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('3B3FF',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('356F7',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('1E882',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('98896',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('1A1A9',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('E0367',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('2B3BF',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('E82A7',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('86D5C',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('6121A',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('4E551',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('07A4E',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('A226E',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('1B79B',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('806FE',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('16437',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('00B68',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('972A8',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('21097',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('0CDF6',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('FBF76',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('CCB8D',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('25E64',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('723DA',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('DCDFD',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('86998',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('03BD9',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('2F42C',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('BB144',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('D9D7B',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('6CEA1',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('0E2C2',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('6506F',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('56774',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('9B2A0',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('E3EA3',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('94B5B',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('690DB',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('08BDE',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('2A39B',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('D33BE',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('03BB3',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('3D1C6',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('851D6',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('34D96',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('62E0F',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('03CA3',0,'',0.00,'','2021-05-31 11:26:45','2021-05-31 11:26:45'),('52AAA',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('A4DCB',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('885Fd',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('12311',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('47CEC',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('CE840',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('9B2E8',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('7A414',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('9A49A',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('A9C15',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('23275',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('BDF48',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('D7C72',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('24B43',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('AD1C1',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('CEFA4',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('A82AA',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('3284D',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('34E76',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('C6B7A',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('C2818',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('1D01B',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('41A6F',395,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-02 14:48:36'),('30110',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('AD9F2',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('24B16',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('DFBBD',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('0FE81',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('530F4',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('D242F',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('C12D1',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('46BF8',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('83E8E',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('B139A',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2B45E',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2A5B6',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('13C0A',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('17EB2',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('5A570',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('5AF7C',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('08185',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('00C7B',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('7B443',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('45728',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('26357',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('39D65',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('9C72E',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('FC603',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('A3060',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2B315',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('D0999',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('153E7',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('A18D1',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('D5AB8',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('BEE59',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F1298',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('BEF00',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('BFDAF',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('A8578',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('1E360',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('17693',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('CEA0C',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('489DC',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('10E64',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('CA8A2',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('35A8B',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('B162D',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('4D883',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('FD5AC',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('DCCB1',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('0BB10',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('EFAF8',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('EAC62',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('65478',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('5AE6F',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('777B0',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('40D32',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('42831',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('9BA08',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F7714',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('20C43',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('ABC99',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('90E67',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('B3BF6',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('8222E',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('09356',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2217A',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('20C26',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('8C19F',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2CE93',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('3574C',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F187A',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('ED1D1',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('898B5',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('E8792',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('78BBB',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('B5BAA',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('92350',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('D7E4C',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('0A51F',502,'FL',1000.00,'','2021-05-31 11:33:22','2021-06-01 15:46:14'),('AC7DF',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('4C78F',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F454A',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('9DD28',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F4E34',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('D6B5B',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('31882',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('33322',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('3E24E',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('6541C',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('4AD03',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('B1457',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F5ABE',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('3D7D9',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('A5E94',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2DE5D',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('FD92A',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('EF6FE',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2BCB8',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('48237',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('14375',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('DE670',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('7C8AA',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('5E1E1',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('D4C07',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('0B368',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F629E',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('373E4',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F06D9',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('FC424',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('48FEF',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('54CB4',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F5122',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('A5759',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('3BFF8',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('3EB4C',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('BF441',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('C42AF',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('F7C91',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('14069',0,'',0.00,'','2021-05-31 11:33:22','2021-05-31 11:33:22'),('2BF62',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('23CCC',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('47D1E',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('7503C',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('89588',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('40216',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('3CFAC',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('51B51',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('C671B',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('53B86',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('6EAB7',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('4299F',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('76CF4',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('FC161',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('6D1E4',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('EA181',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('6AFFE',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('A3181',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('D4A4C',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('5554F',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CEF73',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DA652',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('A8436',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('D4AE7',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('4600F',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('69ADC',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DDE16',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('25B3A',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('87722',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('5C433',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('32BB9',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('00A17',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('E515D',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('F8B18',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('D2E9D',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('B2BAD',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('99932',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('9A7E4',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CD474',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('4C640',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('018DD',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('17B52',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('6A014',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('34DC2',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('C0F6F',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('977E6',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('EC7F3',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DBD90',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('4D83F',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('41DBA',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('E7BA9',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('91665',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('86B20',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('E58CC',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('4A91A',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('6944D',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('0B0D2',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('82898',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('76033',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('F18A6',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('77B1A',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('70949',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('5D7AD',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('B1553',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('65489',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('A878D',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('222C4',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('AC2EF',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('92A0E',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('F06AD',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('0DF9D',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('46982',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CAD22',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('540AE',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('29957',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CDCB2',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('F21C4',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('ACA32',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('80B0C',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('30D0D',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('512FC',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('B4274',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('FB5F5',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('07B93',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('51783',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('EF189',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('AEE44',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DC478',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('2D579',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CC638',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('35CF8',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('D3AEE',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('E6482',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('2BD17',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('842CA',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('D86A2',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CB757',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('647C7',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('1EF03',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('83207',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('A6334',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('4A081',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('7DD11',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('9268B',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('39B76',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('3BA41',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('1B2C7',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('97737',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('4A29F',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('25926',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('475D6',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('65C55',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('04600',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('C0642',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('129FA',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('288CC',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('94E19',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('5ABAD',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('23CE1',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CC0C0',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('2F891',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CD49F',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('8D98E',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('D1F44',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('1C23E',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DE7E7',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DF7C2',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('23D2E',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DB105',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DD4E4',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('CA344',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('11A31',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('604ED',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('580F9',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('3E06C',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('1255C',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('B9CB9',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('E98B5',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('A4787',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('DB923',0,'',0.00,'','2021-05-31 11:38:19','2021-05-31 11:38:19'),('1B47A',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('C0D58',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('FC76D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('F5FE1',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('A5329',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('BF499',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('E4D78',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('FF5B3',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('39A1D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('82429',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('5A23E',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('F3185',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1FCD4',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('C392D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('AEC4C',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('9B349',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('00749',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('B957D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1FB2A',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('84CB1',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('ABFD6',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('EC69A',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1D354',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('B3F9A',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('C3F7A',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D1CC7',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('23B3E',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('4A587',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('95441',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('680C2',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D2093',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('55327',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('7EEA1',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('3C82C',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('6C6B5',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('BCE8C',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('296AD',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('E7F9E',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('A0B45',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1531B',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('61501',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('FC102',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('8B749',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('990B1',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('BA555',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1F75C',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('C45BD',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('5F158',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('9E106',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('76D41',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('66FE2',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D9466',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('44131',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('82F2B',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('81CFE',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('EE389',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('29DAF',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('6E863',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('E81F1',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('06B44',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('19CA1',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('266C4',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('9D203',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1879D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('CFDE7',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('AE714',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('F2BD2',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('58752',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('96752',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('EE61B',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('9AEAD',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('9BF52',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1C67D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('6DCC8',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('18B0A',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('21AFB',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('C08AE',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('6B3C4',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('2EB56',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('41518',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1166C',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('CE89F',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('5DE2A',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('4E9D4',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('72ABA',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('AEC85',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('A14AC',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('B53B3',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('94968',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('3713D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('F9AB9',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1D038',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('907DE',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('2723D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('91E82',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('7D3C9',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('335F5',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('FF3D6',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('5774B',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D8421',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('449EF',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('3B204',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('344EF',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('A4315',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('4DDD8',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('EF003',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('95E15',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('3DFA0',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('6BA38',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D095E',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('214A7',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('18D84',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('DFA92',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('272E1',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('7575C',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('7269E',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('46125',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('F5C15',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('85AD0',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('CC8C6',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('1FF4D',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('917FA',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('E37E7',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('80A3F',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('F29FA',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('144B6',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('639D7',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('6E07F',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('14798',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('E9DE7',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('B9A01',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D516B',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D4671',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('7E676',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('3D387',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D2A70',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('D77C7',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('6E0CF',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('0421C',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('3AA5B',0,'',0.00,'','2021-05-31 11:58:54','2021-05-31 11:58:54'),('601C6',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('0FFF8',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('92D33',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('42917',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('17D6A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('899B6',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E7DB1',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F7301',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('1B89A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C6EA0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('D1C09',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('876F1',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('1BDA7',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('D7FAC',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('77983',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('FFD4A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('A70F6',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('4AC61',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('32600',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('38550',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C928D',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B7DE9',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('62F6E',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('8B321',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B57FA',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('26876',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('5B9A8',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('EFAAB',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('28982',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('AE951',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('01646',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('6F5E4',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('A0093',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('0FF0A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('8709C',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B844B',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('280AA',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C8819',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('7A2CE',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('6B4A9',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('A9986',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F8E64',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('DFEAD',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('FC792',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('2CA59',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('34113',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F15D3',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('EE364',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('68C1F',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('DE86B',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('ED123',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('BF433',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('4AAA7',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('84CA2',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('CD55F',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F4D22',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('7385D',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('A3301',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E6507',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('FC3F0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('8DD8A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('4D95D',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('0D924',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('50E17',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('FB508',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('043C0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C6B2A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('4379C',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C8BE3',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('28064',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F811F',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('08B41',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('6E171',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('349F8',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('51252',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('AC667',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C6410',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C3236',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('2BE5F',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B7EE6',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('FBE6D',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B6B35',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('4C141',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('22464',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('8AF95',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('86C51',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E71F0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('15709',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('0D0FD',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E0F66',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E40BD',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('45FE7',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E5ABB',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('235FB',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('398E2',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E2FCC',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('D2411',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E8CBA',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('0C52D',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('6B8B8',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('DE3F7',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('2291D',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('73DC6',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('5B970',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('3644A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('14F5C',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F677E',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('623A1',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('4579B',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B59A5',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('9078B',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('9E2A9',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B93E7',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('7BB09',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('DA3B0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F47A0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('90E4D',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('EC894',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('3DCC0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('2E505',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('CF43A',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('ECDF4',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B57F7',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('C0AF9',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F8EB2',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('21C1F',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('54D4C',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('84FEC',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F4BE0',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('BB013',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('31C0B',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('9E8A5',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('397D6',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('125B9',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('F95EC',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('7A2B3',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('A8ECB',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('E6D55',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('B63E5',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('6CE64',0,'',0.00,'','2021-05-31 12:03:43','2021-05-31 12:03:43'),('5E1F6',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('015E3',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('1151D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('95631',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('81BC7',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('02E21',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('0E22A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3D8A0',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('EA46B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F1B96',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('2DE7C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('ED148',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('8E1EF',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('08FCB',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('6CE9F',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3435C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('1B31A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('B7F0B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('55A51',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D4B92',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D397C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('7EE88',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('9F060',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3D7BE',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D5C90',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('8086D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('EDEE3',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('6BE53',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('16048',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('4699D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('1687E',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('751F8',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D0B1C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('571AC',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AC976',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F23A1',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F004A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('1EF4C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('B417C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('26F8D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AFEF6',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('18074',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('DAECA',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('61879',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('7EC24',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('ABA3B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AE87A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('18C9D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('EC79D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('01AD7',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('044A2',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('E1F77',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('5B1AD',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('15FD4',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('DB209',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('157F7',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3FE94',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('59411',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('A1A3E',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('68039',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('7C1B5',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('B8D88',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F1BA8',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('DCD38',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('0A942',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('C61F0',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3FF4C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('E3844',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3ECD2',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D5010',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('2C71B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('5D2A1',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('B252E',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('17AB7',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('C2C1F',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('802CB',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('B4866',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D847A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('FA6F5',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('58599',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('54D30',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('23873',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('63C17',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('C8E77',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('88655',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('57CE3',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AAADB',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F7B49',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('4991A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('DBF32',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('FF413',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AA1B6',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('4317F',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('8BE00',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AF88D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('348A4',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('B313B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('634B9',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('E231A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AF64B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('7C131',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('657E3',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('C255C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('05992',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('36E0C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('9D372',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('E0D04',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3AE4F',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('0CB92',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('1ADFB',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F2207',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('4D8AB',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('DB182',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('9EE01',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('24611',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('C9F02',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AE4DB',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('5FDE4',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AFCF9',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('35D58',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('610CE',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('27AC0',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('23114',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('3FAB5',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F7AC6',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D785B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('04288',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('96671',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('BF5EC',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('6B45B',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('62E09',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('AFF39',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('FFA62',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('F1B4A',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('8B42D',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('5B0AC',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('5FD7C',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('2C758',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('D5729',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('216A7',0,'',0.00,'','2021-05-31 12:14:38','2021-05-31 12:14:38'),('E3603',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('8FD7F',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('06130',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('38763',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('C09F9',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('A5764',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('D1DC3',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('45A04',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('07E99',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('93E4D',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('C1FCF',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('4B8FD',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('93279',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('8F1BA',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('363CE',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('75E1F',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('2FDF1',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('E3118',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('F3BB0',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('56584',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('5424E',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('8BC56',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('8BECA',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('416E5',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('1441F',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('8A707',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('38220',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('78AA9',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('11997',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('DA547',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('072E8',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('3AED8',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('62D90',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('885CB',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('5E3AD',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('EF2A4',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('1C6E0',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('D3379',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('97108',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('B146D',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('C22AB',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('C105C',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('BAB2A',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('A2369',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('55603',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('D1301',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('C37A2',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('8CE8B',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('F8991',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('2F288',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('729F2',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('5FC78',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('8EF4B',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('BD1DE',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('3ACA2',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('68F77',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('FEE6E',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('240C9',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('876AF',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('7BA06',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('CFA25',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('2668A',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('AC508',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('2ABBB',0,'',0.00,'','2021-05-31 12:15:23','2021-05-31 12:15:23'),('89A00',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('9FB7B',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('34F81',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('F3507',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('986E7',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('7DCF8',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('95CB2',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('B8C83',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('5B38A',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('49AFA',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('EFFAA',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('B09E9',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('A9FCE',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('C73ED',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('18856',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('6080B',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('084B6',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('971BB',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('98EDC',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('4C07F',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('B1578',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('C262F',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('3AF3C',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('801F7',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('52DFA',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('966B7',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('477B0',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('6B369',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('AB199',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('C8CC6',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('75883',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('3708D',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('9C58D',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('F101B',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('33463',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('ACB5D',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('E82BE',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('D71FA',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('CEE8D',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('66848',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('FFA55',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('4D124',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('6C94A',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('AC596',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('7316E',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('AF21D',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('C87EE',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('5DE8A',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('7AE77',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('C6943',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('9548A',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('050CB',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('7D8D3',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('48844',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('CF346',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('4D2E7',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('9DCB8',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('813B0',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('DDF26',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('2E390',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('90C83',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('76908',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('093C7',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('BE9AB',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('6605C',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('0BBFD',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('87233',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('9A9E0',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('3181D',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('D7619',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('40BD3',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('7FFD8',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('F2C3B',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('510B6',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('28098',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('84403',0,'',0.00,'','2021-05-31 12:15:24','2021-05-31 12:15:24'),('969A0',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('7504A',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('D63C4',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('F9827',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('45C68',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('16AF6',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('01C9D',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('3E4B6',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('60131',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('853B7',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('BA0C3',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('1B2D6',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('8C7C8',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('786F2',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('DA622',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('0D0D9',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('F0D70',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('7CA5E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('16790',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('C4492',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('8910F',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('4A305',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('CA7BE',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('35BEF',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('F4285',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('85BA1',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('23483',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('1E4D3',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('8012C',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('0740B',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('A7A89',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('ABA0E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('ABF09',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('69158',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('000A9',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('422E4',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('FA11D',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('78428',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('FD1BE',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('91F57',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('E9527',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('262F3',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('E64C9',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('B8361',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('19184',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('5ED17',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('5E2B6',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('E394C',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('E0A9B',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('5779E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('41D87',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('9C68B',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('BEB69',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('ACF7C',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('D4E3E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('993E2',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('EAC93',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('FE894',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('3795D',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('786FC',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('9DCC7',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('16B5E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('A2926',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('DCE31',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('A5148',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('23AF4',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('29647',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('F5C98',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('EFE34',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('D54E9',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('254ED',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('C89F2',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('0CAA6',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('CB265',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('92464',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('4F31C',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('ECF4A',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('04B0F',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('FC095',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('2D818',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('9379F',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('10C72',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('C5603',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('C766C',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('56C25',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('964D1',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('0EC29',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('0314C',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('B9558',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('ADEB5',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('CCF49',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('6E828',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('9B4F5',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('3D7A8',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('032DD',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('C5D64',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('DFF8E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('78748',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('89AF2',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('B5DAA',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('52D5D',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('AC39D',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('59E0B',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('111EA',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('25B30',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('84D57',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('CE052',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('8E088',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('5C5A9',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('628F1',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('12B2F',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('9365A',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('D44EA',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('5705E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('660BC',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('D202E',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('7A6CF',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('20BE3',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('36852',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('FBC6B',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('8210D',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('B0928',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('8CC4B',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('574EB',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('37931',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('1AA7A',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('183F5',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('07895',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('AE614',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('DC131',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('D5215',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('5FF4F',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('3ED01',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('65A3A',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('786AB',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('88F51',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('34609',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('F5618',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('A7FDD',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('81F89',0,'',0.00,'','2021-05-31 12:19:03','2021-05-31 12:19:03'),('20A85',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('5F937',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('C8997',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('13D4B',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('803EF',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('2618C',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('9518D',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('DC9DA',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('3A6CD',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('34B19',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('1BE3B',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('E6384',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('A36B5',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('FC7E8',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('61513',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('1123C',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('588FA',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('FE256',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('683A1',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('7C960',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('22269',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('B96A8',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('6A40C',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('9087F',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('2647C',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('AFF00',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('07FB6',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('A7BA7',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('E3873',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('E523E',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('48FDF',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('ACE2E',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('5726D',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('40043',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('AB731',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('5F679',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('9F6F2',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('63C3D',0,'',0.00,'','2021-05-31 12:21:31','2021-05-31 12:21:31'),('7EAB4',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('6A801',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('0B668',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('6D3B4',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('859B0',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('A389E',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('FF5F9',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('0181D',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('32E7B',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('09859',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('E7B24',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('10B83',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('9C7F9',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('3C1E4',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('4123D',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('0EC96',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('B6D69',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('81BAA',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('FDAA0',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('CE480',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('00FD1',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('C70A5',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('C8067',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('B06CB',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('AFF0A',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('8AC46',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('E8855',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('9F6C3',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('A64F2',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('5B1E2',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('A9DBF',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('B6BF0',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('42085',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('2F364',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('EF239',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('41CCC',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('E3A41',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('B2B8D',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('B34E2',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('0AC6B',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('6D9CB',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('9FCF1',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('D9DB2',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('499F5',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('647C4',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('11B02',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('F4D3D',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('11B46',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('D3F93',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('5EA2F',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('FAAD1',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('4BA92',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('E3DB3',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('1772D',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('81517',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('34186',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('A4487',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('373CB',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('8810D',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('89A47',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('DFFBB',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('03190',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('56FD8',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('60315',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('1C330',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('226D1',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('B6AD3',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('72812',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('524E3',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('7EF60',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('B1196',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('F65C0',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('65B03',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('3139F',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('77EBE',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('63154',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('48910',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('5B5C7',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('76607',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('D778D',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('D709F',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('CD10C',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('E3C87',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('EB95E',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('F9C6F',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('23EC2',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('15727',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('8B2AE',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('6198B',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('94151',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('D9FEA',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('F67CD',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('FECFE',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('37464',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('1D3B8',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('BA61A',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('35769',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('22041',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('7AFE9',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('5527E',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('1D241',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('E1E26',0,'',0.00,'','2021-05-31 12:21:32','2021-05-31 12:21:32'),('25361',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('D3E8F',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('2E85D',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('305A3',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('D6563',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('67462',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('7A674',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('E1091',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('27816',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('954F3',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('F3D40',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('C77CF',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('49B8B',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('60C53',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('DFC30',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('37E4F',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('548F4',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('91BE2',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('2EE2B',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('841B6',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('35949',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('BEA6C',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('EAD7D',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('08504',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('7BD89',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('69421',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('7D604',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('6D16B',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('B7A78',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('65DDF',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('ACF73',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('07602',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('F8895',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('EC615',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('3812F',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('BEC86',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('9A497',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('D2849',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('6CA4E',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('9E6A9',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('BD819',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('7BC1E',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('44EC7',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('45D77',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('5C4F8',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('FB5D9',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('ED8B4',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('D33C1',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('5A0B8',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('78D8A',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('15109',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('D9BD6',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('B8B9C',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('87269',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('1FC8C',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('C38CF',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('242C1',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('74378',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('39CD7',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('11F9E',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('A485F',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('EA9BE',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('4D051',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('20EEC',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('BB2DB',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('9BAFA',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('AD551',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('C1AA3',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('DBA76',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('DB2ED',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('A30BC',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('4564E',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('539A4',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('878D5',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('C8ED2',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('A66B2',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('1A77B',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('E04F7',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('FACE3',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('DF6A6',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('5694B',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('F542E',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('AD067',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('9D006',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('F05F7',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('6A46F',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('1BA3C',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('1AE51',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('B12B9',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('CA759',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('BA01B',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('E2AE7',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('221D7',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('DA0B5',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('450E9',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('D1C79',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('5E18B',0,'',0.00,'','2021-05-31 12:25:55','2021-05-31 12:25:55'),('66072',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('914CE',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('2A774',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('F3C95',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('7854C',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('D6DA0',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('BFE6C',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('73A3D',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('692F4',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('9DCBA',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('90F1C',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('659F7',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('AD0CB',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('0447A',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('F0A67',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('37D0B',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('E1F58',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('1DC90',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('66980',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('714AE',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('9D38E',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('EAB4F',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('085DF',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('D91F7',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('FBABB',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('6F04F',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('09B35',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('D756D',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('1CF2B',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('0801B',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('2ADEE',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('AA8FD',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('C2100',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('A3D10',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('E96B0',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('37790',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('9461C',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('0172D',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('976E1',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('8BE57',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('4B4A9',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('9DA70',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('A510A',0,'',0.00,'','2021-05-31 12:25:56','2021-05-31 12:25:56'),('63544',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('5FB0C',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('A790B',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('C13CE',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('00A03',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('4B31C',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('5E02B',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('84A52',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('9E04A',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('A749E',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('B7420',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('94BF0',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('7B670',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('437AA',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('BD082',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('7059C',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('E9DCB',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('0FC8D',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('C4FAC',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('D4BAD',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('E988C',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('81817',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('16026',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('9B65E',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('6A7B6',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('3B21D',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('8F38A',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('DB4DB',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('C2040',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('E9D36',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('F2FB9',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('AB9EB',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('B4BEE',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('56509',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('7A54F',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('AF83D',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('9B54F',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('36A21',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('57220',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('B88E5',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('1FF5F',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('BD3B6',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('B3D5C',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('7104A',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('C0E2B',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('69516',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('07C4E',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('CA889',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('72CF9',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('AA2C6',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('44923',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('F01A3',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('59049',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('4B7F8',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('FCC6F',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('37953',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('460C9',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('9796D',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('41F1F',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('A5311',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('6F240',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('B68C8',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('A5E4E',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('92E6E',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('6C1E5',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('40BF8',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('E3C92',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('1C4B0',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('98D27',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('D7C3F',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('1AB60',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('5C5BC',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('9CBE3',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('CDD7D',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('91E1F',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('1F10C',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('98795',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('0C71F',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('09094',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('745DC',518,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:21:49'),('7B5BD',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('ABBB9',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('E1167',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('76006',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('383BE',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('234C7',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('4E4E5',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('AF074',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('DFC69',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('03710',515,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:17:31'),('AA3F8',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('F0D48',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('F1CE9',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('8e034',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 14:22:55'),('AEB00',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('AAD5A',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('BA01E',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('B3B9B',502,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 15:46:14'),('3218B',501,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:53:42'),('E6340',396,'FL',1000.00,'','2021-05-31 12:29:25','2021-06-01 11:13:40'),('D79F7',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('D9E7A',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('D9FF9',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('00650',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('E2230',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('2CD29',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('92650',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('F2458',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('9274A',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('9313F',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('E721A',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('DBA59',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('E0413',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('11E2A',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('068BC',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('FDB0D',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('B4768',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('526C3',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('B4337',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('4B12D',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('2CE97',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('F9D3A',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('F8ED6',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('CDAD4',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('95DA0',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('EE8DC',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('C1DE2',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('E1FF3',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('63EFF',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('F2E24',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('78378',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('2959C',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('AD015',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('67EC9',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('A6D01',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('35B72',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('9BC09',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('5F949',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('0988A',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('5D252',0,'',0.00,'','2021-05-31 12:29:25','2021-05-31 12:29:25'),('EF7DB',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('3B502',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D59EC',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('658BB',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('88F0B',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('DA2C3',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('289DF',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('362F8',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('91E48',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('DE535',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('FEDDF',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0EF03',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('452BF',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('A1674',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('B4491',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('06F59',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('DC11B',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('5957B',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('C2115',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('45713',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('8BEFB',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('7297F',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('EBA9F',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('AE897',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('9CA90',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('6A61D',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('FFEED',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('B0CED',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('FD2DB',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('481D6',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('E4A62',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('C3FDC',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('6C095',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('B4FD1',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('C2D89',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('9029D',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0CED3',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0EC52',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('AAF92',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('93B11',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('A1678',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('1E007',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0D01F',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('6EFD2',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('8C805',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('F23D1',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('33267',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('E4A86',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('65981',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('7B780',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('7B497',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('7EF47',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('9B72E',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0D22D',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0D399',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('72FB2',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('671F0',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('50F0A',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('5E72A',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('5A4B2',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('2D542',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D6973',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('039C7',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('366C9',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D709D',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D7524',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('391E1',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('967C2',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('4053E',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('2E02C',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('54963',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('ED777',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('E02AF',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('ADE55',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('C5AB6',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('089D3',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('46BEC',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('152AA',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('59B90',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('F5DFF',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('FD904',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('7F018',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('A725C',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('C0AD6',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('754D8',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('20CF7',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('6B676',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('01DD6',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('44CFE',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('79758',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('06965',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('90838',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('E0070',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D5DA2',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D5B9F',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('25D80',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('583D5',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('64EC9',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('22CCA',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('DEE76',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('B1F39',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D6429',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('CA03D',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('F054B',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('BA545',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('23C84',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('FCDF2',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0D5BF',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('3D2C3',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('21E04',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('86C68',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('3A1F8',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('64223',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('33933',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('B520F',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('F4552',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('234B9',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('ECA85',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('99BCF',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('4FA7C',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('DB5F9',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('17AE6',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('C0E19',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('57F04',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('E7010',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('1671F',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('A79C8',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('66894',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('38904',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('715E8',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('D5B2B',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('635B6',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('A8E86',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('42BA5',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('78139',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('E6D80',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('38616',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('69706',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('0F6B1',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('A74E8',0,'',0.00,'','2021-05-31 12:29:39','2021-05-31 12:29:39'),('C944F',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('9C453',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('E58A9',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('2C80E',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('32621',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('C5CC1',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('22108',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('CA49D',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('4734B',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('5434D',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('86D24',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('9682C',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('21E8C',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('90B8E',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('FBE9E',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('A1035',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('CCA19',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('490EF',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('04378',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('ED8A8',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('08FA4',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('000E8',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('7DC75',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('0871A',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('E3140',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('512B6',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('CC0A5',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('EB999',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('35614',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('E0126',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('1CEB3',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('906F1',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('4625D',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('7A398',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('C4D45',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('79013',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('CA7E0',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('33E9D',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('291DB',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('324E9',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('2F181',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('F2AAA',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('D1D6A',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('70625',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('9B7ED',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('5F69E',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('4B58D',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('22EDA',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('71DD8',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('41E62',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('98C24',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('51DA8',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('DCE4D',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('A4666',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('68C86',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('DA60B',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('9A468',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('8B71C',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('770F8',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('10527',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('A8D7D',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('E22DD',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('351B6',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('164F4',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('A2639',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('DCD3F',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('8D887',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('0EB7A',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('BB44C',0,'',0.00,'','2021-05-31 12:29:48','2021-05-31 12:29:48'),('76759',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('AD809',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('66E06',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('A59A9',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('86EDC',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('EDB63',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('305DD',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('8F64C',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('5B85F',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('8F5F3',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('C1B8C',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('0BFAD',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('7C365',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('550B1',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('1A375',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('3E250',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('B4FD2',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('3A824',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('18C7C',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('336E4',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('E287F',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('9F7A8',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('CAF14',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('AD703',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('C4C42',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('227BD',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('D8C9D',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('0CDBB',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('59BCD',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('D9C9A',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('3F989',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('CCD2D',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('D6F05',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('CDF03',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('89B9E',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('7CD5A',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('D2935',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('5FCC6',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('643D5',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('94C28',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('828C3',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('3871B',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('DFF22',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('A979C',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('52426',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('11F64',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('4FDAA',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('1CDBC',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('DBEC0',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('A75A8',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('FE840',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('DBAFB',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('0A30A',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('37CB3',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('C0AA6',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('D62BC',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('6A444',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('1E505',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('A501B',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('0D49A',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('F91EE',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('54C3D',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('6F16F',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('A08FE',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('52EDC',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('18D32',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('5B768',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('1C549',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('7A8CE',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('ECE15',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('F2C5B',0,'',0.00,'','2021-05-31 12:29:49','2021-05-31 12:29:49'),('BD3B9',502,'FL',1000.00,'','2021-06-01 12:55:56','2021-06-01 15:46:14'),('609AC',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('5F045',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('80E0A',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('A957A',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('11BCD',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('B02E5',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('482B3',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('F5F3B',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('2711A',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('CBA8E',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('48BB5',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('DACD9',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('3A30B',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('67405',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('61745',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('C6D73',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('7FCB0',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('EB3C3',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('24322',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('F126A',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('3ACA5',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('FCF1D',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('28746',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('384B8',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('128AC',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('19DC1',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('50959',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('CF513',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('C5005',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('79001',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('43FC9',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('1577E',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('9B06D',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('54B14',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('1D9AA',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('92262',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('9E29F',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('685D3',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('5D913',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('3152E',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('4EFA8',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('A91A5',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('98B17',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('E2549',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('66F04',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('4CFBF',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('4F5C4',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('BA8E2',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('931A7',0,'',0.00,'','2021-06-01 13:56:04','2021-06-01 13:56:04'),('F226F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('4AB52',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('B9B95',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('32CE0',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('164BF',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('36A3C',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('7F55D',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('536B0',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('3E3DA',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('E3ACB',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('D4726',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('FFB43',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('28B43',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('F8139',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('47F17',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('9CC1C',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('4EFC9',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('3A2EE',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('C02B1',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('45CEF',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('16ED2',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('2ED08',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('10B2B',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('0E3FE',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('AC450',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('B43C5',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('A7F0D',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('B4189',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('F46B2',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('DAA17',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('1833A',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('6D4E5',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('77DF7',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('5CBBA',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('D840C',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('3F5EE',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('258BE',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('7EC12',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('874E3',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('E47D7',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('6508D',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('4E62E',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('B27D5',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('999DF',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('EC4B2',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('DC563',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('2890F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('ABFFB',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('68FF0',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('0251A',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('12365',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('CA70E',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('8EEFC',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('2DB9F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('4B009',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('451EE',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('D3802',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('A1A93',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('DDB1B',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('5FBCF',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('0188E',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('EF354',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('0D14E',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('758A0',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('7F5A1',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('0B7FD',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('C6455',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('4F67F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('DC7E9',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('8E14F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('C68F7',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('2326E',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('3D22F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('0E87A',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('1A15D',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('085CC',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('3C0CD',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('DD458',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('410A0',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('8A66C',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('9DCB6',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('9824F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('1FC3F',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('95060',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('9B2E0',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('2D5F5',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('D26BE',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('62854',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('698D5',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('35468',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('E6ACF',0,'',0.00,'','2021-06-01 13:56:05','2021-06-01 13:56:05'),('8e+34',502,'FL',1000.00,'','2021-06-01 14:23:36','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:46:50','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:48:26','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:48:57','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:49:26','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:50:21','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:50:55','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:51:33','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:52:01','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:52:34','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:53:17','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:54:08','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:54:40','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:55:12','2021-06-01 15:46:14'),('2580',502,'FL',1000.00,'','2021-06-01 14:55:12','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:56:01','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:57:12','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:57:41','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 14:58:11','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:02:02','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:04:25','2021-06-01 15:46:14'),('3276F',502,'FL',10000.00,'','2021-06-01 15:04:25','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:06:21','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:06:52','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:07:22','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:08:36','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:11:16','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:11:52','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:12:42','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:13:35','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:14:13','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:14:49','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:15:26','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:15:26','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:16:24','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:16:24','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:17:10','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:17:10','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:17:48','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:17:48','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:18:24','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:18:24','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:18:53','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:18:53','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:19:53','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:19:53','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:20:35','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:20:35','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:20:58','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:20:58','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:21:53','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:21:53','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:22:29','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:22:29','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:23:18','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:23:18','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:24:32','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:24:32','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:29:25','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:29:25','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:35:12','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:35:12','2021-06-01 15:46:14'),('22800',502,'FL',1000.00,'','2021-06-01 15:46:13','2021-06-01 15:46:14'),('60000',502,'FL',1000.00,'','2021-06-01 15:46:13','2021-06-01 15:46:14'),('S2AAA',502,'FL',1000.00,'','2021-06-01 15:46:14','2021-06-01 15:46:14');
/*!40000 ALTER TABLE `product_stocks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` char(4) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'active',
  `particular` varchar(50) DEFAULT ' ',
  `length` decimal(7,3) DEFAULT NULL,
  `width` decimal(7,3) DEFAULT NULL,
  `thickness` decimal(7,3) NOT NULL,
  `type` char(3) NOT NULL COMMENT 'IMP - Import, LOC -  Local',
  `allowance` int(11) NOT NULL,
  `part_no` varchar(30) DEFAULT ' ',
  `unit` varchar(10) DEFAULT ' ',
  `description` varchar(255) DEFAULT ' ',
  `capital` decimal(8,2) DEFAULT NULL,
  `markup` decimal(8,2) DEFAULT NULL,
  `srp` decimal(8,2) DEFAULT NULL,
  `tmp_srp` decimal(10,2) NOT NULL,
  `soh_quantity` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `min_quantity` int(11) DEFAULT NULL,
  `max_quantity` int(11) DEFAULT NULL,
  `discountable` tinyint(1) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=822 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES (1,'PLT8','active','AS ACRYLIC  CLR',1219.200,2438.400,3.000,'IMP',5,'','mm','AS ACRYLIC CLR',0.00,0.00,0.00,500.00,2863407,500,0,0,0,'2020-02-27 01:23:45','2021-06-01 13:05:37'),(2,'PLT8','active','AS ACRYLIC  CLR',1219.200,2438.400,5.000,'IMP',5,'','mm','AS ACRYLIC CLR',0.00,0.00,0.00,100.00,2972642,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:36:13'),(3,'PLT8','active','AS ACRYLIC  CLR',1219.200,2438.400,6.000,'IMP',5,'','mm','AS ACRYLIC CLR',0.00,0.00,0.00,50.00,2972895,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 12:50:26'),(4,'PLT8','active','AS ACRYLIC  CLR',1219.200,2438.400,10.000,'IMP',5,'','mm','AS ACRYLIC CLR',0.00,0.00,0.00,50.00,2971897,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:36:34'),(5,'PLT8','active','AS PC  CLR',1219.200,2438.400,2.000,'IMP',5,'','mm','AS PC CLR',0.00,0.00,0.00,500.00,2803971,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:37:39'),(6,'PLT8','active','AS PC  CLR',1219.200,2438.400,3.000,'IMP',5,'','mm','AS PC CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:37:56'),(7,'PLT8','active','AS PC CLR',1219.200,2438.400,4.000,'IMP',5,'','mm','AS PC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:38:15'),(8,'PLT8','active','AS PC CLR',1219.200,2438.400,5.000,'IMP',5,'','mm','AS PC CLR',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:38:46'),(9,'PLT8','active','AS PC CLR',1219.200,2438.400,6.000,'IMP',5,'','mm','AS PC CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:39:32'),(10,'PLT8','active','AS PC CLR',1219.200,2438.400,10.000,'IMP',5,'','mm','AS PC CLR',0.00,0.00,0.00,1000.00,-308994,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:39:41'),(11,'PLT8','active','PET  CLEAR',1200.000,2000.000,3.000,'IMP',5,'','mm','PET  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-28 12:14:37'),(12,'PLT8','active','PET  CLEAR',1200.000,2000.000,5.000,'IMP',5,'','mm','PET CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:40:07'),(13,'PLT8','active','PET  CLEAR',1200.000,2000.000,6.000,'IMP',5,'','mm','PET  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:40:16'),(14,'PLT8','active','PC   CLEAR',1219.200,2438.400,2.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:40:48'),(15,'PLT8','active','PC   CLEAR',1219.200,2438.400,3.000,'IMP',5,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:40:54'),(16,'PLT8','active','PC   CLEAR',1219.200,2438.400,4.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:41:11'),(17,'PLT8','active','PC   CLEAR',1219.200,2438.400,5.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:41:21'),(18,'PLT8','active','PC   CLEAR',1219.200,2438.400,6.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:41:31'),(19,'PLT8','active','PC   CLEAR',1219.200,2438.400,8.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:41:40'),(20,'PLT8','active','PC   CLEAR',1219.200,2438.400,10.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,-10000,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:42:07'),(21,'PLT8','active','PC   CLEAR',1219.200,2438.400,12.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:20'),(22,'PLT8','active','PC   CLEAR',1219.200,2438.400,15.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:42:31'),(23,'PLT8','active','PC   CLEAR',1219.200,2438.400,20.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:42:55'),(24,'PLT8','active','PC   CLEAR',1219.200,2438.400,20.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:43:30'),(25,'PLT8','active','PC   CLEAR',1219.200,2438.400,25.000,'IMP',5,'','mm','PC   CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:43:39'),(26,'PLT8','active','PVC  CLEAR',1219.200,2438.400,2.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:43:50'),(27,'PLT8','active','PVC  CLEAR',1219.200,2438.400,3.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:43:59'),(28,'PLT8','active','PVC  CLEAR',1219.200,2438.400,5.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:04'),(29,'PLT8','active','PVC  CLEAR',1219.200,2438.400,6.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:10'),(30,'PLT8','active','PVC  CLEAR',1219.200,2438.400,8.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:15'),(31,'PLT8','active','PVC  CLEAR',1219.200,2438.400,10.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:31'),(32,'PLT8','active','PVC  CLEAR',1219.200,2438.400,12.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:36'),(33,'PLT8','active','PVC  CLEAR',1219.200,2438.400,15.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:41'),(34,'PLT8','active','PVC  CLEAR',1219.200,2438.400,20.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:45'),(35,'PLT8','active','PVC  CLEAR',1219.200,2438.400,25.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:45:17'),(36,'PLT8','active','PVC  CLEAR',1219.200,2438.400,30.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:44:55'),(37,'PLT8','active','PVC  CLEAR',1000.000,2000.000,40.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:45:29'),(38,'PLT8','active','PVC  CLEAR',1000.000,2000.000,50.000,'IMP',5,'','mm','PVC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:45:34'),(39,'PLT8','active','ABS  IVORY ',1000.000,2000.000,3.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:45:53'),(40,'PLT8','active','ABS  IVORY ',1000.000,2000.000,5.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,1500.00,-129500,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:45:59'),(41,'PLT8','active','ABS  IVORY ',1000.000,2000.000,6.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:46:05'),(42,'PLT8','active','ABS  IVORY ',1000.000,2000.000,8.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:46:16'),(43,'PLT8','active','ABS  IVORY ',1000.000,2000.000,10.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:46:21'),(44,'PLT8','active','ABS  IVORY ',1000.000,2000.000,12.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:46:26'),(45,'PLT8','active','ABS  IVORY ',1000.000,2000.000,15.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:46:36'),(46,'PLT8','active','ABS  IVORY ',1000.000,2000.000,20.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:47:07'),(47,'PLT8','active','ABS  IVORY ',1200.000,2000.000,25.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:47:12'),(48,'PLT8','active','ABS  IVORY ',1200.000,2000.000,30.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:46:47'),(49,'PLT8','active','ABS  IVORY ',1200.000,2000.000,35.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:46:43'),(50,'PLT8','active','ABS  IVORY ',1200.000,2000.000,40.000,'IMP',5,'','mm','ABS  IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:47:23'),(51,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,2.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:32:44'),(52,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,3.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:32:49'),(53,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,4.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:32:55'),(54,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,4.600,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:32:59'),(55,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,5.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,-11427103,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:32:23'),(56,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,6.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:32:39'),(57,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,8.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:33:09'),(58,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,10.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:33:13'),(59,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,12.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:33:16'),(60,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,18.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,55.00,-21504,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 18:01:36'),(61,'PLT8','active','ACRYLIC  CLR',1219.200,2438.400,25.000,'IMP',5,'','mm','ACRYLIC  CLR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:33:24'),(62,'PLT8','active','BAKELITE',1000.000,2000.000,2.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:45:54'),(63,'PLT8','active','BAKELITE',1000.000,2000.000,3.000,'LOC',5,'','mm','BKLT',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:45:50'),(64,'PLT8','active','BAKELITE',1000.000,2000.000,4.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:46:13'),(65,'PLT8','active','BAKELITE',1000.000,2000.000,5.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:46:25'),(66,'PLT8','active','BAKELITE',1000.000,2000.000,6.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:46:34'),(67,'PLT8','active','BAKELITE',1000.000,2000.000,8.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:46:50'),(68,'PLT8','active','BAKELITE',1000.000,2000.000,10.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,50.00,-1005313,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:46:58'),(69,'PLT8','active','BAKELITE',1000.000,2000.000,12.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,185.00,-13904,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:47:06'),(70,'PLT8','active','BAKELITE',1000.000,2000.000,15.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,350.00,-20000,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:47:14'),(71,'PLT8','active','BAKELITE',1000.000,2000.000,20.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,220.00,-1023680,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:47:30'),(72,'PLT8','active','BAKELITE',1000.000,2000.000,25.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,580.00,-20000,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:47:43'),(73,'PLT8','active','BAKELITE',1000.000,2000.000,30.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:47:51'),(74,'PLT8','active','BAKELITE',1000.000,1000.000,40.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,930.00,-20000,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:47:58'),(75,'PLT8','active','BAKELITE',1000.000,1000.000,50.000,'IMP',5,'','mm','BKLT',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:48:05'),(76,'PLT8','active','CDM  BLK',1219.200,2438.400,3.000,'IMP',5,'','mm','CDM  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:52:28'),(77,'PLT8','active','CDM  BLK',1150.000,1250.000,4.000,'IMP',5,'','mm','CDM  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:53:31'),(78,'PLT8','active','CDM  BLK',1150.000,1250.000,5.000,'IMP',5,'','mm','CDM  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:53:37'),(79,'PLT8','active','CDM  BLK',1150.000,1250.000,6.000,'IMP',5,'','mm','CDM  BLK',0.00,0.00,0.00,0.00,-250000,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:53:44'),(80,'PLT8','active','CDM  BLK',1150.000,1250.000,8.000,'IMP',5,'','mm','CDM  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:17:12'),(81,'PLT8','active','CDM  BLK',1150.000,1250.000,10.000,'IMP',5,'','mm','CDM  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:04:27'),(82,'PLT8','active','CDM  BLK',1150.000,1250.000,12.000,'IMP',5,'','mm','CDM  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:14:47'),(83,'PLT8','active','CDM  BLK',1150.000,1250.000,15.000,'IMP',5,'','mm','CDM  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:14:52'),(84,'PLT8','active','CDM  BLK',1150.000,1250.000,20.000,'IMP',5,'','mm','CDM  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:04:42'),(85,'PLT8','active','CDR6  BLK',600.000,1200.000,5.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 16:58:33'),(86,'PLT8','active','CDR6  BLK',600.000,1200.000,6.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 16:58:44'),(87,'PLT8','active','CDR6  BLK',600.000,1200.000,8.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 16:58:38'),(88,'PLT8','active','CDR6  BLK',600.000,1200.000,10.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,920.00,-188496,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:53:25'),(89,'PLT8','active','CDR6  BLK',600.000,1200.000,12.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 16:59:00'),(90,'PLT8','active','CDR6  BLK',600.000,1200.000,16.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 16:59:04'),(91,'PLT8','active','CDR6  BLK',600.000,1200.000,20.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:43:42'),(92,'PLT8','active','CDR6  BLK',600.000,1200.000,25.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:29:49'),(93,'PLT8','active','CDR6  BLK',600.000,1200.000,30.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:29:55'),(94,'PLT8','active','CDR6  BLK',600.000,1200.000,40.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 12:35:08'),(95,'RODS','active','CDR6  BLK',1000.000,NULL,8.000,'IMP',5,'','mm','CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 11:28:46'),(96,'RODS','active','CDR6  BLK',1000.000,NULL,10.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:30:50'),(97,'RODS','active','CDR6  BLK',1000.000,NULL,12.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:31:03'),(98,'RODS','active','CDR6  BLK',1000.000,NULL,15.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:31:13'),(99,'RODS','active','CDR6  BLK',1000.000,NULL,20.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:31:26'),(100,'RODS','active','CDR6  BLK',1000.000,NULL,30.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:31:33'),(101,'RODS','active','CDR6  BLK',1000.000,NULL,40.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:32:06'),(102,'RODS','active','CDR6  BLK',1000.000,NULL,50.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:32:18'),(103,'RODS','active','CDR6  BLK',1000.000,NULL,60.000,'IMP',5,'','mm','CDR6  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:32:26'),(104,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,2.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:43:55'),(105,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,3.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:43:57'),(106,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,4.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:34:48'),(107,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,5.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:34:54'),(108,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,6.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,-4,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:07'),(109,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,8.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:12'),(110,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,10.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:20'),(111,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,12.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:27'),(112,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,15.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:32'),(113,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,20.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:38'),(114,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,25.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:43'),(115,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,30.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,700.00,-61509,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:44:03'),(116,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,35.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:35:52'),(117,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,40.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:40:13'),(118,'PLT8','active','DELRIN  WHITE ',1000.000,2000.000,45.000,'IMP',5,'','mm','DELRIN  W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:37:09'),(119,'PLT8','active','DELRIN  WHITE ',600.000,1200.000,50.000,'IMP',5,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:09:55'),(120,'PLT8','active','DELRIN  WHITE ',600.000,1200.000,60.000,'IMP',5,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:10:31'),(121,'PLT8','active','DELRIN  WHITE ',600.000,1200.000,70.000,'IMP',5,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:14:34'),(122,'PLT8','active','DELRIN  WHITE ',600.000,1219.200,80.000,'IMP',5,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:10:49'),(123,'PLT8','active','DELRIN  WHITE ',600.000,1219.200,100.000,'IMP',5,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:14:29'),(124,'RODS','active','DELRIN WHITE ',1000.000,NULL,6.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:56:11'),(125,'RODS','active','DELRIN WHITE ',1000.000,NULL,8.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:56:20'),(126,'RODS','active','DELRIN WHITE ',1000.000,NULL,10.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:55:12'),(127,'RODS','active','DELRIN WHITE ',1000.000,NULL,12.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:55:20'),(128,'RODS','active','DELRIN WHITE ',1000.000,NULL,15.000,'IMP',0,'','mm','DELRIN (WH',0.00,0.00,0.00,155.00,-1000,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 14:20:57'),(129,'RODS','active','DELRIN WHITE ',1000.000,NULL,20.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:55:48'),(130,'RODS','active','DELRIN WHITE ',1000.000,NULL,25.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:56:02'),(131,'RODS','active','DELRIN WHITE ',1000.000,NULL,30.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:53:16'),(132,'RODS','active','DELRIN WHITE ',1000.000,NULL,35.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,770.00,-1540,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:18:19'),(133,'RODS','active','DELRIN WHITE ',1000.000,NULL,40.000,'IMP',5,'','mm','DELRIN WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 12:33:24'),(134,'RODS','active','DELRIN WHITE ',1000.000,NULL,45.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,115.00,-90,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:42:22'),(135,'RODS','active','DELRIN WHITE ',1000.000,NULL,50.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:53:44'),(136,'RODS','active','DELRIN WHITE ',1000.000,NULL,55.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:53:54'),(137,'RODS','active','DELRIN WHITE ',1000.000,NULL,60.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:54:02'),(138,'RODS','active','DELRIN WHITE ',1000.000,NULL,65.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:54:10'),(139,'RODS','active','DELRIN WHITE ',1000.000,NULL,70.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:54:17'),(140,'RODS','active','DELRIN WHITE ',1000.000,NULL,75.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:54:25'),(141,'RODS','active','DELRIN WHITE ',1000.000,NULL,80.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:56:48'),(142,'RODS','active','DELRIN WHITE ',1000.000,NULL,85.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:56:56'),(143,'RODS','active','DELRIN WHITE ',1000.000,NULL,90.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,655.00,-348,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:42:22'),(144,'RODS','active','DELRIN WHITE ',1000.000,NULL,95.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:57:30'),(145,'RODS','active','DELRIN WHITE ',1000.000,NULL,100.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:57:25'),(146,'RODS','active','DELRIN WHITE ',1000.000,NULL,110.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:57:49'),(147,'RODS','active','DELRIN WHITE ',1000.000,NULL,120.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:57:56'),(148,'RODS','active','DELRIN WHITE ',1000.000,NULL,130.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:58:03'),(149,'RODS','active','DELRIN WHITE ',1000.000,NULL,140.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:58:23'),(150,'RODS','active','DELRIN WHITE ',1000.000,NULL,145.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:58:37'),(151,'RODS','active','DELRIN WHITE ',1000.000,NULL,150.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:59:54'),(152,'RODS','active','DELRIN WHITE ',1000.000,NULL,160.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:59:57'),(153,'RODS','active','DELRIN WHITE ',1000.000,NULL,170.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:00:00'),(154,'RODS','active','DELRIN WHITE ',1000.000,NULL,180.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:59:35'),(155,'RODS','active','DELRIN WHITE ',1000.000,NULL,190.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 12:59:42'),(156,'RODS','active','DELRIN WHITE ',1000.000,NULL,200.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:00:04'),(157,'RODS','active','DELRIN WHITE ',1000.000,NULL,220.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:00:11'),(158,'RODS','active','DELRIN WHITE ',1000.000,NULL,250.000,'IMP',5,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:00:20'),(159,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,5.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:00:24'),(160,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,6.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 12:39:10'),(161,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,8.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:24:34'),(162,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,10.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:20:43'),(163,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,12.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:20:49'),(164,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,15.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,55.00,-4428,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:37:39'),(165,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,20.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:21:02'),(166,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,25.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:21:08'),(167,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,30.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:21:14'),(168,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,35.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:21:38'),(169,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,40.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:40:40'),(170,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,45.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:21:59'),(171,'PLT8','active','DELRIN  BLACK ',1000.000,2000.000,50.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:26:07'),(172,'PLT8','active','DELRIN  BLACK ',600.000,1219.200,60.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:24:47'),(173,'PLT8','active','DELRIN  BLACK ',600.000,1219.200,80.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:16:19'),(174,'PLT8','active','DELRIN  BLACK ',600.000,1000.000,100.000,'IMP',5,'','mm','DELRIN  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:16:12'),(175,'RODS','active','DELRIN BLACK ',1000.000,NULL,6.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:15:51'),(176,'RODS','active','DELRIN BLACK ',1000.000,NULL,8.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:15:44'),(177,'RODS','active','DELRIN BLACK ',1000.000,NULL,10.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:15:33'),(178,'RODS','active','DELRIN BLACK ',1000.000,NULL,12.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:15:26'),(179,'RODS','active','DELRIN BLACK ',1000.000,NULL,15.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:14:49'),(180,'RODS','active','DELRIN BLACK ',1000.000,NULL,20.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:14:41'),(181,'RODS','active','DELRIN BLACK ',1000.000,NULL,25.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:13:14'),(182,'RODS','active','DELRIN BLACK ',1000.000,NULL,30.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:55:06'),(183,'RODS','active','DELRIN BLACK ',1000.000,NULL,40.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:40:51'),(184,'RODS','active','DELRIN BLACK ',1000.000,NULL,45.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:55:13'),(185,'RODS','active','DELRIN BLACK ',1000.000,NULL,50.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:55:25'),(186,'RODS','active','DELRIN BLACK ',1000.000,NULL,55.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:06:42'),(187,'RODS','active','DELRIN BLACK ',1000.000,NULL,60.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:13:49'),(188,'RODS','active','DELRIN BLACK ',1000.000,NULL,65.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:13:56'),(189,'RODS','active','DELRIN BLACK ',1000.000,NULL,70.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:14:03'),(190,'RODS','active','DELRIN BLACK ',1000.000,NULL,75.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:14:11'),(191,'RODS','active','DELRIN BLACK ',1000.000,NULL,80.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:53:53'),(192,'RODS','active','DELRIN BLACK ',1000.000,NULL,85.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:54:01'),(193,'RODS','active','DELRIN BLACK ',1000.000,NULL,90.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:53:45'),(194,'RODS','active','DELRIN BLACK ',1000.000,NULL,95.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:54:22'),(195,'RODS','active','DELRIN BLACK ',1000.000,NULL,100.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:53:38'),(196,'RODS','active','DELRIN BLACK ',1000.000,NULL,110.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:53:30'),(197,'RODS','active','DELRIN BLACK ',1000.000,NULL,120.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:53:22'),(198,'RODS','active','DELRIN BLACK ',1000.000,NULL,130.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:53:15'),(199,'RODS','active','DELRIN BLACK ',1000.000,NULL,140.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:53:07'),(200,'RODS','active','DELRIN BLACK ',1000.000,NULL,145.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 14:16:36'),(201,'RODS','active','DELRIN BLACK ',1000.000,NULL,150.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:50:59'),(202,'RODS','active','DELRIN BLACK ',1000.000,NULL,160.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:51:07'),(203,'RODS','active','DELRIN BLACK ',1000.000,NULL,170.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:51:17'),(204,'RODS','active','DELRIN BLACK ',1000.000,NULL,180.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:51:26'),(205,'RODS','active','DELRIN BLACK ',1000.000,NULL,190.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:51:34'),(206,'RODS','active','DELRIN BLACK ',1000.000,NULL,200.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:52:40'),(207,'RODS','active','DELRIN BLACK ',1000.000,NULL,220.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:51:48'),(208,'RODS','active','DELRIN BLACK ',1000.000,NULL,250.000,'IMP',5,'','mm','DELRIN BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-22 13:52:01'),(209,'PLT8','active','DELRIN  BLUE',600.000,1200.000,10.000,'IMP',5,'','mm','DELRIN  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:44:12'),(210,'PLT8','active','DELRIN  BLUE',600.000,1200.000,15.000,'IMP',5,'','mm','DELRIN  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:44:16'),(211,'PLT8','active','DELRIN  BLUE',600.000,1200.000,25.000,'IMP',5,'','mm','DELRIN  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:44:25'),(212,'PLT8','active','G10 GREEN',1000.000,1200.000,0.500,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:44:56'),(213,'PLT8','active','G10 GREEN',1000.000,1200.000,0.800,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:45:58'),(214,'PLT8','active','G10 GREEN',1000.000,1200.000,1.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:46:06'),(215,'PLT8','active','G10 GREEN',1000.000,1200.000,0.900,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:46:14'),(216,'PLT8','active','G10 GREEN',1000.000,1200.000,2.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,-250001,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:46:24'),(217,'PLT8','active','G10 GREEN',1000.000,1200.000,3.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:46:36'),(218,'PLT8','active','G10 GREEN',1000.000,1200.000,4.500,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:46:44'),(219,'PLT8','active','G10 GREEN',1000.000,1200.000,5.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:46:54'),(220,'PLT8','active','G10 GREEN',1000.000,1200.000,6.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:47:02'),(221,'PLT8','active','G10 GREEN',1000.000,1200.000,8.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:47:12'),(222,'PLT8','active','G10 GREEN',1000.000,1200.000,10.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:47:20'),(223,'PLT8','active','G10 GREEN',1000.000,1200.000,12.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:47:28'),(224,'PLT8','active','G10 GREEN',1000.000,1200.000,15.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:47:38'),(225,'PLT8','active','G10 GREEN',1000.000,1200.000,20.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:47:46'),(226,'PLT8','active','G10 GREEN',1000.000,1200.000,25.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:47:54'),(227,'PLT8','active','G10 GREEN',1000.000,1200.000,30.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:48:02'),(228,'PLT8','active','G10 GREEN',1000.000,1200.000,40.000,'IMP',5,'','mm','G10 GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 14:48:09'),(229,'PLT8','active','G11 YELLOW ',1200.000,1200.000,3.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:00:13'),(230,'PLT8','active','G11 YELLOW ',1200.000,1200.000,4.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:34:56'),(231,'PLT8','active','G11 YELLOW ',1200.000,1200.000,5.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:37:26'),(232,'PLT8','active','G11 YELLOW ',1200.000,1200.000,5.500,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:35:37'),(233,'PLT8','active','G11 YELLOW ',1200.000,1200.000,8.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:35:44'),(234,'PLT8','active','G11 YELLOW ',1200.000,1200.000,10.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:35:50'),(235,'PLT8','active','G11 YELLOW ',1000.000,1200.000,12.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:35:56'),(236,'PLT8','active','G11 YELLOW ',1000.000,1200.000,15.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:36:02'),(237,'PLT8','active','G11 YELLOW ',1000.000,1200.000,20.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:36:29'),(238,'PLT8','active','G11 YELLOW ',1000.000,1200.000,25.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:36:32'),(239,'PLT8','active','G11 YELLOW ',1000.000,1200.000,30.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:37:07'),(240,'PLT8','active','G11 YELLOW ',1000.000,1200.000,40.000,'IMP',5,'','mm','G11 YEL ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 10:36:56'),(241,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,6.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:00:31'),(242,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,8.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:00:36'),(243,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,10.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:00:43'),(244,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,12.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,50.00,-5401,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:00:47'),(245,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,15.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:00:52'),(246,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,20.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:12'),(247,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,25.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:01:06'),(248,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,30.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:01:15'),(249,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,40.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:01:29'),(250,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,50.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:01:34'),(251,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,60.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:26'),(252,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,80.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:30'),(253,'PLT8','active','NYLON  BLUE ',1000.000,2000.000,100.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:34'),(254,'RODS','active','NYLON  BLUE ',1000.000,NULL,6.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:03:54'),(255,'RODS','active','NYLON  BLUE ',1000.000,NULL,8.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:39'),(256,'RODS','active','NYLON  BLUE ',1000.000,NULL,10.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:44'),(257,'RODS','active','NYLON  BLUE ',1000.000,NULL,12.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:51'),(258,'RODS','active','NYLON  BLUE ',1000.000,NULL,15.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:02:59'),(259,'RODS','active','NYLON  BLUE ',1000.000,NULL,20.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:03:36'),(260,'RODS','active','NYLON  BLUE ',1000.000,NULL,25.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,115.00,-215,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:03:42'),(261,'RODS','active','NYLON  BLUE ',1000.000,NULL,30.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:12'),(262,'RODS','active','NYLON  BLUE ',1000.000,NULL,35.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:16'),(263,'RODS','active','NYLON  BLUE ',1000.000,NULL,40.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:21'),(264,'RODS','active','NYLON  BLUE ',1000.000,NULL,45.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:05:11'),(265,'RODS','active','NYLON  BLUE ',1000.000,NULL,50.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:28'),(266,'RODS','active','NYLON  BLUE ',600.000,0.000,45.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:31'),(267,'RODS','active','NYLON  BLUE ',500.000,0.000,50.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:41'),(268,'RODS','active','NYLON  BLUE ',600.000,0.000,50.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:45'),(269,'RODS','active','NYLON  BLUE ',600.000,0.000,55.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:49'),(270,'RODS','active','NYLON  BLUE ',600.000,0.000,60.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:04:53'),(271,'RODS','active','NYLON  BLUE ',600.000,0.000,65.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:05:24'),(272,'RODS','active','NYLON  BLUE ',600.000,0.000,70.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:05:27'),(273,'RODS','active','NYLON  BLUE ',1000.000,NULL,70.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:05:31'),(274,'RODS','active','NYLON  BLUE ',1000.000,NULL,75.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:05:36'),(275,'RODS','active','NYLON  BLUE ',1000.000,NULL,80.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:05:39'),(276,'RODS','active','NYLON  BLUE ',1000.000,NULL,90.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:05:49'),(277,'RODS','active','NYLON  BLUE ',1000.000,NULL,100.000,'IMP',5,'','mm','NYLON  BLU ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:26:45'),(278,'RODS','active','NYLON  BLUE ',1000.000,NULL,110.000,'IMP',5,'','mm','NYLON  BLU ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:26:52'),(279,'RODS','active','NYLON  BLUE ',1000.000,NULL,115.000,'IMP',5,'','mm','NYLON  BLU ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:27:06'),(280,'RODS','active','NYLON  BLUE ',1000.000,NULL,120.000,'IMP',5,'','mm','NYLON  BLU ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:27:13'),(281,'RODS','active','NYLON  BLUE ',1000.000,NULL,130.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:01'),(282,'RODS','active','NYLON  BLUE ',1000.000,NULL,140.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:10'),(283,'RODS','active','NYLON  BLUE ',1000.000,NULL,150.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:07:08'),(284,'RODS','active','NYLON  BLUE ',1000.000,NULL,160.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:14'),(285,'RODS','active','NYLON  BLUE ',1000.000,NULL,170.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:20'),(286,'RODS','active','NYLON  BLUE ',1000.000,NULL,180.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:23'),(287,'RODS','active','NYLON  BLUE ',1000.000,NULL,190.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:27'),(288,'RODS','active','NYLON  BLUE ',1000.000,NULL,200.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:57'),(289,'RODS','active','NYLON  BLUE ',1000.000,NULL,210.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:36'),(290,'RODS','active','NYLON  BLUE ',1000.000,NULL,220.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:06:42'),(291,'RODS','active','NYLON  BLUE ',1000.000,NULL,250.000,'IMP',5,'','mm','NYLON  BLUE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 12:33:43'),(292,'RODS','active','NYLON IVORY ',1000.000,NULL,6.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,-1000,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:40:52'),(293,'RODS','active','NYLON IVORY ',1000.000,NULL,8.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:40:58'),(294,'RODS','active','NYLON IVORY ',1000.000,NULL,10.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:41:29'),(295,'RODS','active','NYLON IVORY ',1000.000,NULL,12.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:41:35'),(296,'RODS','active','NYLON IVORY ',1000.000,NULL,15.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:41:41'),(297,'RODS','active','NYLON IVORY ',1000.000,NULL,20.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:41:46'),(298,'RODS','active','NYLON IVORY ',1000.000,NULL,25.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:41:53'),(299,'RODS','active','NYLON IVORY ',1000.000,NULL,30.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:41:58'),(300,'RODS','active','NYLON IVORY ',1000.000,NULL,35.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:16'),(301,'RODS','active','NYLON IVORY ',1000.000,NULL,40.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:40:28'),(302,'RODS','active','NYLON IVORY ',1000.000,NULL,45.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:40:34'),(303,'RODS','active','NYLON IVORY ',1000.000,NULL,50.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:26'),(304,'RODS','active','NYLON IVORY ',1000.000,NULL,55.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:31'),(305,'RODS','active','NYLON IVORY ',1000.000,NULL,60.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:37'),(306,'RODS','active','NYLON IVORY ',1000.000,NULL,70.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:42'),(307,'RODS','active','NYLON IVORY ',1000.000,NULL,75.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:48'),(308,'RODS','active','NYLON IVORY ',1000.000,NULL,80.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:53'),(309,'RODS','active','NYLON IVORY ',1000.000,NULL,90.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:42:59'),(310,'RODS','active','NYLON IVORY ',1000.000,NULL,100.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:43:05'),(311,'RODS','active','NYLON IVORY ',1000.000,NULL,110.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:43:52'),(312,'RODS','active','NYLON IVORY ',1000.000,NULL,120.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:43:59'),(313,'RODS','active','NYLON IVORY ',1000.000,NULL,130.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:44:07'),(314,'RODS','active','NYLON IVORY ',1000.000,NULL,140.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:44:34'),(315,'RODS','active','NYLON IVORY ',1000.000,NULL,150.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:44:42'),(316,'RODS','active','NYLON IVORY ',1000.000,NULL,160.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:44:49'),(317,'RODS','active','NYLON IVORY ',1000.000,NULL,170.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:44:58'),(318,'RODS','active','NYLON IVORY ',1000.000,NULL,180.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:45:08'),(319,'RODS','active','NYLON IVORY ',1000.000,NULL,190.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:45:28'),(320,'RODS','active','NYLON IVORY ',1000.000,NULL,200.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:45:48'),(321,'RODS','active','NYLON IVORY ',1000.000,NULL,220.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:45:59'),(322,'RODS','active','NYLON IVORY ',1000.000,NULL,250.000,'IMP',5,'','mm','NYLON IVORY ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:46:09'),(323,'PLT8','active','NYLON IVORY',1000.000,2000.000,5.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:08:52'),(324,'PLT8','active','NYLON IVORY',1000.000,2000.000,6.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:09:17'),(325,'PLT8','active','NYLON IVORY',1000.000,2000.000,8.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:09:26'),(326,'PLT8','active','NYLON IVORY',1000.000,2000.000,10.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:09:45'),(327,'PLT8','active','NYLON IVORY',1000.000,2000.000,12.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:09:54'),(328,'PLT8','active','NYLON IVORY',1000.000,2000.000,15.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:10:03'),(329,'PLT8','active','NYLON IVORY',1000.000,2000.000,20.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:10:30'),(330,'PLT8','active','NYLON IVORY',1000.000,2000.000,25.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:10:39'),(331,'PLT8','active','NYLON IVORY',1000.000,2000.000,30.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:10:54'),(332,'PLT8','active','NYLON IVORY',1000.000,2000.000,40.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:11:02'),(333,'PLT8','active','NYLON IVORY',1000.000,2000.000,50.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:11:09'),(334,'PLT8','active','NYLON IVORY',1000.000,2000.000,60.000,'IMP',5,'','mm','NYLON IVORY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:11:17'),(335,'PLT8','active','NYLON  BLACK',1000.000,2000.000,6.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:00:45'),(336,'PLT8','active','NYLON  BLACK',1000.000,2000.000,8.000,'LOC',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:00:52'),(337,'PLT8','active','NYLON  BLACK',1000.000,2000.000,10.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:01:52'),(338,'PLT8','active','NYLON  BLACK',1000.000,2000.000,12.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:01:24'),(339,'PLT8','active','NYLON  BLACK',1000.000,2000.000,15.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:01:19'),(340,'PLT8','active','NYLON  BLACK',1000.000,2000.000,20.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:01:08'),(341,'PLT8','active','NYLON  BLACK',1000.000,2000.000,30.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:09:26'),(342,'PLT8','active','NYLON  BLACK',1000.000,2000.000,40.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:02:16'),(343,'PLT8','active','NYLON  BLACK',1000.000,2000.000,50.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:02:22'),(344,'PLT8','active','NYLON  BLACK',1000.000,2000.000,60.000,'IMP',5,'','mm','NYLON  BLK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:02:28'),(345,'RODS','active','NYLON BLACK ',1000.000,NULL,6.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:47:52'),(346,'RODS','active','NYLON BLACK ',1000.000,NULL,8.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:48:00'),(347,'RODS','active','NYLON BLACK ',1000.000,NULL,10.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:48:06'),(348,'RODS','active','NYLON BLACK ',1000.000,NULL,12.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:48:12'),(349,'RODS','active','NYLON BLACK ',1000.000,NULL,15.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:48:17'),(350,'RODS','active','NYLON BLACK ',1000.000,NULL,20.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 12:34:12'),(351,'RODS','active','NYLON BLACK ',1000.000,NULL,25.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:51:13'),(352,'RODS','active','NYLON BLACK ',1000.000,NULL,30.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:51:18'),(353,'RODS','active','NYLON BLACK ',1000.000,NULL,35.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:51:45'),(354,'RODS','active','NYLON BLACK ',1000.000,NULL,40.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:49:41'),(355,'RODS','active','NYLON BLACK ',1000.000,NULL,45.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:50:59'),(356,'RODS','active','NYLON BLACK ',1000.000,NULL,50.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:50:56'),(357,'RODS','active','NYLON BLACK ',1000.000,NULL,55.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:53:15'),(358,'RODS','active','NYLON BLACK ',1000.000,NULL,60.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:56:18'),(359,'RODS','active','NYLON BLACK ',1000.000,NULL,70.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:56:29'),(360,'RODS','active','NYLON BLACK ',1000.000,NULL,75.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:56:41'),(361,'RODS','active','NYLON BLACK ',1000.000,NULL,80.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:56:55'),(362,'RODS','active','NYLON BLACK ',1000.000,NULL,90.000,'LOC',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:57:12'),(363,'RODS','active','NYLON BLACK ',1000.000,NULL,100.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:57:24'),(364,'RODS','active','NYLON BLACK ',1000.000,NULL,110.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:57:37'),(365,'RODS','active','NYLON BLACK ',1000.000,NULL,120.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:57:48'),(366,'RODS','active','NYLON BLACK ',1000.000,NULL,130.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:58:09'),(367,'RODS','active','NYLON BLACK ',1000.000,NULL,140.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:58:20'),(368,'RODS','active','NYLON BLACK ',1000.000,NULL,150.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:58:34'),(369,'RODS','active','NYLON BLACK ',1000.000,NULL,160.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:58:46'),(370,'RODS','active','NYLON BLACK ',1000.000,NULL,170.000,'IMP',0,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:59:08'),(371,'RODS','active','NYLON BLACK ',1000.000,NULL,180.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:59:45'),(372,'RODS','active','NYLON BLACK ',1000.000,NULL,190.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 17:59:56'),(373,'RODS','active','NYLON BLACK ',1000.000,NULL,200.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:00:06'),(374,'RODS','active','NYLON BLACK ',1000.000,NULL,220.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:00:22'),(375,'RODS','active','NYLON BLACK ',1000.000,NULL,250.000,'IMP',5,'','mm','NYLON BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:00:46'),(376,'PLT8','active','PE  WHITE ',1000.000,2000.000,5.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:01:19'),(377,'PLT8','active','PE  WHITE ',1000.000,2000.000,6.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:20:23'),(378,'PLT8','active','PE  WHITE ',1219.200,2438.400,8.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:20:29'),(379,'PLT8','active','PE  WHITE ',1000.000,2000.000,10.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:20:46'),(380,'PLT8','active','PE  WHITE ',1000.000,2000.000,12.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 11:20:36'),(381,'PLT8','active','PE  WHITE ',1000.000,2000.000,15.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:45:53'),(382,'PLT8','active','PE  WHITE ',1000.000,2000.000,20.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:26:57'),(383,'PLT8','active','PE  WHITE ',1000.000,2000.000,25.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:27:03'),(384,'PLT8','active','PE  WHITE ',1000.000,2000.000,30.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:27:08'),(385,'PLT8','active','PE  WHITE ',1000.000,2000.000,40.000,'IMP',0,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:27:24'),(386,'PLT8','active','PE  WHITE ',1000.000,2000.000,50.000,'',0,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:27:34'),(387,'PLT8','active','PE  WHITE ',1000.000,2000.000,60.000,'',0,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:27:40'),(388,'ROD','active','PE  WHITE ',1000.000,1000.000,6.000,'',0,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:28:00'),(389,'RODS','active','PE  WHITE ',1000.000,NULL,8.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:27:48'),(390,'RODS','active','PE  WHITE ',1000.000,NULL,10.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:27:53'),(391,'RODS','active','PE  WHITE ',1000.000,NULL,12.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:02:14'),(392,'RODS','active','PE  WHITE ',1000.000,NULL,15.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:02:55'),(393,'RODS','active','PE  WHITE ',1000.000,NULL,20.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:03:04'),(394,'RODS','active','PE  WHITE ',1000.000,NULL,25.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:03:15'),(395,'RODS','active','PE  WHITE ',1000.000,NULL,30.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,1000,0,0,0,'2020-02-27 01:23:45','2021-06-02 14:48:36'),(396,'RODS','active','PE  WHITE ',1000.000,NULL,35.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,1000,0,0,0,'2020-02-27 01:23:45','2021-06-01 11:13:40'),(397,'RODS','active','PE  WHITE ',1000.000,NULL,40.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,435.00,-500,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 11:53:59'),(398,'RODS','active','PE  WHITE ',1000.000,NULL,45.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:04:18'),(399,'RODS','active','PE  WHITE ',1000.000,NULL,50.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:04:31'),(400,'RODS','active','PE  WHITE ',1000.000,NULL,55.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-24 18:04:58'),(401,'RODS','active','PE  WHITE ',1000.000,NULL,60.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 10:24:48'),(402,'RODS','active','PE  WHITE ',1000.000,NULL,70.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:09:41'),(403,'RODS','active','PE  WHITE ',1000.000,NULL,75.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:10:39'),(404,'RODS','active','PE  WHITE ',1000.000,NULL,80.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:10:52'),(405,'RODS','active','PE  WHITE ',1000.000,NULL,90.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:10:56'),(406,'RODS','active','PE  WHITE ',1000.000,NULL,100.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:11:00'),(407,'RODS','active','PE  WHITE ',1000.000,NULL,110.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:11:05'),(408,'RODS','active','PE  WHITE ',1000.000,NULL,120.000,'LOC',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:11:11'),(409,'RODS','active','PE  WHITE ',1000.000,NULL,130.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:11:19'),(410,'RODS','active','PE  WHITE ',1000.000,NULL,140.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:11:24'),(411,'RODS','active','PE  WHITE ',1000.000,NULL,150.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:48:41'),(412,'RODS','active','PE  WHITE ',1000.000,NULL,160.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:48:36'),(413,'RODS','active','PE  WHITE ',1000.000,NULL,170.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:48:30'),(414,'RODS','active','PE  WHITE ',1000.000,NULL,180.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:48:24'),(415,'RODS','active','PE  WHITE ',1000.000,NULL,190.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:48:19'),(416,'RODS','active','PE  WHITE ',1000.000,NULL,200.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:48:15'),(417,'RODS','active','PE  WHITE ',1000.000,NULL,220.000,'IMP',5,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:12:08'),(418,'RODS','active','PE  WHITE ',1000.000,NULL,250.000,'IMP',5,'','mm','PE  WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:48:10'),(419,'PLT8','active','PE  BLACK ',1000.000,2000.000,5.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:18:21'),(420,'PLT8','active','PE  BLACK ',1000.000,2000.000,6.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:18:27'),(421,'PLT8','active','PE  BLACK ',1000.000,2000.000,8.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 10:26:38'),(422,'PLT8','active','PE  BLACK ',1000.000,2000.000,10.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:22:53'),(423,'PLT8','active','PE  BLACK ',1000.000,2000.000,12.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:22:59'),(424,'PLT8','active','PE  BLACK ',1000.000,2000.000,15.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:25:39'),(425,'PLT8','active','PE  BLACK ',1000.000,2000.000,20.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:25:47'),(426,'PLT8','active','PE  BLACK ',1000.000,2000.000,25.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:25:54'),(427,'PLT8','active','PE  BLACK ',1000.000,2000.000,30.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:26:00'),(428,'PLT8','active','PE  BLACK ',1000.000,2000.000,40.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:26:07'),(429,'PLT8','active','PE  BLACK ',1000.000,2000.000,50.000,'IMP',5,'','mm','PE  BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:26:25'),(430,'RODS','active','PE BLACK ',1000.000,NULL,6.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:19'),(431,'RODS','active','PE BLACK ',1000.000,NULL,8.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:28'),(432,'RODS','active','PE BLACK ',1000.000,NULL,10.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:33'),(433,'RODS','active','PE BLACK ',1000.000,NULL,12.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:37'),(434,'RODS','active','PE BLACK ',1000.000,NULL,15.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:41'),(435,'RODS','active','PE BLACK ',1000.000,NULL,20.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:44'),(436,'RODS','active','PE BLACK ',1000.000,NULL,25.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:48'),(437,'RODS','active','PE BLACK ',1000.000,NULL,30.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:23:51'),(438,'RODS','active','PE BLACK ',1000.000,NULL,35.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:01'),(439,'RODS','active','PE BLACK ',1000.000,NULL,40.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:06'),(440,'RODS','active','PE BLACK ',1000.000,NULL,45.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:12'),(441,'RODS','active','PE BLACK ',1000.000,NULL,50.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:17'),(442,'RODS','active','PE BLACK ',1000.000,NULL,55.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:25'),(443,'RODS','active','PE BLACK ',1000.000,NULL,60.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:29'),(444,'RODS','active','PE BLACK ',1000.000,NULL,70.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:32'),(445,'RODS','active','PE BLACK ',1000.000,NULL,75.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:36'),(446,'RODS','active','PE BLACK ',1000.000,NULL,80.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:40'),(447,'RODS','active','PE BLACK ',1000.000,NULL,85.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:45'),(448,'RODS','active','PE BLACK ',1000.000,NULL,90.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:55'),(449,'RODS','active','PE BLACK ',1000.000,NULL,100.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:24:59'),(450,'RODS','active','PE BLACK ',1000.000,NULL,110.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:25:03'),(451,'RODS','active','PE BLACK ',1000.000,NULL,120.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:25:09'),(452,'RODS','active','PE BLACK ',1000.000,NULL,130.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:25:12'),(453,'RODS','active','PE BLACK ',1000.000,NULL,140.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:25:17'),(454,'RODS','active','PE BLACK ',1000.000,NULL,150.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:25:21'),(455,'RODS','active','PE BLACK ',1000.000,NULL,160.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:48:15'),(456,'RODS','active','PE BLACK ',1000.000,NULL,170.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:49:17'),(457,'RODS','active','PE BLACK ',1000.000,NULL,180.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:49:08'),(458,'RODS','active','PE BLACK ',1000.000,NULL,190.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:48:59'),(459,'RODS','active','PE BLACK ',1000.000,NULL,200.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:48:51'),(460,'RODS','active','PE BLACK ',1000.000,NULL,220.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:48:44'),(461,'RODS','active','PE BLACK ',1000.000,NULL,250.000,'IMP',5,'','mm','PE BLK ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:30:40'),(462,'PLT8','active','PE  GREEN',1000.000,2000.000,12.000,'IMP',5,'','mm','PE  GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:49:28'),(463,'PLT8','active','PE  GREEN',1000.000,2000.000,15.000,'IMP',5,'','mm','PE  GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:49:36'),(464,'PLT8','active','PE  GREEN',1000.000,2000.000,20.000,'IMP',5,'','mm','PE  GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:49:41'),(465,'PLT8','active','PE  GREEN',1000.000,2000.000,25.000,'IMP',5,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:54:49'),(466,'PLT8','active','PE  GREEN',1000.000,2000.000,30.000,'IMP',5,'','mm','PE  GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:49:48'),(467,'PLT8','active','PE  GREEN',1000.000,2000.000,40.000,'IMP',5,'','mm','PE  GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:49:52'),(468,'PLT8','active','PE  GREEN',1000.000,2000.000,50.000,'IMP',5,'','mm','PE  GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:49:56'),(469,'PLT8','active','PEEK  BEIGE ',300.000,600.000,3.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:50:08'),(470,'PLT8','active','PEEK  BEIGE ',600.000,1000.000,5.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:50:16'),(471,'PLT8','active','PEEK  BEIGE ',600.000,1000.000,6.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,2370.00,-11200,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 09:37:39'),(472,'PLT8','active','PEEK  BEIGE ',600.000,1000.000,8.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,1545.00,-27200,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 11:57:28'),(473,'PLT8','active','PEEK  BEIGE ',600.000,1000.000,10.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:55:37'),(474,'PLT8','active','PEEK  BEIGE ',600.000,1000.000,12.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,435.00,-760,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 11:57:28'),(475,'PLT8','active','PEEK  BEIGE ',600.000,1000.000,16.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:55:51'),(476,'PLT8','active','PEEK  BEIGE ',300.000,600.000,20.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:52:29'),(477,'PLT8','active','PEEK  BEIGE ',300.000,600.000,22.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:52:52'),(478,'PLT8','active','PEEK  BEIGE ',300.000,600.000,25.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:53:07'),(479,'PLT8','active','PEEK  BEIGE ',300.000,600.000,30.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:53:18'),(480,'PLT8','active','PEEK  BEIGE ',300.000,600.000,40.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 12:53:37'),(481,'RODS','active','PEEK  BEIGE ',1000.000,NULL,6.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:04'),(482,'RODS','active','PEEK  BEIGE ',1000.000,NULL,8.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:07'),(483,'RODS','active','PEEK  BEIGE ',1000.000,NULL,10.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:11'),(484,'RODS','active','PEEK  BEIGE ',1000.000,NULL,12.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:15'),(485,'RODS','active','PEEK  BEIGE ',1000.000,NULL,15.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,500.00,-80,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:18:09'),(486,'RODS','active','PEEK  BEIGE ',1000.000,NULL,20.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:26'),(487,'RODS','active','PEEK  BEIGE ',1000.000,NULL,25.000,'IMP',5,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 13:59:29'),(488,'RODS','active','PEEK  BEIGE ',1000.000,NULL,30.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:30'),(489,'RODS','active','PEEK  BEIGE ',1000.000,NULL,35.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:35'),(490,'RODS','active','PEEK  BEIGE ',1000.000,NULL,40.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:51:40'),(491,'RODS','active','PEEK  BEIGE ',1000.000,NULL,60.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:10'),(492,'RODS','active','PEEK  BEIGE ',1000.000,NULL,70.000,'IMP',5,'','mm','PEEK  BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:14'),(493,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,5.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:18'),(494,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,6.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:59'),(495,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,8.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:21'),(496,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,10.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,-2500000,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:25'),(497,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,12.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:29'),(498,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,15.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:39'),(499,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,20.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:43'),(500,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,25.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:52:49'),(501,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,30.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,24000,0,0,0,'2020-02-27 01:23:45','2021-06-01 11:53:42'),(502,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,40.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,216000,0,0,0,'2020-02-27 01:23:45','2021-06-01 15:46:14'),(503,'PLT8','active','PP  NAT WHITE ',1219.200,2438.400,50.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:05'),(504,'RODS','active','PP  NAT WHITE ',1000.000,NULL,6.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:10'),(505,'RODS','active','PP  NAT WHITE ',1000.000,NULL,8.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:16'),(506,'RODS','active','PP  NAT WHITE ',1000.000,NULL,10.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:21'),(507,'RODS','active','PP  NAT WHITE ',1000.000,NULL,12.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:28'),(508,'RODS','active','PP  NAT WHITE ',1000.000,NULL,15.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:32'),(509,'RODS','active','PP  NAT WHITE ',1000.000,NULL,20.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:39'),(510,'RODS','active','PP  NAT WHITE ',1000.000,NULL,25.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:55:44'),(511,'RODS','active','PP  NAT WHITE ',1000.000,NULL,30.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:56:33'),(512,'RODS','active','PP  NAT WHITE ',1000.000,NULL,35.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:56:37'),(513,'RODS','active','PP  NAT WHITE ',1000.000,NULL,40.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:56:43'),(514,'RODS','active','PP  NAT WHITE ',1000.000,NULL,45.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:56:47'),(515,'RODS','active','PP  NAT WHITE ',1000.000,NULL,50.000,'IMP',5,'','mm','PP  NAT WHT',0.00,0.00,0.00,0.00,0,1000,0,0,0,'2020-02-27 01:23:45','2021-06-01 11:17:31'),(516,'RODS','active','PP  NAT WHITE ',1000.000,NULL,55.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:56:59'),(517,'RODS','active','PP  NAT WHITE ',1000.000,NULL,65.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:07'),(518,'RODS','active','PP  NAT WHITE ',1000.000,NULL,60.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,1000,0,0,0,'2020-02-27 01:23:45','2021-06-01 11:21:49'),(519,'RODS','active','PP  NAT WHITE ',1000.000,NULL,70.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:18'),(520,'RODS','active','PP  NAT WHITE ',1000.000,NULL,75.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:23'),(521,'RODS','active','PP  NAT WHITE ',1000.000,NULL,80.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:32'),(522,'RODS','active','PP  NAT WHITE ',1000.000,NULL,85.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:36'),(523,'RODS','active','PP  NAT WHITE ',1000.000,NULL,90.000,'LOC',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:40'),(524,'RODS','active','PP  NAT WHITE ',1000.000,NULL,95.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:44'),(525,'RODS','active','PP  NAT WHITE ',1000.000,NULL,100.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:49'),(526,'RODS','active','PP  NAT WHITE ',1000.000,NULL,110.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:57:56'),(527,'RODS','active','PP  NAT WHITE ',1000.000,NULL,120.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:58:01'),(528,'RODS','active','PP  NAT WHITE ',1000.000,NULL,130.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:58:04'),(529,'RODS','active','PP  NAT WHITE ',1000.000,NULL,140.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:58:08'),(530,'RODS','active','PP  NAT WHITE ',1000.000,NULL,150.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:58:13'),(531,'RODS','active','PP  NAT WHITE ',1000.000,NULL,160.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:59:27'),(532,'RODS','active','PP  NAT WHITE ',1000.000,NULL,170.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:59:17'),(533,'RODS','active','PP  NAT WHITE ',1000.000,NULL,180.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:59:13'),(534,'RODS','active','PP  NAT WHITE ',1000.000,NULL,190.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:59:07'),(535,'RODS','active','PP  NAT WHITE ',1000.000,NULL,200.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:59:02'),(536,'RODS','active','PP  NAT WHITE ',1000.000,NULL,220.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:59:37'),(537,'RODS','active','PP  NAT WHITE ',1000.000,NULL,250.000,'IMP',5,'','mm','PP  NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:58:58'),(538,'PLT8','active','PVC  GRAY ',1219.200,2438.400,2.700,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 15:59:49'),(539,'PLT8','active','PVC  GRAY ',1219.200,2438.400,4.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:32:34'),(540,'PLT8','active','PVC  GRAY ',1219.200,2438.400,5.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:32:40'),(541,'PLT8','active','PVC  GRAY ',1219.200,2438.400,6.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:32:46'),(542,'PLT8','active','PVC  GRAY ',1219.200,2438.400,8.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:32:50'),(543,'PLT8','active','PVC  GRAY ',1219.200,2438.400,10.000,'IMP',5,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:19:25'),(544,'PLT8','active','PVC  GRAY ',1219.200,2438.400,12.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:32:54'),(545,'PLT8','active','PVC  GRAY ',1219.200,2438.400,15.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:13:04'),(546,'PLT8','active','PVC  GRAY ',1219.200,2438.400,20.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:13:08'),(547,'PLT8','active','PVC  GRAY ',1219.200,2438.400,25.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:13:15'),(548,'PLT8','active','PVC  GRAY ',1219.200,2438.400,30.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:13:42'),(549,'PLT8','active','PVC  GRAY ',1000.000,2000.000,40.000,'',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:13:39'),(550,'PLT8','active','PVC  GRAY ',1000.000,2000.000,50.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:13:29'),(551,'RODS','active','PVC  GRAY ',1000.000,0.000,6.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:00:44'),(552,'RODS','active','PVC  GRAY ',1000.000,0.000,8.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:14:31'),(553,'RODS','active','PVC  GRAY ',1000.000,0.000,10.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:14:39'),(554,'RODS','active','PVC  GRAY ',1000.000,0.000,12.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:14:51'),(555,'RODS','active','PVC  GRAY ',1000.000,0.000,15.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:14:45'),(556,'RODS','active','PVC  GRAY ',1000.000,0.000,20.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:15:00'),(557,'RODS','active','PVC  GRAY ',1000.000,0.000,25.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:15:07'),(558,'RODS','active','PVC  GRAY ',1000.000,0.000,30.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,420.00,-3000,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 13:51:04'),(559,'RODS','active','PVC  GRAY ',1000.000,NULL,35.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:15:20'),(560,'RODS','active','PVC  GRAY ',1000.000,0.000,40.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:15:25'),(561,'RODS','active','PVC  GRAY ',1000.000,0.000,45.000,'IMP',5,'','mm','PVC GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:15:54'),(562,'RODS','active','PVC  GRAY ',1000.000,0.000,50.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:00'),(563,'RODS','active','PVC  GRAY ',1000.000,0.000,55.000,'LOC',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:06'),(564,'RODS','active','PVC  GRAY ',1000.000,0.000,60.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:17:06'),(565,'RODS','active','PVC  GRAY ',1000.000,0.000,70.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:12'),(566,'RODS','active','PVC  GRAY ',1000.000,0.000,75.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:17'),(567,'RODS','active','PVC  GRAY ',1000.000,0.000,80.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:57'),(568,'RODS','active','PVC  GRAY ',1000.000,NULL,85.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:30'),(569,'RODS','active','PVC  GRAY ',1000.000,0.000,90.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:35'),(570,'RODS','active','PVC  GRAY ',1000.000,0.000,100.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:16:43'),(571,'RODS','active','PVC  GRAY ',1000.000,0.000,110.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:17:23'),(572,'RODS','active','PVC  GRAY ',1000.000,0.000,120.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:17:27'),(573,'RODS','active','PVC  GRAY ',1000.000,0.000,130.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:17:31'),(574,'RODS','active','PVC  GRAY ',1000.000,0.000,140.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:34:04'),(575,'RODS','active','PVC  GRAY ',1000.000,0.000,150.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:17:40'),(576,'RODS','active','PVC  GRAY ',1000.000,0.000,160.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:17:45'),(577,'RODS','active','PVC  GRAY ',1000.000,0.000,170.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 12:34:35'),(578,'RODS','active','PVC  GRAY ',1000.000,0.000,180.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:34:29'),(579,'RODS','active','PVC  GRAY ',1000.000,0.000,190.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:18:39'),(580,'RODS','active','PVC  GRAY ',1000.000,0.000,200.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:18:26'),(581,'RODS','active','PVC  GRAY ',1000.000,0.000,220.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:34:40'),(582,'RODS','active','PVC  GRAY ',1000.000,0.000,250.000,'IMP',5,'','mm','PVC  GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 13:34:48'),(583,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,3.000,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,1000.00,-1000000,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:01:53'),(584,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,4.000,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:50:09'),(585,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,4.500,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:50:16'),(586,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,5.000,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:50:28'),(587,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,6.000,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:51:51'),(588,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,8.000,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:50:52'),(589,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,10.000,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:51:18'),(590,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,12.000,'IMP',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:52:19'),(591,'PLT8','active','RICOCEL  BLACK  ',1200.000,1200.000,15.000,'LOC',5,'','mm','RICOCEL  BLK  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 14:52:39'),(592,'PLT8','active','ULTEM  AMBER ',600.000,1200.000,9.525,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:03:21'),(593,'PLT8','active','ULTEM  AMBER ',600.000,1200.000,9.525,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:03:26'),(594,'PLT8','active','ULTEM  AMBER ',600.000,1200.000,12.700,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:05:49'),(595,'PLT8','active','ULTEM  AMBER ',600.000,1200.000,12.700,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:05:38'),(596,'PLT8','active','ULTEM  AMBER ',600.000,1200.000,15.875,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:05:54'),(597,'PLT8','active','ULTEM  AMBER ',600.000,1200.000,25.400,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:05:59'),(598,'PLT8','active','ULTEM  AMBER ',600.000,1200.000,31.750,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:03:14'),(599,'RODS','active','ULTEM  AMBER ',1000.000,NULL,6.350,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:06:03'),(600,'RODS','active','ULTEM  AMBER ',1000.000,NULL,9.525,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:06:08'),(601,'RODS','active','ULTEM  AMBER ',1000.000,NULL,12.700,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:07'),(602,'RODS','active','ULTEM  AMBER ',1000.000,NULL,15.875,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:11'),(603,'RODS','active','ULTEM  AMBER ',1000.000,NULL,19.050,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:15'),(604,'RODS','active','ULTEM  AMBER ',1000.000,NULL,25.400,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:19'),(605,'RODS','active','ULTEM  AMBER ',1000.000,NULL,31.750,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:26'),(606,'RODS','active','ULTEM  AMBER ',1000.000,NULL,38.100,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:30'),(607,'RODS','active','ULTEM  AMBER ',1000.000,NULL,44.450,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:35'),(608,'RODS','active','ULTEM  AMBER ',1000.000,NULL,63.750,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:39'),(609,'RODS','active','ULTEM  AMBER ',1000.000,NULL,76.200,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:07:43'),(610,'RODS','active','ULTEM  AMBER ',1000.000,NULL,69.850,'IMP',5,'','mm','ULTEM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:13:10'),(611,'RODS','active','SEMITRON',1000.000,NULL,6.350,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:12:34'),(612,'RODS','active','SEMITRON',1000.000,NULL,9.525,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:12:26'),(613,'RODS','active','SEMITRON',1000.000,NULL,12.700,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:12:20'),(614,'RODS','active','SEMITRON',1000.000,NULL,15.875,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,-2,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:12:05'),(615,'RODS','active','SEMITRON',1000.000,NULL,19.050,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,500.00,-27,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:11:48'),(616,'RODS','active','SEMITRON',1000.000,NULL,22.220,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:11:40'),(617,'RODS','active','SEMITRON',1000.000,NULL,25.400,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:11:28'),(618,'RODS','active','SEMITRON',1000.000,NULL,31.750,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:11:21'),(619,'RODS','active','SEMITRON',1000.000,NULL,38.100,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:11:12'),(620,'RODS','active','SEMITRON',1000.000,NULL,44.450,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:11:02'),(621,'RODS','active','SEMITRON',1000.000,NULL,50.800,'IMP',5,'','mm','SEMITRON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:13:21'),(622,'RODS','active','PC  CLEAR',1000.000,NULL,40.000,'IMP',5,'','mm','PC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:14:30'),(623,'RODS','active','PC  CLEAR',1000.000,NULL,45.000,'IMP',5,'','mm','PC  CLEAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:14:36'),(624,'RODS','active','TEFLON  WHITE ',1000.000,NULL,5.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:14:52'),(625,'RODS','active','TEFLON  WHITE ',1000.000,NULL,6.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:14:58'),(626,'RODS','active','TEFLON  WHITE ',1000.000,NULL,8.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:15:04'),(627,'RODS','active','TEFLON  WHITE ',1000.000,NULL,10.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:15:13'),(628,'RODS','active','TEFLON  WHITE ',1000.000,NULL,12.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:15:18'),(629,'RODS','active','TEFLON  WHITE ',1000.000,NULL,15.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:15:22'),(630,'RODS','active','TEFLON  WHITE ',1000.000,NULL,20.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:15:27'),(631,'RODS','active','TEFLON  WHITE ',1000.000,NULL,25.000,'LOC',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:11'),(632,'RODS','active','TEFLON  WHITE ',1000.000,NULL,30.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:16'),(633,'RODS','active','TEFLON  WHITE ',1000.000,NULL,35.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:20'),(634,'RODS','active','TEFLON  WHITE ',1000.000,NULL,40.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:25'),(635,'RODS','active','TEFLON  WHITE ',1000.000,NULL,45.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:29'),(636,'RODS','active','TEFLON  WHITE ',1000.000,NULL,50.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:34'),(637,'RODS','active','TEFLON  WHITE ',1000.000,NULL,55.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:45'),(638,'RODS','active','TEFLON  WHITE ',1000.000,NULL,60.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:16:50'),(639,'RODS','active','TEFLON  WHITE ',1000.000,NULL,70.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:18:55'),(640,'RODS','active','TEFLON  WHITE ',1000.000,NULL,75.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:18:48'),(641,'RODS','active','TEFLON  WHITE ',1000.000,NULL,80.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:19:29'),(642,'RODS','active','TEFLON  WHITE ',1000.000,NULL,90.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:19:37'),(643,'RODS','active','TEFLON  WHITE ',1000.000,NULL,100.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:19:42'),(644,'RODS','active','TEFLON  WHITE ',1000.000,NULL,110.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:19:45'),(645,'RODS','active','TEFLON  WHITE ',1000.000,NULL,120.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:19:49'),(646,'RODS','active','TEFLON  WHITE ',1000.000,NULL,130.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:19:53'),(647,'RODS','active','TEFLON  WHITE ',1000.000,NULL,140.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:19:58'),(648,'RODS','active','TEFLON  WHITE ',1000.000,NULL,150.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:20:02'),(649,'RODS','active','TEFLON  WHITE ',1000.000,NULL,160.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:20:06'),(650,'RODS','active','TEFLON  WHITE ',1000.000,NULL,170.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:20:10'),(651,'RODS','active','TEFLON  WHITE ',1000.000,NULL,180.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:23:04'),(652,'RODS','active','TEFLON  WHITE ',1000.000,NULL,190.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:23:09'),(653,'RODS','active','TEFLON  WHITE ',1000.000,NULL,200.000,'IMP',5,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 15:27:37'),(654,'RODS','active','TEFLON  WHITE ',1000.000,NULL,220.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:23:15'),(655,'RODS','active','TEFLON  WHITE ',1000.000,NULL,250.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:23:21'),(656,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,1.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:23:42'),(657,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,2.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:23:48'),(658,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,3.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:23:53'),(659,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,5.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,1740.00,-330000,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 10:04:39'),(660,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,6.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:24:05'),(661,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,8.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:24:50'),(662,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,10.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:29:29'),(663,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,12.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,115.00,-14091,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 11:12:45'),(664,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,15.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:25:01'),(665,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,20.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:25:04'),(666,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,25.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,180.00,-4746,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 11:12:45'),(667,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,30.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,485.00,-10938,0,0,0,0,'2020-02-27 01:23:45','2021-06-02 11:12:45'),(668,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,40.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:25:19'),(669,'PLT8','active','TEFLON  WHITE ',1200.000,1200.000,50.000,'IMP',5,'','mm','TEFLON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:25:23'),(670,'PLT8','active','TORLON  4203',300.000,300.000,5.000,'IMP',5,'','mm','TORLON  4203',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:29:59'),(671,'PLT8','active','TORLON  4203',300.000,300.000,7.000,'IMP',5,'','mm','TORLON  4203',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:31:08'),(672,'PLT8','active','TORLON  4203',300.000,300.000,10.000,'IMP',5,'','mm','TORLON  4203',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:31:36'),(673,'PLT8','active','TORLON  4203',300.000,300.000,12.000,'IMP',5,'','mm','TORLON  4203',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:31:58'),(674,'PLT8','active','TORLON  4203',300.000,300.000,15.000,'IMP',5,'','mm','TORLON  4203',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:32:03'),(675,'PLT8','active','TORLON  4203',300.000,300.000,20.000,'IMP',5,'','mm','TORLON  4203',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:32:09'),(676,'PLT8','active','TORLON  4203',300.000,300.000,25.000,'IMP',5,'','mm','TORLON  4203',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:32:14'),(677,'RODS','active','PU YELLOW',300.000,0.000,6.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:34:53'),(678,'RODS','active','PU YELLOW',300.000,0.000,8.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:34:58'),(679,'RODS','active','PU YELLOW',300.000,0.000,10.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:35:05'),(680,'RODS','active','PU YELLOW',300.000,0.000,12.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:35:18'),(681,'RODS','active','PU YELLOW',300.000,0.000,15.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:34:42'),(682,'RODS','active','PU YELLOW',500.000,0.000,20.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:35:37'),(683,'RODS','active','PU YELLOW',500.000,0.000,25.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:35:50'),(684,'RODS','active','PU YELLOW',500.000,0.000,30.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:35:59'),(685,'RODS','active','PU YELLOW',500.000,0.000,35.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:36:07'),(686,'RODS','active','PU YELLOW',500.000,0.000,40.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:36:14'),(687,'RODS','active','PU YELLOW',500.000,0.000,45.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:36:22'),(688,'RODS','active','PU YELLOW',500.000,0.000,50.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:36:34'),(689,'RODS','active','PU YELLOW',500.000,0.000,55.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:36:41'),(690,'RODS','active','PU YELLOW',500.000,0.000,60.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:36:49'),(691,'RODS','active','PU YELLOW',500.000,0.000,65.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:37:17'),(692,'RODS','active','PU YELLOW',500.000,0.000,70.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:37:25'),(693,'ROD','active','PU YELLOW',500.000,0.000,75.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:37:38'),(694,'RODS','active','PU YELLOW',500.000,0.000,80.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:38:27'),(695,'RODS','active','PU YELLOW',500.000,0.000,85.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:38:40'),(696,'RODS','active','PU YELLOW',500.000,0.000,90.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:38:51'),(697,'RODS','active','PU YELLOW',500.000,0.000,100.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:39:00'),(698,'RODS','active','PU YELLOW',500.000,0.000,110.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-25 16:39:08'),(699,'RODS','active','PU YELLOW PLATE ',500.000,0.000,120.000,'IMP',5,'','mm','PU YEL PLATE ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 15:48:06'),(700,'RODS','active','PU YELLOW PLATE ',500.000,0.000,130.000,'IMP',5,'','mm','PU YEL PLATE ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 15:48:16'),(701,'RODS','active','PU YELLOW PLATE ',500.000,0.000,140.000,'IMP',5,'','mm','PU YEL PLATE ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 15:48:50'),(702,'RODS','active','PU YELLOW PLATE ',500.000,0.000,150.000,'IMP',5,'','mm','PU YEL PLATE ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-21 15:48:57'),(703,'PLT8','active','PU YELLOW',1000.000,2000.000,2.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 11:29:10'),(704,'PLT8','active','PU YELLOW',1000.000,2000.000,3.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,200.00,-38500,0,0,0,0,'2020-02-27 01:23:45','2021-06-01 12:39:49'),(705,'PLT8','active','PU YELLOW',1000.000,2000.000,5.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 11:30:29'),(706,'PLT8','active','PU YELLOW',1000.000,2000.000,10.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 11:32:20'),(707,'PLT8','active','PU YELLOW',1000.000,2000.000,15.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 11:32:45'),(708,'PLT8','active','PU YELLOW',1000.000,2000.000,20.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 11:33:04'),(709,'PLT8','active','PU YELLOW',1000.000,2000.000,30.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 11:33:19'),(710,'PLT8','active','PU YELLOW',1000.000,2000.000,40.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 12:36:18'),(711,'PLT8','active','PU YELLOW',1000.000,2000.000,50.000,'IMP',5,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-05-26 11:39:22'),(712,'PLT8','active','ALUMINUM T6 PLATE',1219.200,2438.400,16.000,'LOC',5,' ','mm','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-17 12:02:56','2021-05-26 11:40:24'),(713,'RODS','active','ALUMINUM T6 ROD',2000.000,NULL,50.000,'LOC',5,' ','MM','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-21 15:56:01','2021-05-22 12:32:35'),(714,'RODS','active','ALUMINUM T6',2000.000,NULL,16.000,'LOC',5,' ','MM','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-21 16:01:00','2021-05-22 15:52:45'),(715,'RODS','active','ALUMINUM T6',2000.000,NULL,31.750,'LOC',5,' ','MM','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 09:13:35','2021-05-22 12:32:43'),(716,'RODS','active','SUS 304 ROD',3000.000,NULL,6.000,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 09:14:52','2021-05-26 12:32:23'),(717,'RODS','active','ALUMINUM T6 ROD',3000.000,NULL,8.000,'LOC',5,' ','MM','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 09:16:08','2021-05-22 12:32:02'),(718,'RODS','active','ALUMINUM T6 ROD',3000.000,NULL,10.000,'LOC',5,' ','MM','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 09:19:51','2021-05-22 12:31:52'),(719,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,2.000,'LOC',5,' ','MM','ALUM 5083',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 09:37:45','2021-05-26 11:40:08'),(720,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,16.000,'LOC',5,' ','MM','ALUM 5083',NULL,NULL,0.00,0.00,-7200,NULL,NULL,NULL,0,'2021-05-22 12:02:13','2021-05-26 11:39:54'),(721,'RODS','active','ALUMINUM T6 ROD',3000.000,NULL,38.000,'LOC',5,' ','MM','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 12:04:11','2021-05-22 12:34:00'),(722,'RODS','active','S45C ROD',3000.000,NULL,20.000,'LOC',5,' ','MM','S45C',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 12:05:01','2021-05-22 12:34:06'),(723,'RODS','active','1045 ROD',1000.000,NULL,40.000,'LOC',5,' ','MM','1045',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 12:05:36','2021-05-22 12:34:15'),(724,'RODS','active','ALUMINUM T6 ROD',3000.000,NULL,6.000,'LOC',5,' ','MM','ALUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 12:28:36','2021-05-22 12:34:22'),(725,'RODS','active','SUS 304 ROD',3000.000,NULL,3.000,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 12:30:54','2021-05-22 12:34:26'),(726,'RODS','active','SUS 304 ROD',3000.000,NULL,3.000,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:35:17','2021-05-22 12:35:17'),(727,'RODS','active','SUS 304 ROD',3000.000,NULL,8.000,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:35:50','2021-05-22 12:35:50'),(728,'RODS','active','SUS 304 ROD',3000.000,NULL,16.000,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:36:37','2021-05-22 12:36:37'),(729,'RODS','active','SUS 304 ROD',3000.000,NULL,20.000,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:37:05','2021-05-22 12:37:05'),(730,'RODS','active','SS400 ROD',3000.000,NULL,10.000,'LOC',5,' ','MM','SS400',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:37:36','2021-05-22 12:37:36'),(731,'RODS','active','SS400 ROD',3000.000,NULL,16.000,'LOC',5,' ','MM','SS400',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:38:25','2021-05-22 12:38:25'),(732,'RODS','active','SS400 ROD',3000.000,NULL,20.000,'LOC',5,' ','MM','SS400',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:38:56','2021-05-22 12:38:56'),(733,'RODS','active','SS400 ROD',3000.000,NULL,25.000,'LOC',5,' ','MM','SS400',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 12:39:32','2021-05-22 12:39:32'),(734,'RODS','active','CDR6',1000.000,NULL,6.000,'IMP',5,' ','MM','CDR6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:07:06','2021-06-01 16:59:18'),(735,'RODS','active','CDR6',1000.000,NULL,35.000,'IMP',5,' ','MM','CDR6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:08:08','2021-06-01 16:59:30'),(736,'RODS','active','CDR6',1000.000,NULL,65.000,'IMP',0,' ','MM','CDR6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:08:53','2021-06-01 16:59:38'),(737,'RODS','active','CDR6',1000.000,NULL,70.000,'IMP',5,' ','MM','CDR6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:09:29','2021-06-01 17:00:06'),(738,'RODS','active','MC501 CDR6',1000.000,NULL,80.000,'IMP',5,' ','MM','CDR6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:10:07','2021-05-22 13:10:07'),(739,NULL,'active',' ',NULL,NULL,0.000,'',0,' ',' ',' ',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:10:26','2021-05-22 13:10:26'),(740,'PLT8','active','DELRIN WHITE',600.000,1200.000,4.000,'IMP',0,' ','MM','DELRIN WHITE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:11:59','2021-05-22 13:11:59'),(741,'PLT8','active','DELRIN WHITE',600.000,1200.000,5.000,'IMP',5,' ','MM','DELRIN WHITE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:12:34','2021-05-22 13:12:34'),(742,'PLT8','active','DELRIN WHITE',600.000,1200.000,8.000,'IMP',5,' ','MM','DELRIN WHITE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:13:19','2021-05-22 13:13:19'),(743,'PLT8','active','DELRIN WHITE',600.000,1200.000,15.000,'IMP',5,' ','MM','DELRIN WHITE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:14:03','2021-05-22 13:14:03'),(744,'PLT8','active','DELRIN WHITE',600.000,1200.000,40.000,'IMP',5,' ','MM','DELRIN WHITE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:14:43','2021-05-25 14:41:26'),(745,'PLT8','active','AS-PVC CLEAR',1219.200,2438.240,3.000,'IMP',5,' ','MM','AS- PVC CLR',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:16:53','2021-05-25 13:35:09'),(746,'PLT8','active','AS-PVC CLEAR',1219.200,2438.240,5.000,'IMP',5,' ','MM','AS-PVC CLR',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:17:36','2021-05-25 13:35:18'),(747,'PLT8','active','AS-PVC CLEAR',1219.200,2438.240,6.000,'IMP',5,' ','MM','AS-PVC CLR',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:18:37','2021-05-25 13:35:29'),(748,'PLT8','active','AS-PVC CLEAR',1219.200,2438.240,10.000,'IMP',5,' ','MM','AS-PVC CLR',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:20:06','2021-05-25 13:35:34'),(749,'PLT8','active','ACRYLIC CLEAR',1219.200,2438.240,20.000,'Sel',5,' ','MM','ACRY CLEAR',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:24:11','2021-05-22 13:24:11'),(750,'RODS','active','ACRYLIC CLEAR',1000.000,NULL,10.000,'IMP',5,' ','MM','ACRY CLR ROD',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:25:24','2021-05-26 12:50:43'),(751,'RODS','active','ACRYLIC CLEAR',1000.000,NULL,12.000,'IMP',5,' ','MM','ACRY CLR ROD',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:26:52','2021-05-26 12:50:48'),(752,'RODS','active','ACRYLIC CLEAR',1000.000,NULL,15.000,'IMP',5,' ','MM','ACRY CLR ROD',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:27:53','2021-05-26 12:50:56'),(753,'RODS','active','ACRYLIC CLEAR',1000.000,NULL,20.000,'IMP',5,' ','MM','ACRY CLR ROD',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:28:36','2021-05-26 12:51:27'),(754,'RODS','active','ACRYLIC CLEAR',1000.000,NULL,25.000,'IMP',5,' ','MM','ACRY CLR ROD',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-22 13:29:22','2021-06-01 12:33:31'),(755,'PLT8','active','DELRIN BLK',600.000,1200.000,5.000,'IMP',5,' ','MM','DELRIN BLK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:38:51','2021-05-22 13:38:51'),(756,'PLT8','active','DELRIN BLK',600.000,1200.000,6.000,'IMP',5,' ','MM','DELRIN BLK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:39:56','2021-05-22 13:39:56'),(757,'PLT8','active','DELRIN BLK',600.000,1200.000,12.000,'IMP',5,' ','MM','DELRIN BLK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:40:34','2021-05-22 13:40:34'),(758,'PLT8','active','DELRIN BLK',600.000,1200.000,15.000,'IMP',5,' ','MM','DELRIN BLK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:41:30','2021-05-22 13:41:30'),(759,'PLT8','active','DELRIN BLK',600.000,1200.000,20.000,'IMP',5,' ','MM','DELRIN BLK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:42:55','2021-05-22 13:42:55'),(760,'PLT8','active','DELRIN BLK',600.000,1200.000,25.000,'IMP',5,' ','MM','DELRIN BLK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 13:43:33','2021-05-22 13:43:33'),(761,'PLT8','active','DELRIN BLUE',600.000,1200.000,12.000,'IMP',5,' ','MM','DELRIN BLUE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 14:41:17','2021-05-22 14:41:17'),(762,'PLT8','active','DELRIN BLUE',600.000,1200.000,20.000,'IMP',5,' ','MM','DELRIN BLUE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 14:41:55','2021-05-22 14:41:55'),(763,'PLT8','active','DELRIN BLUE',600.000,1200.000,30.000,'IMP',5,' ','MM','DELRIN BLUE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-22 14:42:47','2021-05-22 14:42:47'),(764,'PLT8','active','PU YELLOW',1000.000,2000.000,4.000,'IMP',5,' ','MM','PU YELLOW',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:41:15','2021-05-26 11:41:15'),(765,'PLT8','active','PU YELLOW',1000.000,2000.000,6.000,'IMP',5,' ','MM','PU YELLOW',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:41:39','2021-05-26 11:41:39'),(766,'PLT8','active','PU YELLOW',1000.000,2000.000,8.000,'IMP',5,' ','MM','PU YELLOW',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:42:43','2021-05-26 11:42:43'),(767,'PLT8','active','PU YELLOW',1000.000,2000.000,12.000,'IMP',5,' ','MM','PU YELLOW',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:43:10','2021-05-26 11:43:10'),(768,'PLT8','active','PU YELLOW',1000.000,2000.000,20.000,'IMP',5,' ','MM','PU YELLOW',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:43:44','2021-05-26 11:43:44'),(769,'PLT8','active','PU YELLOW',1000.000,2000.000,25.000,'IMP',5,' ','MM','PU YELLOW',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:44:24','2021-05-26 11:44:24'),(770,'PLT8','active','FIBRA',1000.000,2000.000,10.000,'IMP',5,' ','MM','FIBRA',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:50:50','2021-05-26 11:50:50'),(771,'PLT8','active','FIBRA',1000.000,2000.000,15.000,'IMP',5,' ','MM','FIBRA',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:51:28','2021-05-26 11:51:28'),(772,'PLT8','active','FIBRA',1000.000,2000.000,20.000,'IMP',5,' ','MM','FIBRA',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:52:24','2021-05-26 11:52:24'),(773,'PLT8','active','FIBRA',1000.000,2000.000,30.000,'IMP',5,' ','MM','FIBRA',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:52:55','2021-05-26 11:52:55'),(774,'RODS','active','TEFLON',1000.000,NULL,65.000,'IMP',5,' ','MM','TEFLON',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:53:31','2021-05-26 11:53:31'),(775,'PLT8','active','SEMITRON',600.000,1200.000,10.000,'IMP',5,' ','MM','SEMITRON',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:54:19','2021-05-26 11:54:19'),(776,'PLT8','active','SEMITRON',600.000,1000.000,15.000,'IMP',5,' ','mm','SEMITRON',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:54:46','2021-05-26 11:54:46'),(777,'PLT8','active','SEMITRON',600.000,1200.000,20.000,'IMP',5,' ','mm','SEMITRON',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:55:20','2021-05-26 11:55:20'),(778,NULL,'active','SEMITRON',600.000,1200.000,30.000,'IMP',5,' ','mm','SEMITRON',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:56:07','2021-05-26 11:56:07'),(779,'PLT8','active','PVC IVORY',1219.200,2438.400,8.000,'IMP',5,' ','MM','PVC IVORY',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:56:59','2021-05-26 11:56:59'),(780,'PLT8','active','PVC IVORY',1219.200,2438.400,10.000,'IMP',5,' ','MM','PVC IVORY',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:57:23','2021-05-26 11:57:23'),(781,'PLT8','active','PVC IVORY',1219.200,2438.400,12.000,'IMP',5,' ','MM','PVC IVORY',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 11:57:55','2021-05-26 11:57:55'),(782,'RODS','active','G10 GREEN',1000.000,NULL,10.000,'IMP',5,' ','MM','G10 GREEN',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:01:37','2021-05-26 12:01:37'),(783,'RODS','active','G10 GREEN',1000.000,NULL,12.000,'IMP',5,' ','MM','G10 GREEN',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:01:54','2021-05-26 12:01:54'),(784,'RODS','active','G10 GREEN',1000.000,NULL,15.000,'IMP',5,' ','MM','G10 GREEN',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:02:14','2021-05-26 12:02:14'),(785,'RODS','active','G10 GREEN',1000.000,NULL,20.000,'IMP',5,' ','MM','G10 GREEN',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:02:30','2021-05-26 12:02:30'),(786,'RODS','active','G10 GREEN',1000.000,NULL,25.000,'IMP',5,' ','MM','G10 GREEN',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:03:43','2021-05-26 12:03:43'),(787,'RODS','active','G10 GREEN',1000.000,NULL,30.000,'IMP',5,' ','MM','G10 GREEN',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:04:09','2021-05-26 12:04:09'),(788,'RODS','active','G10 GREEN',1000.000,NULL,40.000,'IMP',5,' ','MM','G10 GREEN',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:04:27','2021-05-26 12:04:27'),(789,'PLT8','active','G10 BLACK',1000.000,1200.000,8.000,'IMP',5,' ','MM','G10 BLACK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:05:03','2021-05-26 12:05:03'),(790,'PLT8','active','G10 BLACK',1000.000,1200.000,10.000,'IMP',5,' ','MM','G10 BLACK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:05:24','2021-05-26 12:05:24'),(791,'PLT8','active','G10 BLACK',1000.000,1200.000,12.000,'IMP',5,' ','MM','G10 BLACK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:05:49','2021-05-26 12:05:49'),(792,'RODS','active','BRASS',1000.000,NULL,12.700,'LOC',5,' ','MM','BRASS',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:20:42','2021-05-26 12:20:42'),(793,'RODS','active','BRASS',1000.000,NULL,19.050,'LOC',5,' ','MM','BRASS',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:21:16','2021-05-26 12:21:16'),(794,'RODS','active','BRASS',1000.000,NULL,9.525,'LOC',5,' ','MM','BRASS',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:22:36','2021-05-26 12:22:36'),(795,'RODS','active','BRASS',1000.000,NULL,7.938,'LOC',5,' ','MM','BRASS',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:24:24','2021-05-26 12:24:24'),(796,'RODS','active','BRASS',1000.000,NULL,6.350,'LOC',5,' ','MM','BRASS',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:25:03','2021-05-26 12:25:03'),(797,'RODS','active','SUS 304',9999.999,NULL,100.000,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:26:07','2021-05-26 12:26:07'),(798,'RODS','active','SUS 304',9999.999,NULL,12.700,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:26:33','2021-05-26 12:26:33'),(799,'RODS','active','ALUMINUM T6',2000.000,NULL,20.000,'LOC',5,' ','MM','AL T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-05-26 12:30:55','2021-05-26 12:30:55'),(800,'PLT8','active','ACRYLIC CLR',1219.200,2438.400,1.500,'IMP',5,' ','MM','ACRYLIC CLR',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-05-26 12:49:58','2021-06-01 12:33:35'),(801,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,6.000,'LOC',5,' ','MM','AL 5083',NULL,NULL,0.00,840.00,-67100,NULL,NULL,NULL,0,'2021-06-01 12:46:00','2021-06-01 14:01:45'),(802,'RODS','active','SUS 304 ROD',3000.000,NULL,57.150,'LOC',0,' ','MM','SUS 304',NULL,NULL,0.00,1075.00,-205,NULL,NULL,NULL,0,'2021-06-01 13:05:58','2021-06-02 09:33:23'),(803,'RODS','active','SUS 316 ROD',3000.000,NULL,12.700,'LOC',0,' ','MM','SUS 316',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 13:07:26','2021-06-01 13:07:26'),(804,'PLT8','active','PVC GRAY',1219.200,2438.400,3.000,'IMP',5,' ','MM','PVC GRAY',NULL,NULL,0.00,0.00,-2880000,NULL,NULL,NULL,NULL,'2021-06-01 13:16:33','2021-06-01 13:16:33'),(805,'RODS','active','NYLON IVORY',1000.000,NULL,5.000,'IMP',5,' ','MM','NYLON IVORY',NULL,NULL,0.00,0.00,-1000,NULL,NULL,NULL,NULL,'2021-06-01 13:17:59','2021-06-01 13:17:59'),(806,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,20.000,'LOC',5,' ','MM','AL 5083',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 13:57:16','2021-06-01 13:57:16'),(807,'PLT8','active','ALUMINUM T6 PLATE',1219.200,2438.400,20.000,'LOC',5,' ','MM','AL T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 13:58:10','2021-06-01 13:58:10'),(808,'RODS','active','SUS 304 ROD',3000.000,NULL,4.760,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 14:00:05','2021-06-01 14:00:05'),(809,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,12.000,'LOC',5,' ','MM',' AL 5083',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 14:01:13','2021-06-01 14:01:13'),(810,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,10.000,'LOC',5,' ','MM','AL 5083',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 14:02:44','2021-06-01 14:02:44'),(811,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,15.000,'LOC',5,' ','MM','AL 5083',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 14:03:17','2021-06-01 14:03:17'),(812,'PLT8','active','ALUMINUM 5083 PLATE',1219.200,2438.400,25.400,'LOC',5,' ','MM','AL 5083',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 14:04:13','2021-06-01 14:04:13'),(813,'PLT8','active','DELRIN BLACK',1219.200,2438.400,70.000,'IMP',5,' ','MM','DELRIN BLACK',NULL,NULL,0.00,1290.00,-14840,NULL,NULL,NULL,NULL,'2021-06-01 15:00:10','2021-06-02 10:10:04'),(814,'RODS','active','SUS 304 ROD',3000.000,NULL,50.800,'LOC',5,' ','MM','SUS 304',NULL,NULL,0.00,1000.00,-176,NULL,NULL,NULL,NULL,'2021-06-01 16:17:06','2021-06-02 09:33:23'),(815,'PLT8','active','DELRIN BLACK',1000.000,2000.000,70.000,'LOC',5,' ','MM','DELRIN BLACK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-06-01 17:29:39','2021-06-01 17:42:04'),(816,'PLT8','active','DELRIN BLACK',1000.000,2000.000,65.000,'IMP',5,' ','MM','DELRIN BLACK',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-06-01 17:30:00','2021-06-01 17:42:13'),(817,'PLT8','active','NYLON BLUE',1000.000,2000.000,70.000,'IMP',5,' ','MM','NYLON BLUE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,0,'2021-06-01 17:30:28','2021-06-01 17:41:02'),(818,'PLT8','active','NYLON  BLUE',1000.000,2000.000,35.000,'IMP',5,' ','MM','NYLON  BLUE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 17:39:00','2021-06-01 17:39:00'),(819,'PLT8','active','NYLON  BLUE',1000.000,2000.000,45.000,'IMP',5,' ','MM','NYLON  BLUE',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 17:40:17','2021-06-01 17:40:17'),(820,'PLT8','active','ALUMINUM T6',1219.200,2438.400,39.000,'Sel',5,' ','MM','ALUMINUM T6',NULL,NULL,0.00,0.00,NULL,NULL,NULL,NULL,NULL,'2021-06-01 17:43:20','2021-06-01 17:43:20'),(821,'PLT8','active','ACRYLIC CLEAR',1219.200,2438.400,20.000,'IMP',5,' ','MM','ACRYLIC CLR',NULL,NULL,0.00,65.00,-3200,NULL,NULL,NULL,NULL,'2021-06-02 10:44:57','2021-06-02 11:18:29');
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_order_details`
--

DROP TABLE IF EXISTS `purchase_order_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `length` int(11) NOT NULL,
  `width` int(11) NOT NULL,
  `thickness` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `quantity_area` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_order_details`
--

LOCK TABLES `purchase_order_details` WRITE;
/*!40000 ALTER TABLE `purchase_order_details` DISABLE KEYS */;
INSERT INTO `purchase_order_details` VALUES (1,1,40,370,350,5,1,129500,1500.00,1500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(2,2,39,100,35,3,1,3500,100.00,100.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(3,3,1,10,10,4,25,2500,500.00,12500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(4,3,1,10,10,4,1,100,500.00,500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(5,4,10,30,20,10,1,600,20.00,20.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(6,5,1,10,10,3,5,500,500.00,2500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(7,5,2,15,15,5,10,2250,100.00,1000.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(8,6,1,10,10,3,15,1500,500.00,7500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(9,7,244,42,16,12,6,4032,50.00,300.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(10,7,260,215,1,25,1,215,115.00,115.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(11,8,10,600,510,10,1,306000,1000.00,1000.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(12,9,71,100,57,20,1,5700,140.00,140.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(13,9,69,155,85,12,1,13175,185.00,185.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(14,9,68,85,20,10,1,1700,50.00,50.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(15,9,68,140,20,10,1,2800,50.00,50.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(16,10,115,300,200,30,1,60000,700.00,700.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(17,11,71,1000,1000,20,1,1000000,17500.00,17500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(18,11,68,1000,1000,10,1,1000000,9000.00,9000.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(19,12,55,2400,1200,5,5,14400000,5100.00,25500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(20,13,704,220,175,3,1,38500,200.00,200.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(21,13,71,250,35,20,2,17500,220.00,440.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(22,14,558,1000,1,30,3,3000,420.00,1260.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(23,15,643,50,1,100,1,50,1750.00,1750.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(24,16,485,80,1,15,1,80,500.00,500.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(25,17,132,770,1,35,2,1540,770.00,1540.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(26,18,128,1000,1,15,1,1000,155.00,155.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(27,19,801,305,220,6,1,67100,840.00,840.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(28,20,128,1000,1,15,1,1000,155.00,155.00,'0000-00-00 00:00:00','0000-00-00 00:00:00'),(29,21,804,2400,1200,3,1,2880000,4000.00,4000.00,'2021-06-01 13:21:28','2021-06-01 13:21:28'),(30,22,292,1000,1,6,1,1000,80.00,80.00,'2021-06-01 13:25:52','2021-06-01 13:25:52'),(31,22,805,1000,1,5,1,1000,75.00,75.00,'2021-06-01 13:25:52','2021-06-01 13:25:52'),(32,23,802,45,1,57,1,45,385.00,385.00,'2021-06-01 13:42:55','2021-06-01 13:42:55'),(33,24,720,80,45,16,2,7200,135.00,270.00,'2021-06-01 14:25:24','2021-06-01 14:25:24'),(34,25,813,140,106,70,1,14840,1290.00,1290.00,'2021-06-01 15:03:03','2021-06-01 15:03:03'),(35,26,659,600,110,5,5,330000,1740.00,8700.00,'2021-06-01 16:00:43','2021-06-01 16:00:43'),(36,27,814,176,1,51,1,176,1000.00,1000.00,'2021-06-01 16:19:13','2021-06-01 16:19:13'),(37,27,802,160,1,57,1,160,1075.00,1075.00,'2021-06-01 16:19:13','2021-06-01 16:19:13'),(38,28,667,75,55,30,2,8250,720.00,1440.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(39,28,666,55,48,25,1,2640,400.00,400.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(40,28,667,56,48,30,1,2688,485.00,485.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(41,28,666,39,27,25,2,2106,180.00,360.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(42,28,663,75,33,12,2,4950,185.00,370.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(43,28,663,67,33,12,2,4422,165.00,330.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(44,28,663,48,33,12,1,1584,120.00,120.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(45,28,663,50,33,12,1,1650,125.00,125.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(46,28,663,45,33,12,1,1485,115.00,115.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(47,29,88,153,88,10,14,188496,920.00,12880.00,'2021-06-01 17:01:39','2021-06-01 17:01:39'),(48,30,80,100,47,8,1,4700,380.00,380.00,'2021-06-01 17:06:31','2021-06-01 17:06:31'),(49,30,60,48,28,18,16,21504,55.00,880.00,'2021-06-01 17:06:31','2021-06-01 17:06:31'),(50,31,164,82,27,15,2,4428,55.00,110.00,'2021-06-01 17:09:57','2021-06-01 17:09:57'),(51,31,471,160,70,6,1,11200,2370.00,2370.00,'2021-06-01 17:09:57','2021-06-01 17:09:57'),(52,32,70,200,100,15,1,20000,350.00,350.00,'2021-06-01 17:17:05','2021-06-01 17:17:05'),(53,32,72,200,100,25,1,20000,580.00,580.00,'2021-06-01 17:17:05','2021-06-01 17:17:05'),(54,32,74,200,100,40,1,20000,930.00,930.00,'2021-06-01 17:17:06','2021-06-01 17:17:06'),(55,32,143,15,1,90,2,30,145.00,290.00,'2021-06-01 17:17:06','2021-06-01 17:17:06'),(56,32,143,37,1,90,4,148,305.00,1220.00,'2021-06-01 17:17:06','2021-06-01 17:17:06'),(57,32,143,85,1,90,2,170,655.00,1310.00,'2021-06-01 17:17:06','2021-06-01 17:17:06'),(58,32,134,45,1,45,2,90,115.00,230.00,'2021-06-01 17:17:06','2021-06-01 17:17:06'),(59,33,60,48,28,18,16,21504,55.00,880.00,'2021-06-01 17:40:03','2021-06-01 17:40:03'),(60,34,820,60,38,39,4,9120,220.00,880.00,'2021-06-02 09:19:15','2021-06-02 09:19:15'),(61,35,821,40,40,20,2,3200,65.00,130.00,'2021-06-02 10:49:01','2021-06-02 10:49:01'),(62,36,474,40,19,12,1,760,435.00,435.00,'2021-06-02 11:08:49','2021-06-02 11:08:49'),(63,36,472,200,32,8,2,12800,2040.00,4080.00,'2021-06-02 11:08:49','2021-06-02 11:08:49'),(64,36,472,150,32,8,3,14400,1545.00,4635.00,'2021-06-02 11:08:49','2021-06-02 11:08:49'),(65,37,397,500,1,40,1,500,435.00,435.00,'2021-06-02 11:24:28','2021-06-02 11:24:28'),(66,38,167,60,50,30,1,3000,125.00,125.00,'2021-06-02 12:24:26','2021-06-02 12:24:26'),(67,38,167,55,50,30,1,2750,115.00,115.00,'2021-06-02 12:24:26','2021-06-02 12:24:26'),(68,39,188,50,1,65,1,50,190.00,190.00,'2021-06-02 12:26:22','2021-06-02 12:26:22'),(69,40,179,1000,1,15,2,2000,145.00,290.00,'2021-06-02 14:05:47','2021-06-02 14:05:47'),(70,40,178,1000,1,12,7,7000,95.00,665.00,'2021-06-02 14:05:47','2021-06-02 14:05:47');
/*!40000 ALTER TABLE `purchase_order_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_order_terms`
--

DROP TABLE IF EXISTS `purchase_order_terms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_order_terms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_order_terms`
--

LOCK TABLES `purchase_order_terms` WRITE;
/*!40000 ALTER TABLE `purchase_order_terms` DISABLE KEYS */;
INSERT INTO `purchase_order_terms` VALUES (1,1,'CHRG','30',1500.00,'2021-05-08 15:58:38','2021-05-08 15:58:38'),(2,2,'CHRG','30',100.00,'2021-05-08 16:04:10','2021-05-08 16:04:10'),(3,3,'CHRG','15',13000.00,'2021-05-08 16:06:49','2021-05-08 16:06:49'),(4,4,'CHRG','30',20.00,'2021-05-17 12:30:04','2021-05-17 12:30:04'),(5,5,'CHRG','15',3500.00,'2021-05-25 11:26:29','2021-05-25 11:26:29'),(6,6,'CHRG','15',7500.00,'2021-05-25 11:42:22','2021-05-25 11:42:22'),(7,7,'CHRG','30',415.00,'2021-05-25 12:41:31','2021-05-25 12:41:31'),(8,8,'CHRG','30',1000.00,'2021-05-25 13:04:13','2021-05-25 13:04:13'),(9,9,'CHRG','30',425.00,'2021-05-25 14:25:07','2021-05-25 14:25:07'),(10,10,'CHRG','30',700.00,'2021-05-25 14:41:24','2021-05-25 14:41:24'),(11,11,'CASH','COD',26500.00,'2021-06-01 11:44:09','2021-06-01 11:44:09'),(12,12,'CHRG','30',25500.00,'2021-06-01 11:47:25','2021-06-01 11:47:25'),(13,13,'CASH','COD',640.00,'2021-06-01 11:51:03','2021-06-01 11:51:03'),(14,14,'CHRG','30',1260.00,'2021-06-01 11:54:44','2021-06-01 11:54:44'),(15,15,'CASH','COD',1750.00,'2021-06-01 12:00:33','2021-06-01 12:00:33'),(16,16,'CASH','COD',500.00,'2021-06-01 12:04:13','2021-06-01 12:04:13'),(17,17,'CASH','COD',1540.00,'2021-06-01 12:13:05','2021-06-01 12:13:05'),(18,18,'CASH','COD',155.00,'2021-06-01 12:22:20','2021-06-01 12:22:20'),(19,19,'CHRG','30',840.00,'2021-06-01 12:47:47','2021-06-01 12:47:47'),(20,20,'CASH','COD',155.00,'2021-06-01 13:01:46','2021-06-01 13:01:46'),(21,21,'CHRG','30',4000.00,'2021-06-01 13:21:28','2021-06-01 13:21:28'),(22,22,'CHRG','30',155.00,'2021-06-01 13:25:52','2021-06-01 13:25:52'),(23,23,'CHRG','30',385.00,'2021-06-01 13:42:55','2021-06-01 13:42:55'),(24,24,'CHRG','30',270.00,'2021-06-01 14:25:24','2021-06-01 14:25:24'),(25,25,'CHRG','30',1290.00,'2021-06-01 15:03:03','2021-06-01 15:03:03'),(26,26,'CHCK',NULL,8700.00,'2021-06-01 16:00:43','2021-06-01 16:00:43'),(27,27,'CHRG','30',2075.00,'2021-06-01 16:19:13','2021-06-01 16:19:13'),(28,28,'CHRG','30',3745.00,'2021-06-01 16:45:54','2021-06-01 16:45:54'),(29,29,'CHRG','30',12880.00,'2021-06-01 17:01:39','2021-06-01 17:01:39'),(30,30,'CASH','COD',1260.00,'2021-06-01 17:06:31','2021-06-01 17:06:31'),(31,31,'CHRG','60',2480.00,'2021-06-01 17:09:57','2021-06-01 17:09:57'),(32,32,'CHRG','30',4910.00,'2021-06-01 17:17:06','2021-06-01 17:17:06'),(33,33,'CASH','COD',880.00,'2021-06-01 17:40:03','2021-06-01 17:40:03'),(34,34,'CHRG','30',880.00,'2021-06-02 09:19:15','2021-06-02 09:19:15'),(35,35,'CHRG','30',130.00,'2021-06-02 10:49:01','2021-06-02 10:49:01'),(36,36,'CHRG','30',9150.00,'2021-06-02 11:08:49','2021-06-02 11:08:49'),(37,37,'CASH','COD',435.00,'2021-06-02 11:24:28','2021-06-02 11:24:28'),(38,38,'CHRG','30',240.00,'2021-06-02 12:24:26','2021-06-02 12:24:26'),(39,39,'CHRG','30',190.00,'2021-06-02 12:26:22','2021-06-02 12:26:22'),(40,40,'CHRG','30',955.00,'2021-06-02 14:05:47','2021-06-02 14:05:47');
/*!40000 ALTER TABLE `purchase_order_terms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_orders`
--

DROP TABLE IF EXISTS `purchase_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `po_no` varchar(20) NOT NULL,
  `po_date` date DEFAULT NULL,
  `customer` varchar(80) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_orders`
--

LOCK TABLES `purchase_orders` WRITE;
/*!40000 ALTER TABLE `purchase_orders` DISABLE KEYS */;
INSERT INTO `purchase_orders` VALUES (1,'123','2021-05-08','Raitech',1500.00,0.00,0.00,0.00,0.00,'2021-05-08 15:58:38'),(2,'125','2021-05-08','Raitech',100.00,0.00,0.00,0.00,0.00,'2021-05-08 16:04:10'),(3,'1234','2021-05-08','Raitech',13000.00,0.00,0.00,0.00,0.00,'2021-05-08 16:06:49'),(4,'thru phone','2021-05-17','Customer A',20.00,0.00,0.00,0.00,0.00,'2021-05-17 12:30:04'),(5,'','2021-05-25','Customer A',3500.00,0.00,0.00,0.00,0.00,'2021-05-25 11:26:29'),(6,'123','2021-05-25','Customer A',7500.00,0.00,0.00,0.00,0.00,'2021-05-25 11:42:22'),(7,'VTC-RM-21-363A','2021-05-25','VJF Toolmaster Corporation',415.00,0.00,0.00,0.00,0.00,'2021-05-25 12:41:31'),(8,'112','2021-05-25','Accura Mechanical Parts & Components Corp.',1000.00,0.00,0.00,0.00,0.00,'2021-05-25 13:04:13'),(9,'21-6808','2021-05-25','Meraki-Tech Precision Corporation',425.00,0.00,0.00,0.00,0.00,'2021-05-25 14:25:06'),(10,'12356','2021-05-25','Meraki-Tech Precision Corporation',700.00,0.00,0.00,0.00,0.00,'2021-05-25 14:41:24'),(11,'THRU PHONE','2021-06-01','Kobaishina Enterprise',26500.00,0.00,0.00,0.00,0.00,'2021-06-01 11:44:09'),(12,'DM21-329','2021-06-01','Dynamicro Precision Toolings & Fab Inc.',25500.00,0.00,0.00,0.00,0.00,'2021-06-01 11:47:25'),(13,'021-001798','2021-06-01','RGG Stainless Steel & Metal Fabrication',640.00,0.00,0.00,0.00,0.00,'2021-06-01 11:51:03'),(14,'THRU PHONE','2021-06-01','Adtech Metal Precision',1260.00,0.00,0.00,0.00,0.00,'2021-06-01 11:54:43'),(15,'THRU PHONE','2021-06-01','Cosreq Metal Fabrication and Industrial Supply',1750.00,0.00,0.00,0.00,0.00,'2021-06-01 12:00:33'),(16,'THRU PHONE','2021-06-01','EMC Machine Works',500.00,0.00,0.00,0.00,0.00,'2021-06-01 12:04:13'),(17,'THRU PHONE','2021-06-01','SPH Industrial Supply and Services',1540.00,0.00,0.00,0.00,0.00,'2021-06-01 12:13:05'),(18,'04422','2021-06-01','Aim Tooling Technologies & Metal Fabrication',155.00,0.00,0.00,0.00,0.00,'2021-06-01 12:22:20'),(19,'THRU PHONE','2021-06-01','Maretech Precision Toolings & Industrial Services',840.00,0.00,0.00,0.00,0.00,'2021-06-01 12:47:47'),(20,'04422','2021-06-01','Almendrala Industrial Services Company',155.00,0.00,0.00,0.00,0.00,'2021-06-01 13:01:46'),(21,'3TI-1053-21','2021-06-01','Technoalloy Tooling Technologies',4000.00,0.00,0.00,0.00,0.00,'2021-06-01 13:21:28'),(22,'THRU PHONE','2021-06-01','Prov3 Toolings',155.00,0.00,0.00,0.00,0.00,'2021-06-01 13:25:52'),(23,'THRU PHONE','2021-06-01','Ficatech Precision Metal Fabrication',385.00,0.00,0.00,0.00,0.00,'2021-06-01 13:42:55'),(24,'THRU PHONE','2021-06-01','Prov3 Toolings',270.00,0.00,0.00,0.00,0.00,'2021-06-01 14:25:24'),(25,'21-05-0197','2021-06-01','ESJ Precision Toolings',1290.00,0.00,0.00,0.00,0.00,'2021-06-01 15:03:03'),(26,'PF21-031','2021-06-01','Petrineans General Merchandise',8700.00,0.00,0.00,0.00,0.00,'2021-06-01 16:00:43'),(27,'0720','2021-06-01','Tri-Fusion Precision Corporation',2075.00,0.00,0.00,0.00,0.00,'2021-06-01 16:19:13'),(28,'0069','2021-06-01','Alevro Precision Tooling Services',3745.00,0.00,0.00,0.00,0.00,'2021-06-01 16:45:54'),(29,'107783','2021-06-01','Key Automation Unit, Inc.',12880.00,0.00,0.00,0.00,0.00,'2021-06-01 17:01:39'),(30,'THRU PHONE','2021-06-01','FULLERTECH',1260.00,0.00,0.00,0.00,0.00,'2021-06-01 17:06:31'),(31,'THRU PHONE','2021-06-01','Critomo Precision And Industrial Services',2480.00,0.00,0.00,0.00,0.00,'2021-06-01 17:09:57'),(32,'MT05621-21','2021-06-01','JFS Precision Technology Corporation',4910.00,0.00,0.00,0.00,0.00,'2021-06-01 17:17:05'),(33,'THRU PHONE','2021-06-01','FULLERTECH',880.00,0.00,0.00,0.00,0.00,'2021-06-01 17:40:03'),(34,'1007','2021-06-02','ELF Precision Tooling Services',880.00,0.00,0.00,0.00,0.00,'2021-06-02 09:19:15'),(35,'2021-026','2021-06-02','Victowin Engineering Services & Trading',130.00,0.00,0.00,0.00,0.00,'2021-06-02 10:49:01'),(36,'RM210602011','2021-06-02','Lark Seimitsu Tooling Corporation',9150.00,0.00,0.00,0.00,0.00,'2021-06-02 11:08:49'),(37,'THRU PHONE','2021-06-02','Silver Jem Corporation',435.00,0.00,0.00,0.00,0.00,'2021-06-02 11:24:28'),(38,'JMS0621-702','2021-06-02','JMS Metal Fabrication & Precision Toolings Services',240.00,0.00,0.00,0.00,0.00,'2021-06-02 12:24:26'),(39,'UPMF-060221-0300','2021-06-02','Ubana Plastic & Metal Fabrication Works',190.00,0.00,0.00,0.00,0.00,'2021-06-02 12:26:22'),(40,'HT21-0208','2021-06-02','Hantverk Technologies',955.00,0.00,0.00,0.00,0.00,'2021-06-02 14:05:47');
/*!40000 ALTER TABLE `purchase_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `supplier_ledgers`
--

DROP TABLE IF EXISTS `supplier_ledgers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `supplier_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `supplier_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `supplier_ledgers`
--

LOCK TABLES `supplier_ledgers` WRITE;
/*!40000 ALTER TABLE `supplier_ledgers` DISABLE KEYS */;
/*!40000 ALTER TABLE `supplier_ledgers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `suppliers`
--

DROP TABLE IF EXISTS `suppliers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `suppliers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) DEFAULT NULL,
  `alias` varchar(20) NOT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `tax_type` char(3) NOT NULL COMMENT 'VAT- VATable, ZRO-  NonVat',
  `tin` varchar(20) NOT NULL,
  `address` varchar(150) NOT NULL,
  `business_style` varchar(150) NOT NULL,
  `unit_system` char(3) NOT NULL COMMENT 'ENG - English, MET - Metric',
  `last_bill` int(11) NOT NULL,
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `suppliers`
--

LOCK TABLES `suppliers` WRITE;
/*!40000 ALTER TABLE `suppliers` DISABLE KEYS */;
/*!40000 ALTER TABLE `suppliers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_details`
--

DROP TABLE IF EXISTS `transaction_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transaction_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` char(36) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=172 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_details`
--

LOCK TABLES `transaction_details` WRITE;
/*!40000 ALTER TABLE `transaction_details` DISABLE KEYS */;
INSERT INTO `transaction_details` VALUES (1,'609644ae-7a90-4020-af0d-02dcc0a80105',40,1,1500.00,1500.00),(2,'609644cf-f680-49ac-8425-02dcc0a80105',40,129500,0.00,0.00),(3,'6096455f-755c-46b9-9a54-02dcc0a80105',40,1,1500.00,1500.00),(4,'609645fa-8cd0-49f2-b1a4-02dcc0a80105',39,1,100.00,100.00),(5,'60964699-a2f4-420f-92c8-02dc82e7a674',1,25,500.00,12500.00),(6,'60964699-a2f4-420f-92c8-02dc82e7a674',1,1,500.00,500.00),(7,'60964741-b370-444d-9f92-02dc82e7a674',1,3600,0.00,0.00),(8,'60964741-b370-444d-9f92-02dc82e7a674',1,100,0.00,0.00),(9,'60a1f14c-b7d8-4090-85fe-32e8c0a80069',10,1,20.00,20.00),(10,'60ac6e65-4cc4-46d3-83f1-2fb482e7a674',1,5,500.00,2500.00),(11,'60ac6e65-4cc4-46d3-83f1-2fb482e7a674',2,10,100.00,1000.00),(12,'60ac721e-25f8-477f-9dbb-2fb482e7a674',1,15,500.00,7500.00),(13,'60ac7ffb-62dc-4ef9-b5f8-2fb4c0a8006a',244,6,50.00,300.00),(14,'60ac7ffb-62dc-4ef9-b5f8-2fb4c0a8006a',260,1,115.00,115.00),(15,'60ac84c7-bb6c-4d7f-a5f6-2fb482e7a674',244,5400,0.00,0.00),(16,'60ac84c7-bb6c-4d7f-a5f6-2fb482e7a674',260,215,0.00,0.00),(17,'60ac84dc-9448-4f70-be48-2fb482e7a674',244,6,50.00,300.00),(18,'60ac84dc-9448-4f70-be48-2fb482e7a674',260,1,115.00,115.00),(19,'60ac854d-7e48-4c37-8bbc-2fb4c0a8006a',10,1,1000.00,1000.00),(20,'60ac85c0-a594-4129-9901-2fb4c0a8006a',10,309000,0.00,0.00),(21,'60ac85df-6fdc-4298-83a5-2fb4c0a8006a',10,1,1000.00,1000.00),(22,'60ac9842-d7d4-446a-b510-2fb4c0a8006a',71,1,140.00,140.00),(23,'60ac9842-d7d4-446a-b510-2fb4c0a8006a',69,1,185.00,185.00),(24,'60ac9842-d7d4-446a-b510-2fb4c0a8006a',68,1,50.00,50.00),(25,'60ac9842-d7d4-446a-b510-2fb4c0a8006a',68,1,50.00,50.00),(26,'60ac98aa-b024-46d1-b187-2fb4c0a8006a',71,6180,0.00,0.00),(27,'60ac98aa-b024-46d1-b187-2fb4c0a8006a',69,13904,0.00,0.00),(28,'60ac98aa-b024-46d1-b187-2fb4c0a8006a',68,2024,0.00,0.00),(29,'60ac98aa-b024-46d1-b187-2fb4c0a8006a',68,3289,0.00,0.00),(30,'60ac98cd-d18c-4294-a3b6-2fb4c0a8006a',71,1,140.00,140.00),(31,'60ac98cd-d18c-4294-a3b6-2fb4c0a8006a',69,1,185.00,185.00),(32,'60ac98cd-d18c-4294-a3b6-2fb4c0a8006a',68,1,50.00,50.00),(33,'60ac98cd-d18c-4294-a3b6-2fb4c0a8006a',68,1,50.00,50.00),(34,'60ac9c14-ba94-4c36-8428-2fb4c0a8006a',115,1,700.00,700.00),(35,'60ac9c7b-7cdc-4c45-888b-2fb4c0a8006a',115,61509,0.00,0.00),(36,'60ac9cb3-f11c-4e81-b034-2fb4c0a8006a',115,1,700.00,700.00),(37,'60b5ad09-283c-4bc5-aa9a-320cc0a8006a',71,1,17500.00,17500.00),(38,'60b5ad09-283c-4bc5-aa9a-320cc0a8006a',68,1,9000.00,9000.00),(39,'60b5adcd-1320-45ab-a37e-320cc0a8006a',55,5,5100.00,25500.00),(40,'60b5aea7-f794-4eaa-b260-320cc0a8006a',704,1,200.00,200.00),(41,'60b5aea7-f794-4eaa-b260-320cc0a8006a',71,2,220.00,440.00),(42,'60b5af83-1588-4d43-9005-320cc0a8006a',558,3,420.00,1260.00),(43,'60b5b0e1-b848-4ffa-9d78-320cc0a8006a',643,1,1750.00,1750.00),(44,'60b5b1bd-34a8-4cf3-843e-320cc0a8006a',485,1,500.00,500.00),(45,'60b5b20b-921c-429e-92b9-320cc0a8006a',485,80,0.00,0.00),(46,'60b5b269-f404-4f08-aec1-320cc0a8006a',485,1,500.00,500.00),(47,'60b5b3d1-9f80-4224-84a8-320cc0a8006a',132,2,770.00,1540.00),(48,'60b5b4da-60f8-463a-86ba-320cc0a8006a',132,1540,0.00,0.00),(49,'60b5b501-27ec-4610-b00b-320c82e7a674',485,1,500.00,500.00),(50,'60b5b50b-b250-44bd-b69c-320cc0a8006a',132,2,770.00,1540.00),(51,'60b5b5fc-9c0c-4f50-b1e8-320cc0a8006a',128,1,155.00,155.00),(52,'60b5b9eb-c3e4-4a77-87f6-320cc0a8006a',704,38500,0.00,0.00),(53,'60b5b9eb-c3e4-4a77-87f6-320cc0a8006a',71,17500,0.00,0.00),(54,'60b5ba15-b914-4e63-99c2-320cc0a8006a',704,1,200.00,200.00),(55,'60b5ba15-b914-4e63-99c2-320cc0a8006a',71,2,220.00,440.00),(56,'60b5bbf3-cb80-490a-b230-320cc0a8006a',801,1,840.00,840.00),(57,'60b5bc26-5ed4-4039-ab35-320cc0a8006a',801,67100,0.00,0.00),(58,'60b5bc43-3f88-4817-888a-320cc0a8006a',801,1,840.00,840.00),(59,'60b5bf3a-aaa8-46af-9075-320cc0a8006a',128,1,155.00,155.00),(60,'60b5c3d8-67ac-4a8e-b074-320cc0a8006a',804,1,4000.00,4000.00),(61,'60b5c4e0-0328-4ce1-9c24-320cc0a8006a',292,1,80.00,80.00),(62,'60b5c4e0-0328-4ce1-9c24-320cc0a8006a',805,1,75.00,75.00),(63,'60b5c8df-67f8-43d4-9edc-320cc0a8006a',802,1,385.00,385.00),(64,'60b5c9ad-f360-4c6f-96bc-320cc0a8006a',71,1000000,0.00,0.00),(65,'60b5c9ad-f360-4c6f-96bc-320cc0a8006a',68,1000000,0.00,0.00),(66,'60b5ca93-6304-4e16-be90-320cc0a8006a',558,3000,0.00,0.00),(67,'60b5cac8-c670-4a5c-82cf-320cc0a8006a',558,3,420.00,1260.00),(68,'60b5d0f2-ef48-40ca-9d3f-320cc0a8006a',802,45,0.00,0.00),(69,'60b5d111-209c-40c2-86dc-320cc0a8006a',292,1000,0.00,0.00),(70,'60b5d111-209c-40c2-86dc-320cc0a8006a',805,1000,0.00,0.00),(71,'60b5d131-8208-469a-b8e8-320cc0a8006a',128,1000,0.00,0.00),(72,'60b5d16e-1f18-490a-86d8-320cc0a8006a',55,14400000,0.00,0.00),(73,'60b5d1c9-d760-4eb5-af51-320cc0a8006a',128,1,155.00,155.00),(74,'60b5d2d4-c5f4-4e5b-843b-320cc0a8006a',720,2,135.00,270.00),(75,'60b5dba7-f0ac-4d18-a6dd-320cc0a8006a',813,1,1290.00,1290.00),(76,'60b5e92b-5cd0-4917-9c3b-320cc0a8006a',659,5,1740.00,8700.00),(77,'60b5ed81-db90-4364-8309-320cc0a8006a',814,1,1000.00,1000.00),(78,'60b5ed81-db90-4364-8309-320cc0a8006a',802,1,1075.00,1075.00),(79,'60b5eddf-8470-4463-9e4d-320cc0a8006a',814,176,0.00,0.00),(80,'60b5eddf-8470-4463-9e4d-320cc0a8006a',802,160,0.00,0.00),(81,'60b5f3c2-f180-4713-8704-320cc0a8006a',667,2,720.00,1440.00),(82,'60b5f3c2-f180-4713-8704-320cc0a8006a',666,1,400.00,400.00),(83,'60b5f3c2-f180-4713-8704-320cc0a8006a',667,1,485.00,485.00),(84,'60b5f3c2-f180-4713-8704-320cc0a8006a',666,2,180.00,360.00),(85,'60b5f3c2-f180-4713-8704-320cc0a8006a',663,2,185.00,370.00),(86,'60b5f3c2-f180-4713-8704-320cc0a8006a',663,2,165.00,330.00),(87,'60b5f3c2-f180-4713-8704-320cc0a8006a',663,1,120.00,120.00),(88,'60b5f3c2-f180-4713-8704-320cc0a8006a',663,1,125.00,125.00),(89,'60b5f3c2-f180-4713-8704-320cc0a8006a',663,1,115.00,115.00),(90,'60b5f773-a7b0-4792-84de-320cc0a8006a',88,14,920.00,12880.00),(91,'60b5f7cd-bc1c-4366-b7cd-320cc0a8006a',720,7200,0.00,0.00),(92,'60b5f806-206c-4ea4-bb57-320cc0a8006a',804,2880000,0.00,0.00),(93,'60b5f821-f154-41b8-9b61-320cc0a8006a',659,330000,0.00,0.00),(94,'60b5f897-98c8-4763-867c-320cc0a8006a',80,1,380.00,380.00),(95,'60b5f897-98c8-4763-867c-320cc0a8006a',60,16,55.00,880.00),(96,'60b5f965-4734-4a4f-9773-320cc0a8006a',164,2,55.00,110.00),(97,'60b5f965-4734-4a4f-9773-320cc0a8006a',471,1,2370.00,2370.00),(98,'60b5fb11-62f4-4be0-a661-320cc0a8006a',70,1,350.00,350.00),(99,'60b5fb11-62f4-4be0-a661-320cc0a8006a',72,1,580.00,580.00),(100,'60b5fb11-62f4-4be0-a661-320cc0a8006a',74,1,930.00,930.00),(101,'60b5fb11-62f4-4be0-a661-320cc0a8006a',143,2,145.00,290.00),(102,'60b5fb11-62f4-4be0-a661-320cc0a8006a',143,4,305.00,1220.00),(103,'60b5fb11-62f4-4be0-a661-320cc0a8006a',143,2,655.00,1310.00),(104,'60b5fb11-62f4-4be0-a661-320cc0a8006a',134,2,115.00,230.00),(105,'60b60073-c15c-4a32-bcb2-320cc0a8006a',60,16,55.00,880.00),(106,'60b60549-1164-4a0b-b817-320cc0a8006a',60,21504,0.00,0.00),(107,'60b60580-3b80-4f94-ad53-320cc0a8006a',60,16,55.00,880.00),(108,'60b606b7-c8f0-452b-9ccc-320cc0a8006a',70,20000,0.00,0.00),(109,'60b606b7-c8f0-452b-9ccc-320cc0a8006a',72,20000,0.00,0.00),(110,'60b606b7-c8f0-452b-9ccc-320cc0a8006a',74,20000,0.00,0.00),(111,'60b606b7-c8f0-452b-9ccc-320cc0a8006a',143,30,0.00,0.00),(112,'60b606b7-c8f0-452b-9ccc-320cc0a8006a',143,148,0.00,0.00),(113,'60b606b7-c8f0-452b-9ccc-320cc0a8006a',143,170,0.00,0.00),(114,'60b606b7-c8f0-452b-9ccc-320cc0a8006a',134,90,0.00,0.00),(115,'60b606e3-f9b8-460d-af51-320cc0a8006a',164,4428,0.00,0.00),(116,'60b606e3-f9b8-460d-af51-320cc0a8006a',471,11200,0.00,0.00),(117,'60b6dc93-b84c-48e7-ae55-3318c0a8006a',820,4,220.00,880.00),(118,'60b6dfe3-1328-48a3-8bc0-3318c0a8006a',814,1,1000.00,1000.00),(119,'60b6dfe3-1328-48a3-8bc0-3318c0a8006a',802,1,1075.00,1075.00),(120,'60b6e0e3-7a54-4738-8efc-3318c0a8006a',164,2,55.00,110.00),(121,'60b6e0e3-7a54-4738-8efc-3318c0a8006a',471,1,2370.00,2370.00),(122,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a',70,1,350.00,350.00),(123,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a',72,1,580.00,580.00),(124,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a',74,1,930.00,930.00),(125,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a',143,2,145.00,290.00),(126,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a',143,4,305.00,1220.00),(127,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a',143,2,655.00,1310.00),(128,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a',134,2,115.00,230.00),(129,'60b6e46c-cc60-4a40-a5de-3318c0a8006a',88,188496,0.00,0.00),(130,'60b6e495-0d14-4db7-887c-3318c0a8006a',88,14,920.00,12880.00),(131,'60b6e737-1210-4dd1-8dc7-3318c0a8006a',659,5,1740.00,8700.00),(132,'60b6e810-9d2c-488d-aed5-3318c0a8006a',813,14840,0.00,0.00),(133,'60b6e87c-a554-4b6e-8bf8-3318c0a8006a',813,1,1290.00,1290.00),(134,'60b6f19d-4bd0-4114-b1ca-3318c0a8006a',821,2,65.00,130.00),(135,'60b6f641-3e44-4b45-afdc-3318c0a8006a',474,1,435.00,435.00),(136,'60b6f641-3e44-4b45-afdc-3318c0a8006a',472,2,2040.00,4080.00),(137,'60b6f641-3e44-4b45-afdc-3318c0a8006a',472,3,1545.00,4635.00),(138,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',667,8250,0.00,0.00),(139,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',666,2640,0.00,0.00),(140,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',667,2688,0.00,0.00),(141,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',666,2106,0.00,0.00),(142,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',663,4950,0.00,0.00),(143,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',663,4422,0.00,0.00),(144,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',663,1584,0.00,0.00),(145,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',663,1650,0.00,0.00),(146,'60b6f6e9-22b4-4a46-ac04-3318c0a8006a',663,1485,0.00,0.00),(147,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',667,2,720.00,1440.00),(148,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',666,1,400.00,400.00),(149,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',667,1,485.00,485.00),(150,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',666,2,180.00,360.00),(151,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',663,2,185.00,370.00),(152,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',663,2,165.00,330.00),(153,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',663,1,120.00,120.00),(154,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',663,1,125.00,125.00),(155,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a',663,1,115.00,115.00),(156,'60b6f849-b684-4cfe-9b9c-3318c0a8006a',821,3200,0.00,0.00),(157,'60b6f885-88cc-4201-a48b-3318c0a8006a',821,2,65.00,130.00),(158,'60b6f9ec-6594-48f2-908a-3318c0a8006a',397,1,435.00,435.00),(159,'60b700ab-85a8-4a10-93e8-3318c0a8006a',397,500,0.00,0.00),(160,'60b700d7-bd7c-4913-b6c9-3318c0a8006a',397,1,435.00,435.00),(161,'60b70181-5fb8-45aa-a8c6-3318c0a8006a',474,760,0.00,0.00),(162,'60b70181-5fb8-45aa-a8c6-3318c0a8006a',472,12800,0.00,0.00),(163,'60b70181-5fb8-45aa-a8c6-3318c0a8006a',472,14400,0.00,0.00),(164,'60b701a8-d40c-46de-b08a-3318c0a8006a',474,1,435.00,435.00),(165,'60b701a8-d40c-46de-b08a-3318c0a8006a',472,2,2040.00,4080.00),(166,'60b701a8-d40c-46de-b08a-3318c0a8006a',472,3,1545.00,4635.00),(167,'60b707fa-c408-4dfd-9517-3318c0a8006a',167,1,125.00,125.00),(168,'60b707fa-c408-4dfd-9517-3318c0a8006a',167,1,115.00,115.00),(169,'60b7086e-23f0-4e89-ae40-3318c0a8006a',188,1,190.00,190.00),(170,'60b71fbb-d0dc-489d-9dab-3318c0a8006a',179,2,145.00,290.00),(171,'60b71fbb-d0dc-489d-9dab-3318c0a8006a',178,7,95.00,665.00);
/*!40000 ALTER TABLE `transaction_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_payments`
--

DROP TABLE IF EXISTS `transaction_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transaction_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` char(36) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_payments`
--

LOCK TABLES `transaction_payments` WRITE;
/*!40000 ALTER TABLE `transaction_payments` DISABLE KEYS */;
INSERT INTO `transaction_payments` VALUES (1,'609644ae-7a90-4020-af0d-02dcc0a80105','CHRG','30',1500.00),(2,'6096455f-755c-46b9-9a54-02dcc0a80105','CHRG','30',1500.00),(3,'609645fa-8cd0-49f2-b1a4-02dcc0a80105','CHRG','30',100.00),(4,'60964699-a2f4-420f-92c8-02dc82e7a674','CHRG','15',13000.00),(5,'60a1f14c-b7d8-4090-85fe-32e8c0a80069','CHRG','30',20.00),(6,'60ac6e65-4cc4-46d3-83f1-2fb482e7a674','CHRG','15',3500.00),(7,'60ac721e-25f8-477f-9dbb-2fb482e7a674','CHRG','15',7500.00),(8,'60ac7ffb-62dc-4ef9-b5f8-2fb4c0a8006a','CHRG','30',415.00),(9,'60ac84dc-9448-4f70-be48-2fb482e7a674','CHRG','30',415.00),(10,'60ac854d-7e48-4c37-8bbc-2fb4c0a8006a','CHRG','30',1000.00),(11,'60ac85df-6fdc-4298-83a5-2fb4c0a8006a','CHRG','30',1000.00),(12,'60ac9842-d7d4-446a-b510-2fb4c0a8006a','CHRG','30',425.00),(13,'60ac98cd-d18c-4294-a3b6-2fb4c0a8006a','CHRG','30',425.00),(14,'60ac9c14-ba94-4c36-8428-2fb4c0a8006a','CHRG','30',700.00),(15,'60ac9cb3-f11c-4e81-b034-2fb4c0a8006a','CHRG','30',700.00),(16,'60b5ad09-283c-4bc5-aa9a-320cc0a8006a','CASH','COD',26500.00),(17,'60b5adcd-1320-45ab-a37e-320cc0a8006a','CHRG','30',25500.00),(18,'60b5aea7-f794-4eaa-b260-320cc0a8006a','CASH','COD',640.00),(19,'60b5af83-1588-4d43-9005-320cc0a8006a','CHRG','30',1260.00),(20,'60b5b0e1-b848-4ffa-9d78-320cc0a8006a','CASH','COD',1750.00),(21,'60b5b1bd-34a8-4cf3-843e-320cc0a8006a','CASH','COD',500.00),(22,'60b5b269-f404-4f08-aec1-320cc0a8006a','CASH','COD',500.00),(23,'60b5b3d1-9f80-4224-84a8-320cc0a8006a','CASH','COD',1540.00),(24,'60b5b501-27ec-4610-b00b-320c82e7a674','CASH','COD',500.00),(25,'60b5b50b-b250-44bd-b69c-320cc0a8006a','CASH','COD',1540.00),(26,'60b5b5fc-9c0c-4f50-b1e8-320cc0a8006a','CASH','COD',155.00),(27,'60b5ba15-b914-4e63-99c2-320cc0a8006a','CHRG','30',640.00),(28,'60b5bbf3-cb80-490a-b230-320cc0a8006a','CHRG','30',840.00),(29,'60b5bc43-3f88-4817-888a-320cc0a8006a','CHRG','30',840.00),(30,'60b5bf3a-aaa8-46af-9075-320cc0a8006a','CASH','COD',155.00),(31,'60b5c3d8-67ac-4a8e-b074-320cc0a8006a','CHRG','30',4000.00),(32,'60b5c4e0-0328-4ce1-9c24-320cc0a8006a','CHRG','30',155.00),(33,'60b5c8df-67f8-43d4-9edc-320cc0a8006a','CHRG','30',385.00),(34,'60b5cac8-c670-4a5c-82cf-320cc0a8006a','CHRG','30',1260.00),(35,'60b5d1c9-d760-4eb5-af51-320cc0a8006a','CASH','COD',155.00),(36,'60b5d2d4-c5f4-4e5b-843b-320cc0a8006a','CHRG','30',270.00),(37,'60b5dba7-f0ac-4d18-a6dd-320cc0a8006a','CHRG','30',1290.00),(38,'60b5e92b-5cd0-4917-9c3b-320cc0a8006a','CHCK',NULL,8700.00),(39,'60b5ed81-db90-4364-8309-320cc0a8006a','CHRG','30',2075.00),(40,'60b5f3c2-f180-4713-8704-320cc0a8006a','CHRG','30',3745.00),(41,'60b5f773-a7b0-4792-84de-320cc0a8006a','CHRG','30',12880.00),(42,'60b5f897-98c8-4763-867c-320cc0a8006a','CASH','COD',1260.00),(43,'60b5f965-4734-4a4f-9773-320cc0a8006a','CHRG','60',2480.00),(44,'60b5fb11-62f4-4be0-a661-320cc0a8006a','CHRG','30',4910.00),(45,'60b60073-c15c-4a32-bcb2-320cc0a8006a','CASH','COD',880.00),(46,'60b60580-3b80-4f94-ad53-320cc0a8006a','CASH','COD',880.00),(47,'60b6dc93-b84c-48e7-ae55-3318c0a8006a','CHRG','30',880.00),(48,'60b6dfe3-1328-48a3-8bc0-3318c0a8006a','CHRG','30',2075.00),(49,'60b6e0e3-7a54-4738-8efc-3318c0a8006a','CHRG','30',2480.00),(50,'60b6e1fe-b63c-4cc6-be32-3318c0a8006a','CHRG','30',4910.00),(51,'60b6e495-0d14-4db7-887c-3318c0a8006a','CHRG','30',12880.00),(52,'60b6e737-1210-4dd1-8dc7-3318c0a8006a','CASH','COD',8700.00),(53,'60b6e87c-a554-4b6e-8bf8-3318c0a8006a','CHRG','30',1290.00),(54,'60b6f19d-4bd0-4114-b1ca-3318c0a8006a','CHRG','30',130.00),(55,'60b6f641-3e44-4b45-afdc-3318c0a8006a','CHRG','30',9150.00),(56,'60b6f72d-2dc4-4fd5-a842-3318c0a8006a','CHRG','30',3745.00),(57,'60b6f885-88cc-4201-a48b-3318c0a8006a','CASH','COD',130.00),(58,'60b6f9ec-6594-48f2-908a-3318c0a8006a','CASH','COD',435.00),(59,'60b700d7-bd7c-4913-b6c9-3318c0a8006a','CASH','COD',435.00),(60,'60b701a8-d40c-46de-b08a-3318c0a8006a','CHRG','30',9150.00),(61,'60b707fa-c408-4dfd-9517-3318c0a8006a','CHRG','30',240.00),(62,'60b7086e-23f0-4e89-ae40-3318c0a8006a','CHRG','30',190.00),(63,'60b71fbb-d0dc-489d-9dab-3318c0a8006a','CHRG','30',955.00);
/*!40000 ALTER TABLE `transaction_payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transactions` (
  `id` char(36) NOT NULL DEFAULT '',
  `user` varchar(10) DEFAULT NULL,
  `type` char(10) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `entity_type` char(10) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `ref_no` varchar(25) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

LOCK TABLES `transactions` WRITE;
/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
INSERT INTO `transactions` VALUES ('609644ae-7a90-4020-af0d-02dcc0a80105','admin','po','invoiced','customer',3,'1',1500.00,0.00,0.00,0.00,0.00,NULL,'2021-05-08 15:58:38','2021-05-08 15:58:38','2021-05-08 16:01:35'),('609644cf-f680-49ac-8425-02dcc0a80105','admin','jo','invoiced','customer',3,'1',1.00,0.00,0.00,0.00,0.00,NULL,'2021-05-08 15:59:11','2021-05-08 15:59:11','2021-05-08 16:01:35'),('6096455f-755c-46b9-9a54-02dcc0a80105','admin','invoice','fulfilled','customer',3,'1',1500.00,0.00,0.00,0.00,0.00,NULL,'2021-05-08 16:01:35','2021-05-08 16:01:35','2021-05-08 16:01:35'),('609645fa-8cd0-49f2-b1a4-02dcc0a80105','admin','po','cancelled','customer',3,'2',100.00,0.00,0.00,0.00,0.00,NULL,'2021-05-08 16:04:10','2021-05-08 16:04:10','2021-05-08 16:04:10'),('60964699-a2f4-420f-92c8-02dc82e7a674','admin','po','inprogress','customer',3,'3',13000.00,0.00,0.00,0.00,0.00,NULL,'2021-05-08 16:06:49','2021-05-08 16:06:49','2021-05-08 16:09:37'),('60964741-b370-444d-9f92-02dc82e7a674','admin','jo','created','customer',3,'2',26.00,0.00,0.00,0.00,0.00,NULL,'2021-05-08 16:09:37','2021-05-08 16:09:37','2021-05-08 16:09:37'),('60a1f14c-b7d8-4090-85fe-32e8c0a80069','angelie','po','cancelled','customer',2,'4',20.00,0.00,0.00,0.00,0.00,NULL,'2021-05-17 12:30:04','2021-05-17 12:30:04','2021-05-17 12:30:04'),('60ac6e65-4cc4-46d3-83f1-2fb482e7a674','admin','po','cancelled','customer',2,'5',3500.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 11:26:28','2021-05-25 11:26:29','2021-05-25 11:26:29'),('60ac721e-25f8-477f-9dbb-2fb482e7a674','admin','po','cancelled','customer',2,'6',7500.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 11:42:22','2021-05-25 11:42:22','2021-05-25 11:42:22'),('60ac7ffb-62dc-4ef9-b5f8-2fb4c0a8006a','angelie','po','invoiced','customer',135,'7',415.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 12:41:31','2021-05-25 12:41:31','2021-05-25 13:02:21'),('60ac84c7-bb6c-4d7f-a5f6-2fb482e7a674',NULL,'jo','invoiced','customer',135,'3',7.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 13:01:59','2021-05-25 13:01:59','2021-05-25 13:02:21'),('60ac84dc-9448-4f70-be48-2fb482e7a674',NULL,'invoice','fulfilled','customer',135,'2',415.00,0.00,0.00,49.80,0.00,NULL,'2021-05-25 13:02:20','2021-05-25 13:02:20','2021-05-25 13:02:21'),('60ac854d-7e48-4c37-8bbc-2fb4c0a8006a','angelie','po','invoiced','customer',6,'8',1000.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 13:04:13','2021-05-25 13:04:13','2021-05-25 13:06:39'),('60ac85c0-a594-4129-9901-2fb4c0a8006a','angelie','jo','invoiced','customer',6,'4',1.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 13:06:08','2021-05-25 13:06:08','2021-05-25 13:06:39'),('60ac85df-6fdc-4298-83a5-2fb4c0a8006a','angelie','invoice','fulfilled','customer',6,'3',1000.00,0.00,0.00,120.00,0.00,NULL,'2021-05-25 13:06:39','2021-05-25 13:06:39','2021-05-25 13:06:39'),('60ac9842-d7d4-446a-b510-2fb4c0a8006a','mhoneth','po','invoiced','customer',87,'9',425.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 14:25:06','2021-05-25 14:25:06','2021-05-25 14:27:25'),('60ac98aa-b024-46d1-b187-2fb4c0a8006a','mhoneth','jo','invoiced','customer',87,'5',4.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 14:26:50','2021-05-25 14:26:50','2021-05-25 14:27:25'),('60ac98cd-d18c-4294-a3b6-2fb4c0a8006a','mhoneth','invoice','fulfilled','customer',87,'4',425.00,0.00,0.00,51.00,0.00,NULL,'2021-05-25 14:27:25','2021-05-25 14:27:25','2021-05-25 14:27:25'),('60ac9c14-ba94-4c36-8428-2fb4c0a8006a','mhoneth','po','invoiced','customer',87,'10',700.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 14:41:24','2021-05-25 14:41:24','2021-05-25 14:44:03'),('60ac9c7b-7cdc-4c45-888b-2fb4c0a8006a','mhoneth','jo','invoiced','customer',87,'6',1.00,0.00,0.00,0.00,0.00,NULL,'2021-05-25 14:43:07','2021-05-25 14:43:07','2021-05-25 14:44:03'),('60ac9cb3-f11c-4e81-b034-2fb4c0a8006a','mhoneth','invoice','fulfilled','customer',87,'5',700.00,0.00,0.00,84.00,0.00,NULL,'2021-05-25 14:44:03','2021-05-25 14:44:03','2021-05-25 14:44:03'),('60b5ad09-283c-4bc5-aa9a-320cc0a8006a','mhoneth','po','inprogress','customer',143,'11',26500.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 11:44:09','2021-06-01 11:44:09','2021-06-01 13:46:21'),('60b5adcd-1320-45ab-a37e-320cc0a8006a','railim','po','created','customer',41,'12',25500.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 11:47:25','2021-06-01 11:47:25','2021-06-01 15:53:03'),('60b5aea7-f794-4eaa-b260-320cc0a8006a','mhoneth','po','invoiced','customer',106,'13',640.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 11:51:03','2021-06-01 11:51:03','2021-06-01 12:39:49'),('60b5af83-1588-4d43-9005-320cc0a8006a','mhoneth','po','invoiced','customer',7,'14',1260.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 11:54:43','2021-06-01 11:54:43','2021-06-01 13:51:04'),('60b5b0e1-b848-4ffa-9d78-320cc0a8006a','mhoneth','po','created','customer',29,'15',1750.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:00:33','2021-06-01 12:00:33','2021-06-01 12:00:33'),('60b5b1bd-34a8-4cf3-843e-320cc0a8006a','mhoneth','po','invoiced','customer',47,'16',500.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:04:13','2021-06-01 12:04:13','2021-06-01 12:18:09'),('60b5b20b-921c-429e-92b9-320cc0a8006a','mhoneth','jo','invoiced','customer',47,'7',1.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:05:31','2021-06-01 12:05:31','2021-06-01 12:18:09'),('60b5b269-f404-4f08-aec1-320cc0a8006a','mhoneth','invoice','fulfilled','customer',47,'6',500.00,0.00,0.00,60.00,0.00,NULL,'2021-06-01 12:07:05','2021-06-01 12:07:05','2021-06-01 12:07:05'),('60b5b3d1-9f80-4224-84a8-320cc0a8006a','mhoneth','po','invoiced','customer',144,'17',1540.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:13:05','2021-06-01 12:13:05','2021-06-01 12:18:19'),('60b5b4da-60f8-463a-86ba-320cc0a8006a','mhoneth','jo','invoiced','customer',144,'8',2.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:17:30','2021-06-01 12:17:30','2021-06-01 12:18:19'),('60b5b501-27ec-4610-b00b-320c82e7a674','admin','invoice','fulfilled','customer',47,'7',500.00,0.00,0.00,53.57,0.00,NULL,'2021-06-01 12:18:09','2021-06-01 12:18:09','2021-06-01 12:18:09'),('60b5b50b-b250-44bd-b69c-320cc0a8006a','mhoneth','invoice','fulfilled','customer',144,'8',1540.00,0.00,0.00,184.80,0.00,NULL,'2021-06-01 12:18:19','2021-06-01 12:18:19','2021-06-01 12:18:19'),('60b5b5fc-9c0c-4f50-b1e8-320cc0a8006a','mhoneth','po','created','customer',9,'18',155.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:22:20','2021-06-01 12:22:20','2021-06-01 12:22:20'),('60b5b9eb-c3e4-4a77-87f6-320cc0a8006a','mhoneth','jo','invoiced','customer',106,'9',3.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:39:07','2021-06-01 12:39:07','2021-06-01 12:39:49'),('60b5ba15-b914-4e63-99c2-320cc0a8006a','mhoneth','invoice','fulfilled','customer',106,'9',640.00,0.00,0.00,76.80,0.00,NULL,'2021-06-01 12:39:49','2021-06-01 12:39:49','2021-06-01 12:39:49'),('60b5bbf3-cb80-490a-b230-320cc0a8006a','mhoneth','po','invoiced','customer',83,'19',840.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:47:47','2021-06-01 12:47:47','2021-06-01 12:49:07'),('60b5bc26-5ed4-4039-ab35-320cc0a8006a','mhoneth','jo','invoiced','customer',83,'10',1.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 12:48:38','2021-06-01 12:48:38','2021-06-01 12:49:07'),('60b5bc43-3f88-4817-888a-320cc0a8006a','mhoneth','invoice','fulfilled','customer',83,'10',840.00,0.00,0.00,90.00,0.00,NULL,'2021-06-01 12:49:07','2021-06-01 12:49:07','2021-06-01 12:49:07'),('60b5bf3a-aaa8-46af-9075-320cc0a8006a','mhoneth','po','invoiced','customer',10,'20',155.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 13:01:46','2021-06-01 13:01:46','2021-06-01 14:20:57'),('60b5c3d8-67ac-4a8e-b074-320cc0a8006a','mhoneth','po','inprogress','customer',119,'21',4000.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 13:21:28','2021-06-01 13:21:28','2021-06-01 17:04:06'),('60b5c4e0-0328-4ce1-9c24-320cc0a8006a','mhoneth','po','inprogress','customer',100,'22',155.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 13:25:52','2021-06-01 13:25:52','2021-06-01 14:17:53'),('60b5c8df-67f8-43d4-9edc-320cc0a8006a','mhoneth','po','inprogress','customer',52,'23',385.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 13:42:55','2021-06-01 13:42:55','2021-06-01 14:17:22'),('60b5c9ad-f360-4c6f-96bc-320cc0a8006a','mhoneth','jo','created','customer',143,'11',2.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 13:46:21','2021-06-01 13:46:21','2021-06-01 13:46:21'),('60b5ca93-6304-4e16-be90-320cc0a8006a','mhoneth','jo','invoiced','customer',7,'12',3.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 13:50:11','2021-06-01 13:50:11','2021-06-01 13:51:04'),('60b5cac8-c670-4a5c-82cf-320cc0a8006a','mhoneth','invoice','fulfilled','customer',7,'11',1260.00,0.00,0.00,135.00,0.00,NULL,'2021-06-01 13:51:04','2021-06-01 13:51:04','2021-06-01 13:51:04'),('60b5d0f2-ef48-40ca-9d3f-320cc0a8006a','mhoneth','jo','created','customer',52,'13',1.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 14:17:21','2021-06-01 14:17:22','2021-06-01 14:17:22'),('60b5d111-209c-40c2-86dc-320cc0a8006a','mhoneth','jo','created','customer',100,'14',2.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 14:17:53','2021-06-01 14:17:53','2021-06-01 14:17:53'),('60b5d131-8208-469a-b8e8-320cc0a8006a','mhoneth','jo','invoiced','customer',10,'15',1.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 14:18:25','2021-06-01 14:18:25','2021-06-01 14:20:57'),('60b5d16e-1f18-490a-86d8-320cc0a8006a','mhoneth','jo','created','customer',41,'16',5.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 14:19:26','2021-06-01 14:19:26','2021-06-01 14:19:26'),('60b5d1c9-d760-4eb5-af51-320cc0a8006a','mhoneth','invoice','fulfilled','customer',10,'12',155.00,0.00,0.00,16.61,0.00,NULL,'2021-06-01 14:20:57','2021-06-01 14:20:57','2021-06-01 14:20:57'),('60b5d2d4-c5f4-4e5b-843b-320cc0a8006a','mhoneth','po','inprogress','customer',100,'24',270.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 14:25:24','2021-06-01 14:25:24','2021-06-01 17:03:09'),('60b5dba7-f0ac-4d18-a6dd-320cc0a8006a','mhoneth','po','invoiced','customer',49,'25',1290.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 15:03:03','2021-06-01 15:03:03','2021-06-02 10:10:04'),('60b5e92b-5cd0-4917-9c3b-320cc0a8006a','mhoneth','po','invoiced','customer',145,'26',8700.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 16:00:43','2021-06-01 16:00:43','2021-06-02 10:04:39'),('60b5ed81-db90-4364-8309-320cc0a8006a','mhoneth','po','invoiced','customer',124,'27',2075.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 16:19:13','2021-06-01 16:19:13','2021-06-02 09:33:23'),('60b5eddf-8470-4463-9e4d-320cc0a8006a','mhoneth','jo','invoiced','customer',124,'17',2.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 16:20:47','2021-06-01 16:20:47','2021-06-02 09:33:23'),('60b5f3c2-f180-4713-8704-320cc0a8006a','mhoneth','po','invoiced','customer',12,'28',3745.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 16:45:54','2021-06-01 16:45:54','2021-06-02 11:12:45'),('60b5f773-a7b0-4792-84de-320cc0a8006a','mhoneth','po','invoiced','customer',147,'29',12880.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:01:39','2021-06-01 17:01:39','2021-06-02 09:53:25'),('60b5f7cd-bc1c-4366-b7cd-320cc0a8006a','mhoneth','jo','created','customer',100,'18',2.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:03:09','2021-06-01 17:03:09','2021-06-01 17:03:09'),('60b5f806-206c-4ea4-bb57-320cc0a8006a','mhoneth','jo','created','customer',119,'19',1.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:04:06','2021-06-01 17:04:06','2021-06-01 17:04:06'),('60b5f821-f154-41b8-9b61-320cc0a8006a','mhoneth','jo','invoiced','customer',145,'20',5.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:04:33','2021-06-01 17:04:33','2021-06-02 10:04:39'),('60b5f897-98c8-4763-867c-320cc0a8006a','mhoneth','po','cancelled','customer',56,'30',1260.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:06:31','2021-06-01 17:06:31','2021-06-01 17:35:16'),('60b5f965-4734-4a4f-9773-320cc0a8006a','mhoneth','po','invoiced','customer',31,'31',2480.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:09:57','2021-06-01 17:09:57','2021-06-02 09:37:39'),('60b5fb11-62f4-4be0-a661-320cc0a8006a','mhoneth','po','invoiced','customer',146,'32',4910.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:17:05','2021-06-01 17:17:05','2021-06-02 09:42:22'),('60b60073-c15c-4a32-bcb2-320cc0a8006a','mhoneth','po','invoiced','customer',56,'33',880.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 17:40:03','2021-06-01 17:40:03','2021-06-01 18:01:36'),('60b60549-1164-4a0b-b817-320cc0a8006a','mhoneth','jo','invoiced','customer',56,'21',16.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 18:00:41','2021-06-01 18:00:41','2021-06-01 18:01:36'),('60b60580-3b80-4f94-ad53-320cc0a8006a','mhoneth','invoice','fulfilled','customer',56,'13',880.00,0.00,0.00,94.29,0.00,NULL,'2021-06-01 18:01:36','2021-06-01 18:01:36','2021-06-01 18:01:36'),('60b606b7-c8f0-452b-9ccc-320cc0a8006a','mhoneth','jo','invoiced','customer',146,'22',13.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 18:06:47','2021-06-01 18:06:47','2021-06-02 09:42:22'),('60b606e3-f9b8-460d-af51-320cc0a8006a','mhoneth','jo','invoiced','customer',31,'23',3.00,0.00,0.00,0.00,0.00,NULL,'2021-06-01 18:07:31','2021-06-01 18:07:31','2021-06-02 09:37:39'),('60b6dc93-b84c-48e7-ae55-3318c0a8006a','mhoneth','po','created','customer',45,'34',880.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 09:19:15','2021-06-02 09:19:15','2021-06-02 09:19:15'),('60b6dfe3-1328-48a3-8bc0-3318c0a8006a','mhoneth','invoice','fulfilled','customer',124,'14',2075.00,0.00,0.00,222.32,0.00,NULL,'2021-06-02 09:33:23','2021-06-02 09:33:23','2021-06-02 09:33:23'),('60b6e0e3-7a54-4738-8efc-3318c0a8006a','mhoneth','invoice','fulfilled','customer',31,'15',2480.00,0.00,0.00,265.71,0.00,NULL,'2021-06-02 09:37:39','2021-06-02 09:37:39','2021-06-02 09:37:39'),('60b6e1fe-b63c-4cc6-be32-3318c0a8006a','mhoneth','invoice','fulfilled','customer',146,'16',4910.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 09:42:22','2021-06-02 09:42:22','2021-06-02 09:42:22'),('60b6e46c-cc60-4a40-a5de-3318c0a8006a','mhoneth','jo','invoiced','customer',147,'24',14.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 09:52:44','2021-06-02 09:52:44','2021-06-02 09:53:25'),('60b6e495-0d14-4db7-887c-3318c0a8006a','mhoneth','invoice','fulfilled','customer',147,'17',12880.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 09:53:25','2021-06-02 09:53:25','2021-06-02 09:53:25'),('60b6e737-1210-4dd1-8dc7-3318c0a8006a','mhoneth','invoice','fulfilled','customer',145,'18',8700.00,0.00,0.00,932.14,0.00,NULL,'2021-06-02 10:04:39','2021-06-02 10:04:39','2021-06-02 10:04:39'),('60b6e810-9d2c-488d-aed5-3318c0a8006a','mhoneth','jo','invoiced','customer',49,'25',1.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 10:08:16','2021-06-02 10:08:16','2021-06-02 10:10:04'),('60b6e87c-a554-4b6e-8bf8-3318c0a8006a','mhoneth','invoice','fulfilled','customer',49,'19',1290.00,0.00,0.00,138.21,0.00,NULL,'2021-06-02 10:10:04','2021-06-02 10:10:04','2021-06-02 10:10:04'),('60b6f19d-4bd0-4114-b1ca-3318c0a8006a','mhoneth','po','invoiced','customer',132,'35',130.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 10:49:01','2021-06-02 10:49:01','2021-06-02 11:18:29'),('60b6f641-3e44-4b45-afdc-3318c0a8006a','mhoneth','po','invoiced','customer',4,'36',9150.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 11:08:49','2021-06-02 11:08:49','2021-06-02 11:57:28'),('60b6f6e9-22b4-4a46-ac04-3318c0a8006a','mhoneth','jo','invoiced','customer',12,'26',13.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 11:11:37','2021-06-02 11:11:37','2021-06-02 11:12:45'),('60b6f72d-2dc4-4fd5-a842-3318c0a8006a','mhoneth','invoice','fulfilled','customer',12,'20',3745.00,0.00,0.00,401.25,0.00,NULL,'2021-06-02 11:12:45','2021-06-02 11:12:45','2021-06-02 11:12:45'),('60b6f849-b684-4cfe-9b9c-3318c0a8006a','mhoneth','jo','invoiced','customer',132,'27',2.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 11:17:28','2021-06-02 11:17:29','2021-06-02 11:18:29'),('60b6f885-88cc-4201-a48b-3318c0a8006a','mhoneth','invoice','fulfilled','customer',132,'21',130.00,0.00,0.00,13.93,0.00,NULL,'2021-06-02 11:18:29','2021-06-02 11:18:29','2021-06-02 11:18:29'),('60b6f9ec-6594-48f2-908a-3318c0a8006a','mhoneth','po','invoiced','customer',113,'37',435.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 11:24:28','2021-06-02 11:24:28','2021-06-02 11:53:59'),('60b700ab-85a8-4a10-93e8-3318c0a8006a','mhoneth','jo','invoiced','customer',113,'28',1.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 11:53:14','2021-06-02 11:53:15','2021-06-02 11:53:59'),('60b700d7-bd7c-4913-b6c9-3318c0a8006a','mhoneth','invoice','fulfilled','customer',113,'22',435.00,0.00,0.00,46.61,0.00,NULL,'2021-06-02 11:53:59','2021-06-02 11:53:59','2021-06-02 11:53:59'),('60b70181-5fb8-45aa-a8c6-3318c0a8006a','mhoneth','jo','invoiced','customer',4,'29',6.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 11:56:49','2021-06-02 11:56:49','2021-06-02 11:57:28'),('60b701a8-d40c-46de-b08a-3318c0a8006a','mhoneth','invoice','fulfilled','customer',4,'23',9150.00,0.00,0.00,980.36,0.00,NULL,'2021-06-02 11:57:27','2021-06-02 11:57:28','2021-06-02 11:57:28'),('60b707fa-c408-4dfd-9517-3318c0a8006a','mhoneth','po','created','customer',148,'38',240.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 12:24:26','2021-06-02 12:24:26','2021-06-02 12:24:26'),('60b7086e-23f0-4e89-ae40-3318c0a8006a','mhoneth','po','created','customer',128,'39',190.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 12:26:22','2021-06-02 12:26:22','2021-06-02 12:26:22'),('60b71fbb-d0dc-489d-9dab-3318c0a8006a','mhoneth','po','created','customer',64,'40',955.00,0.00,0.00,0.00,0.00,NULL,'2021-06-02 14:05:47','2021-06-02 14:05:47','2021-06-02 14:05:47');
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `username` varchar(10) DEFAULT NULL,
  `password` varchar(150) DEFAULT NULL,
  `status` varchar(10) DEFAULT 'active',
  `type` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'Admin','admin','admin','82efa65cdba90e9272ca5d4650062659','active','staff'),(2,'Railim','Dept','railim','5f4dcc3b5aa765d61d8327deb882cf99','active','staff'),(3,'Kristin','Secang','kristin','5f4dcc3b5aa765d61d8327deb882cf99','active','staff'),(4,'Angelie','Samillano','angelie','5f4dcc3b5aa765d61d8327deb882cf99','active','staff'),(5,'Lalaine','Payas','lalaine','5f4dcc3b5aa765d61d8327deb882cf99','active','staff'),(6,'Cutter','Cutter','cutter','827ccb0eea8a706c4c34a16891f84e7b','active','staff'),(7,'Mhoneth','Hernandez','mhoneth','5f4dcc3b5aa765d61d8327deb882cf99','active','staff');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2021-06-02 13:53:50
