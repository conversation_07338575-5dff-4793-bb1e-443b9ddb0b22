-- Update net_of_vat (Ensure tax is properly subtracted)
UPDATE invoices inv
SET inv.net_of_vat = inv.total - inv.tax
WHERE inv.invoice_date >= "2025-02-01";

-- Update with_holding_tax (Fix condition)
UPDATE invoices inv
SET inv.with_holding_tax = inv.net_of_vat * 0.01
WHERE inv.invoice_date >= "2025-02-01"
AND (inv.with_holding_tax IS NULL OR inv.with_holding_tax = 0);

UPDATE invoices inv
SET inv.with_holding_tax = 0
WHERE inv.invoice_date >= "2025-02-01"
AND inv.dr_no IS NOT NULL;

-- Sync the transactions table with updated invoice values
UPDATE transactions trnx
INNER JOIN invoices inv ON trnx.ref_no = inv.id
AND trnx.`type` = 'invoice'
SET 
    trnx.net_of_vat = inv.net_of_vat,
    trnx.with_holding_tax = inv.with_holding_tax
WHERE inv.invoice_date >= "2025-02-01";


-- Verify the updates
SELECT *
FROM invoices inv
INNER JOIN transactions trnx ON trnx.ref_no = inv.id
AND trnx.`type` = 'invoice'
WHERE inv.invoice_date >= "2025-02-01";
