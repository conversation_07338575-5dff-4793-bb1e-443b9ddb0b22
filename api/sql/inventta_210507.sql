-- MySQL dump 10.13  Distrib 5.6.24, for osx10.8 (x86_64)
--
-- Host: 127.0.0.1    Database: inventta_2105007
-- ------------------------------------------------------
-- Server version	5.5.5-10.1.38-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `cash_flows`
--

DROP TABLE IF EXISTS `cash_flows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cash_flows` (
  `id` char(36) NOT NULL,
  `particulars` varchar(25) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `flag` char(1) NOT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cash_flows`
--

LOCK TABLES `cash_flows` WRITE;
/*!40000 ALTER TABLE `cash_flows` DISABLE KEYS */;
/*!40000 ALTER TABLE `cash_flows` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `id` char(4) NOT NULL,
  `name` varchar(30) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `categories`
--

LOCK TABLES `categories` WRITE;
/*!40000 ALTER TABLE `categories` DISABLE KEYS */;
/*!40000 ALTER TABLE `categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customer_ledgers`
--

DROP TABLE IF EXISTS `customer_ledgers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `customer_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer_ledgers`
--

LOCK TABLES `customer_ledgers` WRITE;
/*!40000 ALTER TABLE `customer_ledgers` DISABLE KEYS */;
/*!40000 ALTER TABLE `customer_ledgers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) DEFAULT NULL,
  `alias` varchar(20) NOT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `tin` varchar(20) NOT NULL,
  `address` varchar(150) NOT NULL,
  `business_style` varchar(150) NOT NULL,
  `tax_type` char(3) NOT NULL COMMENT 'VAT - VATable, ZRO -  Non-Vat',
  `unit_system` char(3) NOT NULL COMMENT 'ENG - English, MET - Metric',
  `last_bill` int(11) NOT NULL,
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES (1,'CASH','CASH','open','','','','','',0,0.00,0.00,NULL,'2021-05-07 14:14:05'),(2,'Customer A','C.A.','open','1232342','Sto Tomas Batangas','Construction','ZRO','ENG',0,-15200.00,-15200.00,'2021-02-27 22:39:20','2021-05-07 14:15:36'),(3,'Raitech','Raitech','open','12344','Cabuyao Laguna','Engineering Services','ZRO','MET',0,0.00,0.00,'2021-05-07 14:14:37','2021-05-07 14:14:37');
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `deliveries`
--

DROP TABLE IF EXISTS `deliveries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `deliveries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_date` date DEFAULT NULL,
  `supplier` varchar(50) DEFAULT NULL,
  `doc_no` int(11) DEFAULT NULL,
  `source` char(10) DEFAULT 'delivery' COMMENT 'return,order,delivery',
  `total` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `deliveries`
--

LOCK TABLES `deliveries` WRITE;
/*!40000 ALTER TABLE `deliveries` DISABLE KEYS */;
/*!40000 ALTER TABLE `deliveries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_details`
--

DROP TABLE IF EXISTS `delivery_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_details`
--

LOCK TABLES `delivery_details` WRITE;
/*!40000 ALTER TABLE `delivery_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_payments`
--

DROP TABLE IF EXISTS `delivery_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_payments`
--

LOCK TABLES `delivery_payments` WRITE;
/*!40000 ALTER TABLE `delivery_payments` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_adjustments`
--

DROP TABLE IF EXISTS `inventory_adjustments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_adjustments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_adjustments`
--

LOCK TABLES `inventory_adjustments` WRITE;
/*!40000 ALTER TABLE `inventory_adjustments` DISABLE KEYS */;
/*!40000 ALTER TABLE `inventory_adjustments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_logs`
--

DROP TABLE IF EXISTS `inventory_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `old_quantity` int(11) DEFAULT NULL,
  `act_quantity` int(11) DEFAULT NULL,
  `new_quantity` int(11) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_logs`
--

LOCK TABLES `inventory_logs` WRITE;
/*!40000 ALTER TABLE `inventory_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `inventory_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice_details`
--

DROP TABLE IF EXISTS `invoice_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `description` varchar(50) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_details`
--

LOCK TABLES `invoice_details` WRITE;
/*!40000 ALTER TABLE `invoice_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `invoice_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice_payments`
--

DROP TABLE IF EXISTS `invoice_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_payments`
--

LOCK TABLES `invoice_payments` WRITE;
/*!40000 ALTER TABLE `invoice_payments` DISABLE KEYS */;
/*!40000 ALTER TABLE `invoice_payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `invoice_date` date DEFAULT NULL,
  `terms` int(11) NOT NULL,
  `term_date` date NOT NULL,
  `po_no` varchar(20) NOT NULL COMMENT 'Purchase Order No',
  `po_date` date NOT NULL COMMENT 'PO Date',
  `si_no` varchar(20) NOT NULL COMMENT 'Sales Invoice No',
  `si_date` date NOT NULL COMMENT 'SI Date',
  `dr_no` varchar(20) NOT NULL COMMENT 'Delivery Receipt No',
  `dr_date` date NOT NULL COMMENT 'DR Date',
  `cr_no` varchar(20) NOT NULL COMMENT 'Collection Receipt No',
  `cr_date` date NOT NULL COMMENT 'CR Date',
  `customer` varchar(80) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `created` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

LOCK TABLES `invoices` WRITE;
/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `job_order_details`
--

DROP TABLE IF EXISTS `job_order_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `length` int(11) NOT NULL,
  `length_actual` int(11) NOT NULL,
  `width` int(11) NOT NULL,
  `width_actual` int(11) NOT NULL,
  `thickness` int(11) NOT NULL,
  `thickness_actual` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `quantity_actual` int(11) NOT NULL,
  `quantity_area` int(11) NOT NULL,
  `quantity_area_actual` int(11) NOT NULL,
  `po_price` decimal(8,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `job_order_details`
--

LOCK TABLES `job_order_details` WRITE;
/*!40000 ALTER TABLE `job_order_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `job_order_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `job_orders`
--

DROP TABLE IF EXISTS `job_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `po_no` varchar(20) NOT NULL,
  `po_date` date NOT NULL,
  `jo_date` date DEFAULT NULL,
  `user` varchar(80) DEFAULT NULL,
  `remarks` varchar(150) DEFAULT NULL,
  `created` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `job_orders`
--

LOCK TABLES `job_orders` WRITE;
/*!40000 ALTER TABLE `job_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `job_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `module_users`
--

DROP TABLE IF EXISTS `module_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `module_users` (
  `id` char(36) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `module_id` char(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `module_users`
--

LOCK TABLES `module_users` WRITE;
/*!40000 ALTER TABLE `module_users` DISABLE KEYS */;
INSERT INTO `module_users` VALUES ('574fe2cb-001c-466c-8f4b-04d8c0a80107',6,'users'),('574fe2cb-0d5c-463b-a870-04d8c0a80107',6,'ordrs'),('574fe2cb-1a9c-4492-a90c-04d8c0a80107',6,'sales'),('574fe2cb-6bd8-4887-8088-04d8c0a80107',6,'invtry'),('574fe2cb-797c-4eef-bd3b-04d8c0a80107',6,'rtrnordr'),('574fe2cb-7ad4-41e9-91f5-04d8c0a80107',6,'accnt'),('574fe2cb-93fc-4b98-a9a6-04d8c0a80107',6,'asssmnt'),('574fe2cb-a13c-4f61-8151-04d8c0a80107',6,'trnx'),('574fe2cb-d7f8-4c09-92e2-04d8c0a80107',6,'ldgr'),('574fe2cb-e538-4c02-8bd4-04d8c0a80107',6,'stckrm'),('574fe2cb-f2dc-440f-8790-04d8c0a80107',6,'delvry'),('5bff6078-162c-4838-9067-0850c0a8fe07',5,'ordrs'),('5bff6078-162c-4b09-bf80-0850c0a8fe07',5,'rtrnordr'),('5bff6078-23d0-44e2-b89f-0850c0a8fe07',5,'users'),('5bff6078-23d0-45b8-b2e0-0850c0a8fe07',5,'accnt'),('5bff6078-258c-49da-b1d3-0850c0a8fe07',5,'invtry'),('5bff6078-3110-4557-bf06-0850c0a8fe07',5,'ldgr'),('5bff6078-8f8c-4151-95da-0850c0a8fe07',5,'stckrm'),('5bff6078-9d30-4965-b407-0850c0a8fe07',5,'asssmnt'),('5bff6078-9d30-4994-b685-0850c0a8fe07',5,'sales'),('5bff6078-aa70-42ed-948a-0850c0a8fe07',5,'delvry'),('5bff6078-aa70-4f21-bbde-0850c0a8fe07',5,'trnx'),('602a5ec9-1ac0-477a-a86c-745d68f89d23',2,'rtrnordr'),('602a5ec9-40c0-436a-881f-745d68f89d23',2,'trnx'),('602a5ec9-6ee0-4c9e-9677-745d68f89d23',2,'ordrs'),('602a5ec9-71b8-45b6-a2b6-745d68f89d23',2,'invtry'),('602a5ec9-9918-47de-9259-745d68f89d23',2,'asssmnt'),('602a5ec9-ae4c-46ce-ac08-745d68f89d23',2,'users'),('602a5ec9-b188-4322-b450-745d68f89d23',2,'sales'),('602a5ec9-e4bc-4d0e-99d9-745d68f89d23',2,'delvry'),('607d8c2c-1834-4ca5-922d-444c82e7a674',1,'accnt'),('607d8c2c-3cac-44ad-a9e5-456482e7a674',1,'jo'),('607d8c2c-51e8-4910-bd21-4eda82e7a674',1,'stckrm'),('607d8c2c-7cd8-4fbb-b33c-42dd82e7a674',1,'montr'),('607d8c2c-8148-43ce-8db3-4be982e7a674',1,'ldgr'),('607d8c2c-9794-40f1-8412-4b1a82e7a674',1,'invoice'),('607d8c2c-ad90-416a-af6e-4ff282e7a674',1,'po'),('607d8c2c-bc0c-4d80-90e3-49ad82e7a674',1,'users'),('607d8c2c-cee8-4e86-8979-48cf82e7a674',1,'trnx'),('607d8c2c-e9f8-40a3-9a85-474982e7a674',1,'invtry');
/*!40000 ALTER TABLE `module_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `modules`
--

DROP TABLE IF EXISTS `modules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `modules` (
  `id` char(10) NOT NULL,
  `title` varchar(20) DEFAULT NULL,
  `link` varchar(50) DEFAULT NULL,
  `description` varchar(150) DEFAULT NULL,
  `icon` varchar(30) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `type` char(2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `modules`
--

LOCK TABLES `modules` WRITE;
/*!40000 ALTER TABLE `modules` DISABLE KEYS */;
INSERT INTO `modules` VALUES ('accnt','Accounts','accounts/homepage','Manage customer or supplier information and outstanding balances.','user',3,'BR'),('asssmnt','Assessment','inventory/assessment','Process trade in and return transaction from customers using the assessment module.','tags',2,'FD'),('delvry','Deliveries','deliveries/homepage','Record incoming items using the delivery module.','log-in',3,'FD'),('invoice','Sales Invoice','sales/invoice','Complete PO with Sales Invoice','shopping-cart',1,'FD'),('invtry','Inventory','inventory/homepage','Keep track of your stocks and pricing using the','inbox',1,'BR'),('jo','Job Order','orders/job','Fulfill orders','object-align-bottom',6,'BR'),('ldgr','Ledgers','accounts/ledger','Review customer or supplier charges and payments.','th-list',4,'BR'),('montr','PO Monitoring','transactions/monitoring','Monitor PO transactions','transfer',2,'BR'),('ordrs','Orders','orders/homepage','Request new inventory using the purchase order module.','log-out',4,'FD'),('po','Purchase Order','orders/purchase','Create PO from customers','shopping-cart',6,'FD'),('rtrnordr','Return Orders','inventory/returnorder','Send back items to supplier using the return.','repeat',5,'FD'),('sales','Sales','sales/homepage','Sell products using the point of sales module.','shopping-cart',1,'FD'),('stckrm','Stock Room','inventory/stockroom','Update your physical count using the stock room module.','object-align-bottom',6,'BR'),('trnx','Transactions','transactions/homepage','Review all inbound and outbound transactions.','transfer',2,'BR'),('users','Users','accounts/users','Manage staff\'s access using the user module.','pushpin',5,'BR');
/*!40000 ALTER TABLE `modules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_details`
--

DROP TABLE IF EXISTS `order_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_details`
--

LOCK TABLES `order_details` WRITE;
/*!40000 ALTER TABLE `order_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier` varchar(50) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `price_logs`
--

DROP TABLE IF EXISTS `price_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `price_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `old_price` decimal(10,2) DEFAULT NULL,
  `new_price` decimal(10,2) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `ref_no` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `price_logs`
--

LOCK TABLES `price_logs` WRITE;
/*!40000 ALTER TABLE `price_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `price_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` char(4) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'active',
  `particular` varchar(50) DEFAULT ' ',
  `length` decimal(7,3) DEFAULT NULL,
  `width` decimal(7,3) DEFAULT NULL,
  `thickness` decimal(7,3) NOT NULL,
  `type` char(3) NOT NULL COMMENT 'IMP - Import, LOC -  Local',
  `allowance` int(11) NOT NULL,
  `part_no` varchar(30) DEFAULT ' ',
  `unit` varchar(10) DEFAULT ' ',
  `description` varchar(255) DEFAULT ' ',
  `capital` decimal(8,2) DEFAULT NULL,
  `markup` decimal(8,2) DEFAULT NULL,
  `srp` decimal(8,2) DEFAULT NULL,
  `tmp_srp` decimal(10,2) NOT NULL,
  `soh_quantity` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `min_quantity` int(11) DEFAULT NULL,
  `max_quantity` int(11) DEFAULT NULL,
  `discountable` tinyint(1) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=712 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES (1,'PLT8','active','AS ACRYLIC  (CLEAR) ',1219.200,2438.400,3.500,'LOC',3,'','mm','AS ACRYLIC',0.00,0.00,0.00,500.00,2867107,0,0,0,0,'2020-02-27 01:23:45','2021-05-07 14:02:05'),(2,'PLT8','active','AS ACRYLIC  (CLEAR) ',1219.200,2438.400,5.000,'IMP',0,'','mm','AS ACRYLIC',0.00,0.00,0.00,100.00,2972642,0,0,0,0,'2020-02-27 01:23:45','2021-04-21 14:56:57'),(3,'PLT8','active','AS ACRYLIC  (CLEAR) ',1219.200,2438.400,6.000,'',0,'','mm','AS ACRYLIC',0.00,0.00,0.00,50.00,2972895,0,0,0,0,'2020-02-27 01:23:45','2021-04-22 14:45:13'),(4,'PLT8','active','AS ACRYLIC  (CLEAR) ',1219.200,2438.400,10.000,'',0,'','mm','AS ACRYLIC',0.00,0.00,0.00,50.00,2971897,0,0,0,0,'2020-02-27 01:23:45','2021-04-19 10:43:27'),(5,'PLT8','active','AS POLYCARBONATE  (CLEAR) ',1219.200,2438.400,2.000,'',0,'','mm','AS POLYCAR',0.00,0.00,0.00,500.00,2803971,0,0,0,0,'2020-02-27 01:23:45','2021-04-23 06:16:27'),(6,'PLT8','active','AS POLYCARBONATE  (CLEAR) ',1219.200,2438.400,3.000,'',0,'','mm','AS POLYCAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(7,'PLT8','active','AS POLYCARBONATE  (CLEAR) ',1219.200,2438.400,4.000,'',0,'','mm','AS POLYCAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(8,'PLT8','active','AS POLYCARBONATE  (CLEAR) ',1219.200,2438.400,5.000,'',0,'','mm','AS POLYCAR',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(9,'PLT8','active','AS POLYCARBONATE  (CLEAR) ',1219.200,2438.400,6.000,'',0,'','mm','AS POLYCAR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(10,'PLT8','active','AS POLYCARBONATE  (CLEAR) ',1219.200,2438.400,10.000,'',0,'','mm','AS POLYCAR',0.00,0.00,0.00,0.00,-55125,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(11,'PLT8','active','PET  (CLEAR)',1200.000,1200.000,3.000,'',0,'','mm','PET  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(12,'PLT8','active','PET  (CLEAR)',1200.000,2000.000,5.000,'',0,'','mm','PET  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(13,'PLT8','active','PET  (CLEAR)',1200.000,2000.000,6.000,'',0,'','mm','PET  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(14,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,2.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(15,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,3.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(16,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,4.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(17,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,5.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(18,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,6.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(19,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,8.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(20,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,10.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,-10000,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(21,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,12.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(22,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,15.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(23,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,20.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(24,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,20.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(25,'PLT8','active','POLYCARBONATE   (CLEAR) ',1219.200,2438.400,25.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(26,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,2.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(27,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,3.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(28,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,5.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(29,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,6.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(30,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,8.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(31,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,10.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(32,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,12.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(33,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,15.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(34,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,20.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(35,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,25.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(36,'PLT8','active','PVC  (CLEAR)',1219.200,2438.400,30.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(37,'PLT8','active','PVC  (CLEAR)',1000.000,2000.000,40.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(38,'PLT8','active','PVC  (CLEAR)',1000.000,2000.000,50.000,'',0,'','mm','PVC  (CLEA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(39,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,3.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(40,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,5.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(41,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,6.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(42,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,8.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(43,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,10.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(44,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,12.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(45,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,15.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(46,'PLT8','active','ABS  (IVORY) ',1000.000,2000.000,20.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(47,'PLT8','active','ABS  (IVORY) ',1200.000,2000.000,25.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(48,'PLT8','active','ABS  (IVORY) ',1200.000,2000.000,30.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(49,'PLT8','active','ABS  (IVORY) ',1200.000,2000.000,35.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(50,'PLT8','active','ABS  (IVORY) ',1200.000,2000.000,40.000,'',0,'','mm','ABS  (IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(51,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,2.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(52,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,3.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(53,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,4.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(54,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,4.600,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(55,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,5.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,2972897,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:28:34'),(56,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,6.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(57,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,8.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(58,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,10.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(59,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,12.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(60,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,18.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(61,'PLT8','active','ACRYLIC  (CLEAR) ',1219.200,2438.400,25.000,'',0,'','mm','ACRYLIC  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(62,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,2.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(63,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,3.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(64,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,4.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(65,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,5.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(66,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,6.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(67,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,8.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(68,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,10.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(69,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,12.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(70,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,15.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(71,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,20.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(72,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,25.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(73,'PLT8','active','BAKELITE  (ORANGE)',1000.000,2000.000,30.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(74,'PLT8','active','BAKELITE  (ORANGE)',1000.000,1000.000,40.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(75,'PLT8','active','BAKELITE  (ORANGE)',1000.000,1000.000,50.000,'',0,'','mm','BAKELITE  ',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(76,'PLT8','active','CDM  (BLACK) ',1219.200,2438.400,3.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(77,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,4.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(78,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,5.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(79,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,6.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,-250000,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(80,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,8.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(81,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,10.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(82,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,12.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(83,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,15.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(84,'PLT8','active','CDM  (BLACK) ',1150.000,1250.000,20.000,'',0,'','mm','CDM  (BLAC',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(85,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,5.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(86,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,6.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(87,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,7.500,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(88,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,10.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(89,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,12.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(90,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,16.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(91,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,20.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(92,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,25.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(93,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,30.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(94,'PLT8','active','MC501  CDR6  (BLACK) ',600.000,1200.000,40.000,'',0,'','mm','MC501  CDR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(95,'RODS','active','MC501 CDR6',1000.000,1000.000,8.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:35:47'),(96,'RODS','active','MC501 CDR6',1000.000,1000.000,10.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:35:58'),(97,'RODS','active','MC501 CDR6',1000.000,1000.000,12.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:35:53'),(98,'RODS','active','MC501 CDR6',1000.000,1000.000,15.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:02'),(99,'RODS','active','MC501 CDR6',1000.000,1000.000,20.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:07'),(100,'RODS','active','MC501 CDR6',1000.000,1000.000,30.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:11'),(101,'RODS','active','MC501 CDR6',1000.000,1000.000,40.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:16'),(102,'RODS','active','MC501 CDR6',1000.000,1000.000,50.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:21'),(103,'RODS','active','MC501 CDR6',1000.000,1000.000,60.000,'',0,'','mm','MC501 CDR6',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:26'),(104,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,2.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(105,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,3.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(106,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,4.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(107,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,5.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(108,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,6.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,-4,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(109,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,8.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(110,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,10.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(111,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,12.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(112,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,15.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(113,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,20.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(114,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,25.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(115,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,30.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(116,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,35.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(117,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,40.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(118,'PLT8','active','DELRIN  (WHITE) ',1000.000,2000.000,45.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(119,'PLT8','active','DELRIN  (WHITE) ',600.000,1200.000,50.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(120,'PLT8','active','DELRIN  (WHITE) ',600.000,1200.000,60.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(121,'PLT8','active','DELRIN  (WHITE) ',600.000,1200.000,70.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(122,'PLT8','active','DELRIN  (WHITE) ',600.000,1219.200,80.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(123,'PLT8','active','DELRIN  (WHITE) ',600.000,1219.200,100.000,'',0,'','mm','DELRIN  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(124,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,6.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(125,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,8.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(126,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,10.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(127,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,12.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(128,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,15.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(129,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,20.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(130,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,25.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(131,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,30.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(132,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,35.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(133,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,40.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(134,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,45.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(135,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,50.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(136,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,55.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(137,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,60.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(138,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,65.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(139,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,70.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(140,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,75.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(141,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,80.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(142,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,85.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(143,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,90.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(144,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,95.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(145,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,100.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(146,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,110.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(147,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,120.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(148,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,130.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(149,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,140.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(150,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,145.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(151,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,150.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(152,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,160.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(153,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,170.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(154,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,180.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(155,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,190.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(156,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,200.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(157,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,220.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(158,'ROD','active','DELRIN (WHITE) ',1000.000,1000.000,250.000,'',0,'','mm','DELRIN (WH',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(159,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,5.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(160,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,6.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(161,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,8.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(162,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,10.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(163,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,12.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(164,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,15.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(165,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,20.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(166,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,25.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(167,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,30.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(168,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,35.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(169,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,40.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(170,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,45.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(171,'PLT8','active','DELRIN  (BLACK) ',1000.000,2000.000,50.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(172,'PLT8','active','DELRIN  (BLACK) ',600.000,1219.200,60.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(173,'PLT8','active','DELRIN  (BLACK) ',600.000,1219.200,80.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(174,'PLT8','active','DELRIN  (BLACK) ',600.000,1000.000,100.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(175,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,6.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(176,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,8.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(177,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,10.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(178,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,12.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(179,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,15.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(180,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,20.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(181,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,25.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(182,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,30.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(183,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,40.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(184,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,45.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(185,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,50.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(186,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,55.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(187,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,60.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(188,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,65.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(189,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,70.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(190,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,75.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(191,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,80.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(192,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,85.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(193,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,90.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(194,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,95.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(195,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,100.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(196,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,110.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(197,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,120.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(198,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,130.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(199,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,140.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(200,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,145.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(201,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,150.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(202,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,160.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(203,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,170.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(204,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,180.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(205,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,190.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(206,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,200.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(207,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,220.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(208,'ROD','active','DELRIN (BLACK) ',1000.000,1000.000,250.000,'',0,'','mm','DELRIN (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(209,'PLT8','active','DELRIN  (BLUE)',600.000,1200.000,10.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(210,'PLT8','active','DELRIN  (BLUE)',600.000,1200.000,15.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(211,'PLT8','active','DELRIN  (BLUE)',600.000,1200.000,25.000,'',0,'','mm','DELRIN  (B',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(212,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,0.500,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(213,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,0.800,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(214,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,1.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(215,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,0.900,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(216,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,2.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,-250001,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(217,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,3.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(218,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,4.500,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(219,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,5.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(220,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,6.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(221,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,8.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(222,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,10.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(223,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,12.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(224,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,15.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(225,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,20.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(226,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,25.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(227,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,30.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(228,'PLT8','active','FR4 G10 (GREEN) ',1000.000,1200.000,40.000,'',0,'','mm','FR4 G10 (G',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(229,'PLT8','active','G11 (YELLOW) ',1200.000,1200.000,3.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(230,'PLT8','active','G11 (YELLOW) ',1200.000,1200.000,4.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(231,'PLT8','active','G11 (YELLOW) ',1200.000,1200.000,5.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(232,'PLT8','active','G11 (YELLOW) ',1200.000,1200.000,5.500,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(233,'PLT8','active','G11 (YELLOW) ',1200.000,1200.000,8.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(234,'PLT8','active','G11 (YELLOW) ',1200.000,1200.000,10.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(235,'PLT8','active','G11 (YELLOW) ',1000.000,1200.000,12.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(236,'PLT8','active','G11 (YELLOW) ',1000.000,1200.000,15.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(237,'PLT8','active','G11 (YELLOW) ',1000.000,1200.000,20.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(238,'PLT8','active','G11 (YELLOW) ',1000.000,1200.000,25.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(239,'PLT8','active','G11 (YELLOW) ',1000.000,1200.000,30.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(240,'PLT8','active','G11 (YELLOW) ',1000.000,1200.000,40.000,'',0,'','mm','G11 (YELLO',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(241,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,6.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(242,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,8.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(243,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,10.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(244,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,12.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(245,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,15.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(246,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,20.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(247,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,25.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(248,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,30.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(249,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,40.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(250,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,50.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(251,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,60.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(252,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,80.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(253,'PLT8','active','NYLON  (BLUE) ',1000.000,2000.000,100.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(254,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,6.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(255,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,8.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(256,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,10.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(257,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,12.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(258,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,15.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(259,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,20.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(260,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,25.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(261,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,30.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(262,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,35.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(263,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,40.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(264,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,45.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(265,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,50.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(266,'ROD','active','NYLON  (BLUE) ',600.000,0.000,45.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(267,'ROD','active','NYLON  (BLUE) ',500.000,0.000,50.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(268,'ROD','active','NYLON  (BLUE) ',600.000,0.000,50.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(269,'ROD','active','NYLON  (BLUE) ',600.000,0.000,55.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(270,'ROD','active','NYLON  (BLUE) ',600.000,0.000,60.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(271,'ROD','active','NYLON  (BLUE) ',600.000,0.000,65.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(272,'ROD','active','NYLON  (BLUE) ',600.000,0.000,70.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(273,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,70.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(274,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,75.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(275,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,80.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(276,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,90.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(277,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,100.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(278,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,110.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(279,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,115.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(280,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,120.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(281,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,130.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(282,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,140.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(283,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,150.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(284,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,160.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(285,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,170.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(286,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,180.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(287,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,190.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(288,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,200.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(289,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,210.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(290,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,220.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(291,'ROD','active','NYLON  (BLUE) ',1000.000,1000.000,250.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(292,'ROD','active','NYLON IVORY ',1000.000,1000.000,6.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(293,'ROD','active','NYLON IVORY ',1000.000,1000.000,8.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(294,'ROD','active','NYLON IVORY ',1000.000,1000.000,10.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(295,'ROD','active','NYLON IVORY ',1000.000,1000.000,12.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(296,'ROD','active','NYLON IVORY ',1000.000,1000.000,15.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(297,'ROD','active','NYLON IVORY ',1000.000,1000.000,20.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(298,'ROD','active','NYLON IVORY ',1000.000,1000.000,25.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(299,'ROD','active','NYLON IVORY ',1000.000,1000.000,30.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(300,'ROD','active','NYLON IVORY ',1000.000,1000.000,35.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(301,'ROD','active','NYLON IVORY ',1000.000,1000.000,40.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(302,'ROD','active','NYLON IVORY ',1000.000,1000.000,45.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(303,'ROD','active','NYLON IVORY ',1000.000,1000.000,50.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(304,'ROD','active','NYLON IVORY ',1000.000,1000.000,55.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(305,'ROD','active','NYLON IVORY ',1000.000,1000.000,60.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(306,'ROD','active','NYLON IVORY ',1000.000,1000.000,70.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(307,'ROD','active','NYLON IVORY ',1000.000,1000.000,75.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(308,'ROD','active','NYLON IVORY ',1000.000,1000.000,80.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(309,'ROD','active','NYLON IVORY ',1000.000,1000.000,90.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(310,'ROD','active','NYLON IVORY ',1000.000,1000.000,100.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(311,'ROD','active','NYLON IVORY ',1000.000,1000.000,110.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(312,'ROD','active','NYLON IVORY ',1000.000,1000.000,120.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(313,'ROD','active','NYLON IVORY ',1000.000,1000.000,130.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(314,'ROD','active','NYLON IVORY ',1000.000,1000.000,140.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(315,'ROD','active','NYLON IVORY ',1000.000,1000.000,150.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(316,'ROD','active','NYLON IVORY ',1000.000,1000.000,160.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(317,'ROD','active','NYLON IVORY ',1000.000,1000.000,170.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(318,'ROD','active','NYLON IVORY ',1000.000,1000.000,180.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(319,'ROD','active','NYLON IVORY ',1000.000,1000.000,190.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(320,'ROD','active','NYLON IVORY ',1000.000,1000.000,200.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(321,'ROD','active','NYLON IVORY ',1000.000,1000.000,220.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(322,'ROD','active','NYLON IVORY ',1000.000,1000.000,250.000,'',0,'','mm','NYLON IVOR',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(323,'PLT8','active','NYLON (ivory)',1000.000,2000.000,5.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(324,'PLT8','active','NYLON (ivory)',1000.000,2000.000,6.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(325,'PLT8','active','NYLON (ivory)',1000.000,2000.000,8.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(326,'PLT8','active','NYLON (ivory)',1000.000,2000.000,10.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(327,'PLT8','active','NYLON (ivory)',1000.000,2000.000,12.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(328,'PLT8','active','NYLON (ivory)',1000.000,2000.000,15.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(329,'PLT8','active','NYLON (ivory)',1000.000,2000.000,20.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(330,'PLT8','active','NYLON (ivory)',1000.000,2000.000,25.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(331,'PLT8','active','NYLON (ivory)',1000.000,2000.000,30.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(332,'PLT8','active','NYLON (ivory)',1000.000,2000.000,40.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(333,'PLT8','active','NYLON (ivory)',1000.000,2000.000,50.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(334,'PLT8','active','NYLON (ivory)',1000.000,2000.000,60.000,'',0,'','mm','NYLON (ivo',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(335,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,6.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(336,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,8.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(337,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,10.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(338,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,12.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(339,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,15.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(340,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,20.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(341,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,30.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(342,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,40.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(343,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,50.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(344,'PLT8','active','NYLON  (BLACK)',1000.000,2000.000,60.000,'',0,'','mm','NYLON  (BL',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(345,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,6.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(346,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,8.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(347,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,10.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(348,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,12.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(349,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,15.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(350,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,20.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(351,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,25.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(352,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,30.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(353,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,35.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(354,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,40.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(355,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,45.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(356,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,50.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(357,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,55.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(358,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,60.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(359,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,70.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(360,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,75.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(361,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,80.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(362,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,90.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(363,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,100.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(364,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,110.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(365,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,120.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(366,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,130.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(367,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,140.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(368,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,150.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(369,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,160.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(370,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,170.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(371,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,180.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(372,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,190.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(373,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,200.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(374,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,220.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(375,'ROD','active','NYLON (BLACK) ',1000.000,1000.000,250.000,'',0,'','mm','NYLON (BLA',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(376,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,5.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(377,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,6.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(378,'PLT8','active','PE  (WHITE) ',1219.200,2438.400,8.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(379,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,10.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(380,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,12.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(381,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,15.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(382,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,20.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(383,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,25.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(384,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,30.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(385,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,40.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(386,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,50.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(387,'PLT8','active','PE  (WHITE) ',1000.000,2000.000,60.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(388,'ROD','active','PE  (WHITE) ',1000.000,1000.000,6.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(389,'ROD','active','PE  (WHITE) ',1000.000,1000.000,8.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(390,'ROD','active','PE  (WHITE) ',1000.000,1000.000,10.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(391,'ROD','active','PE  (WHITE) ',1000.000,1000.000,12.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(392,'ROD','active','PE  (WHITE) ',1000.000,1000.000,15.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(393,'ROD','active','PE  (WHITE) ',1000.000,1000.000,20.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(394,'ROD','active','PE  (WHITE) ',1000.000,1000.000,25.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(395,'ROD','active','PE  (WHITE) ',1000.000,1000.000,30.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(396,'ROD','active','PE  (WHITE) ',1000.000,1000.000,35.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(397,'ROD','active','PE  (WHITE) ',1000.000,1000.000,40.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(398,'ROD','active','PE  (WHITE) ',1000.000,1000.000,45.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(399,'ROD','active','PE  (WHITE) ',1000.000,1000.000,50.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(400,'ROD','active','PE  (WHITE) ',1000.000,1000.000,55.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(401,'ROD','active','PE  (WHITE) ',1000.000,1000.000,60.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(402,'ROD','active','PE  (WHITE) ',1000.000,1000.000,70.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(403,'ROD','active','PE  (WHITE) ',1000.000,1000.000,75.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(404,'ROD','active','PE  (WHITE) ',1000.000,1000.000,80.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(405,'ROD','active','PE  (WHITE) ',1000.000,1000.000,90.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(406,'ROD','active','PE  (WHITE) ',1000.000,1000.000,100.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(407,'ROD','active','PE  (WHITE) ',1000.000,1000.000,110.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(408,'ROD','active','PE  (WHITE) ',1000.000,1000.000,120.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(409,'ROD','active','PE  (WHITE) ',1000.000,1000.000,130.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(410,'ROD','active','PE  (WHITE) ',1000.000,1000.000,140.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(411,'ROD','active','PE  (WHITE) ',1000.000,1000.000,150.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(412,'ROD','active','PE  (WHITE) ',1000.000,1000.000,160.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(413,'ROD','active','PE  (WHITE) ',1000.000,1000.000,170.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(414,'ROD','active','PE  (WHITE) ',1000.000,1000.000,180.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(415,'ROD','active','PE  (WHITE) ',1000.000,1000.000,190.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(416,'ROD','active','PE  (WHITE) ',1000.000,1000.000,200.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(417,'ROD','active','PE  (WHITE) ',1000.000,1000.000,220.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(418,'ROD','active','PE  (WHITE) ',1000.000,1000.000,250.000,'',0,'','mm','PE  (WHITE',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(419,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,5.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(420,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,6.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(421,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,8.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(422,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,10.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(423,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,12.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(424,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,15.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(425,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,20.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(426,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,25.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(427,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,30.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(428,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,40.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(429,'PLT8','active','PE  (BLACK) ',1000.000,2000.000,50.000,'',0,'','mm','PE  (BLACK',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(430,'ROD','active','PE (BLACK) ',1000.000,1000.000,6.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(431,'ROD','active','PE (BLACK) ',1000.000,1000.000,8.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(432,'ROD','active','PE (BLACK) ',1000.000,1000.000,10.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(433,'ROD','active','PE (BLACK) ',1000.000,1000.000,12.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(434,'ROD','active','PE (BLACK) ',1000.000,1000.000,15.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(435,'ROD','active','PE (BLACK) ',1000.000,1000.000,20.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(436,'ROD','active','PE (BLACK) ',1000.000,1000.000,25.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(437,'ROD','active','PE (BLACK) ',1000.000,1000.000,30.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(438,'ROD','active','PE (BLACK) ',1000.000,1000.000,35.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(439,'ROD','active','PE (BLACK) ',1000.000,1000.000,40.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(440,'ROD','active','PE (BLACK) ',1000.000,1000.000,45.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(441,'ROD','active','PE (BLACK) ',1000.000,1000.000,50.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(442,'ROD','active','PE (BLACK) ',1000.000,1000.000,55.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(443,'ROD','active','PE (BLACK) ',1000.000,1000.000,60.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(444,'ROD','active','PE (BLACK) ',1000.000,1000.000,70.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(445,'ROD','active','PE (BLACK) ',1000.000,1000.000,75.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(446,'ROD','active','PE (BLACK) ',1000.000,1000.000,80.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(447,'ROD','active','PE (BLACK) ',1000.000,1000.000,85.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(448,'ROD','active','PE (BLACK) ',1000.000,1000.000,90.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(449,'ROD','active','PE (BLACK) ',1000.000,1000.000,100.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(450,'ROD','active','PE (BLACK) ',1000.000,1000.000,110.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(451,'ROD','active','PE (BLACK) ',1000.000,1000.000,120.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(452,'ROD','active','PE (BLACK) ',1000.000,1000.000,130.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(453,'ROD','active','PE (BLACK) ',1000.000,1000.000,140.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(454,'ROD','active','PE (BLACK) ',1000.000,1000.000,150.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(455,'ROD','active','PE (BLACK) ',1000.000,1000.000,160.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(456,'ROD','active','PE (BLACK) ',1000.000,1000.000,170.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(457,'ROD','active','PE (BLACK) ',1000.000,1000.000,180.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(458,'ROD','active','PE (BLACK) ',1000.000,1000.000,190.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(459,'ROD','active','PE (BLACK) ',1000.000,1000.000,200.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(460,'ROD','active','PE (BLACK) ',1000.000,1000.000,220.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(461,'ROD','active','PE (BLACK) ',1000.000,1000.000,250.000,'',0,'','mm','PE (BLACK)',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(462,'PLT8','active','PE  (GREEN)',1000.000,2000.000,12.000,'',0,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(463,'PLT8','active','PE  (GREEN)',1000.000,2000.000,15.000,'',0,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(464,'PLT8','active','PE  (GREEN)',1000.000,2000.000,20.000,'',0,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(465,'PLT8','active','PE  (GREEN)',1000.000,2000.000,25.000,'',0,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(466,'PLT8','active','PE  (GREEN)',1000.000,2000.000,30.000,'',0,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(467,'PLT8','active','PE  (GREEN)',1000.000,2000.000,40.000,'',0,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(468,'PLT8','active','PE  (GREEN)',1000.000,2000.000,50.000,'',0,'','mm','PE  (GREEN',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(469,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,3.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(470,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,5.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(471,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,6.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(472,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,8.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(473,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,10.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(474,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,12.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(475,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,16.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(476,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,20.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(477,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,22.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(478,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,25.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(479,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,30.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(480,'PLT8','active','PEEK  (BEIGE) ',600.000,1000.000,40.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(481,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,6.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(482,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,8.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(483,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,10.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(484,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,12.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(485,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,15.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(486,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,20.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(487,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,25.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(488,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,30.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(489,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,35.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(490,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,40.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(491,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,60.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(492,'ROD','active','PEEK  (BEIGE) ',1000.000,1000.000,70.000,'',0,'','mm','PEEK  (BEI',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(493,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,5.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(494,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,6.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(495,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,8.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(496,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,10.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,-2500000,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(497,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,12.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(498,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,15.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(499,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,20.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(500,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,25.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(501,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,30.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(502,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,40.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(503,'PLT8','active','PP  (NAT WHITE) ',1219.200,2438.400,50.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(504,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,6.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(505,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,8.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(506,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,10.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(507,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,12.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(508,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,15.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(509,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,20.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(510,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,25.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(511,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,30.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(512,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,35.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(513,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,40.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(514,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,45.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(515,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,50.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(516,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,55.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(517,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,65.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(518,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,60.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(519,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,70.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(520,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,75.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(521,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,80.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(522,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,85.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(523,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,90.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(524,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,95.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(525,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,100.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(526,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,110.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(527,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,120.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(528,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,130.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(529,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,140.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(530,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,150.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(531,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,160.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(532,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,170.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(533,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,180.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(534,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,190.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(535,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,200.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(536,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,220.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(537,'ROD','active','PP  (NAT WHITE) ',1000.000,1000.000,250.000,'',0,'','mm','PP  (NAT W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(538,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,2.700,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(539,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,4.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(540,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,5.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(541,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,6.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(542,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,8.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(543,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,10.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(544,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,12.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(545,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,15.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(546,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,20.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(547,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,25.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(548,'PLT8','active','PVC  (GRAY) ',1219.200,2438.400,30.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(549,'PLT8','active','PVC  (GRAY) ',1000.000,2000.000,40.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(550,'PLT8','active','PVC  (GRAY) ',1000.000,2000.000,50.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(551,'ROD','active','PVC  (GRAY) ',1000.000,0.000,6.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(552,'ROD','active','PVC  (GRAY) ',1000.000,0.000,8.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(553,'ROD','active','PVC  (GRAY) ',1000.000,0.000,10.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(554,'ROD','active','PVC  (GRAY) ',1000.000,0.000,12.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(555,'ROD','active','PVC  (GRAY) ',1000.000,0.000,15.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(556,'ROD','active','PVC  (GRAY) ',1000.000,0.000,20.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(557,'ROD','active','PVC  (GRAY) ',1000.000,0.000,25.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(558,'ROD','active','PVC  (GRAY) ',1000.000,0.000,30.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(559,'ROD','active','PVC  (GRAY) ',1000.000,1000.000,35.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(560,'ROD','active','PVC  (GRAY) ',1000.000,0.000,40.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(561,'ROD','active','PVC  (GRAY) ',1000.000,0.000,45.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(562,'ROD','active','PVC  (GRAY) ',1000.000,0.000,50.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(563,'ROD','active','PVC  (GRAY) ',1000.000,0.000,55.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(564,'ROD','active','PVC  (GRAY) ',1000.000,0.000,60.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(565,'ROD','active','PVC  (GRAY) ',1000.000,0.000,70.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(566,'ROD','active','PVC  (GRAY) ',1000.000,0.000,75.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(567,'ROD','active','PVC  (GRAY) ',1000.000,0.000,80.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(568,'ROD','active','PVC  (GRAY) ',1000.000,1000.000,85.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(569,'ROD','active','PVC  (GRAY) ',1000.000,0.000,90.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(570,'ROD','active','PVC  (GRAY) ',1000.000,0.000,100.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(571,'ROD','active','PVC  (GRAY) ',1000.000,0.000,110.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(572,'ROD','active','PVC  (GRAY) ',1000.000,0.000,120.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(573,'ROD','active','PVC  (GRAY) ',1000.000,0.000,130.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(574,'ROD','active','PVC  (GRAY) ',1000.000,0.000,140.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(575,'ROD','active','PVC  (GRAY) ',1000.000,0.000,150.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(576,'ROD','active','PVC  (GRAY) ',1000.000,0.000,160.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(577,'ROD','active','PVC  (GRAY) ',1000.000,0.000,170.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(578,'ROD','active','PVC  (GRAY) ',1000.000,0.000,180.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(579,'ROD','active','PVC  (GRAY) ',1000.000,0.000,190.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(580,'ROD','active','PVC  (GRAY) ',1000.000,0.000,200.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(581,'ROD','active','PVC  (GRAY) ',1000.000,0.000,220.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(582,'ROD','active','PVC  (GRAY) ',1000.000,0.000,250.000,'',0,'','mm','PVC  (GRAY',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(583,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,3.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,1000.00,-1000000,0,0,0,0,'2020-02-27 01:23:45','2021-04-22 14:45:13'),(584,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,4.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(585,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,4.500,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(586,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,5.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(587,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,6.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(588,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,8.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(589,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,10.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(590,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,12.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(591,'PLT8','active','RICOCEL  (BLACK)  ',1200.000,1200.000,15.000,'',0,'','mm','RICOCEL  (',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(592,'PLT8','active','ULTEM  (AMBER) ',600.000,1200.000,9.525,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(593,'PLT8','active','ULTEM  (AMBER) ',600.000,1200.000,9.525,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(594,'PLT8','active','ULTEM  (AMBER) ',600.000,1200.000,12.700,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(595,'PLT8','active','ULTEM  (AMBER) ',600.000,1200.000,12.700,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(596,'PLT8','active','ULTEM  (AMBER) ',600.000,1200.000,15.875,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(597,'PLT8','active','ULTEM  (AMBER) ',600.000,1200.000,25.400,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(598,'PLT8','active','ULTEM  (AMBER) ',600.000,1200.000,31.750,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(599,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,6.350,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(600,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,9.525,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(601,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,12.700,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(602,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,15.875,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(603,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,19.050,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(604,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,25.400,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(605,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,31.750,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(606,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,38.100,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(607,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,44.450,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(608,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,63.750,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(609,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,76.200,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(610,'ROD','active','ULTEM  (AMBER) ',1000.000,1000.000,69.850,'',0,'','mm','ULTEM  (AM',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(611,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,6.350,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(612,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,9.525,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(613,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,12.700,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(614,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,15.875,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,-2,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(615,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,19.050,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,500.00,-27,0,0,0,0,'2020-02-27 01:23:45','2021-04-22 20:22:28'),(616,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,22.220,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(617,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,25.400,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(618,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,31.750,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(619,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,38.100,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(620,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,44.450,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(621,'ROD','active','SEMITRON ESD225   (IVORY) ',1000.000,1000.000,50.800,'',0,'','mm','SEMITRON E',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(622,'ROD','active','POLYCARBONATE   (CLEAR) ',1000.000,1000.000,40.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(623,'ROD','active','POLYCARBONATE   (CLEAR) ',1000.000,1000.000,45.000,'',0,'','mm','POLYCARBON',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(624,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,5.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(625,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,6.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(626,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,8.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(627,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,10.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(628,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,12.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(629,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,15.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(630,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,20.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(631,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,25.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(632,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,30.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(633,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,35.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(634,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,40.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(635,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,45.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(636,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,50.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(637,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,55.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(638,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,60.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(639,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,70.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(640,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,75.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(641,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,80.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(642,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,90.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(643,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,100.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(644,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,110.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(645,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,120.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(646,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,130.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(647,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,140.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(648,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,150.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(649,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,160.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(650,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,170.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(651,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,180.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(652,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,190.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(653,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,200.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(654,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,220.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(655,'ROD','active','TEFLON  (WHITE) ',1000.000,1000.000,250.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(656,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,1.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(657,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,2.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(658,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,3.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(659,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,5.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(660,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,6.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(661,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,8.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(662,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,10.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(663,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,12.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(664,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,15.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(665,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,20.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(666,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,25.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(667,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,30.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(668,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,40.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(669,'PLT8','active','TEFLON  (WHITE) ',1200.000,1200.000,50.000,'',0,'','mm','TEFLON  (W',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(670,'PLT8','active','TORLON  4203   (YELLOW)',300.000,300.000,5.000,'',0,'','mm','TORLON  42',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(671,'PLT8','active','TORLON  4203   (YELLOW)',300.000,300.000,7.000,'',0,'','mm','TORLON  42',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(672,'PLT8','active','TORLON  4203   (YELLOW)',300.000,300.000,10.000,'',0,'','mm','TORLON  42',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(673,'PLT8','active','TORLON  4203   (YELLOW)',300.000,300.000,12.000,'',0,'','mm','TORLON  42',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(674,'PLT8','active','TORLON  4203   (YELLOW)',300.000,300.000,15.000,'',0,'','mm','TORLON  42',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(675,'PLT8','active','TORLON  4203   (YELLOW)',300.000,300.000,20.000,'',0,'','mm','TORLON  42',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(676,'PLT8','active','TORLON  4203   (YELLOW)',300.000,300.000,25.000,'',0,'','mm','TORLON  42',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(677,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,6.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(678,'ROD','active','PU (YELLOW) PLATE ',300.000,0.000,8.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(679,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,10.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(680,'ROD','active','PU (YELLOW) PLATE ',300.000,0.000,12.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(681,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,15.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(682,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,20.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(683,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,25.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(684,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,30.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(685,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,35.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(686,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,40.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(687,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,45.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(688,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,50.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(689,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,55.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(690,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,60.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(691,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,65.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(692,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,70.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(693,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,75.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(694,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,80.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(695,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,85.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(696,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,90.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(697,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,100.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(698,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,110.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(699,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,120.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(700,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,130.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(701,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,140.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(702,'ROD','active','PU (YELLOW) PLATE ',500.000,0.000,150.000,'',0,'','mm','PU (YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(703,'PLT8','active','PU YELLOW',1219.200,2438.400,2.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(704,'PLT8','active','PU YELLOW',1219.200,2438.400,3.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(705,'PLT8','active','PU YELLOW',1219.200,2438.400,5.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(706,'PLT8','active','PU YELLOW',1219.200,2438.400,10.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(707,'PLT8','active','PU YELLOW',1219.200,2438.400,15.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(708,'PLT8','active','PU YELLOW',1219.200,2438.400,20.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(709,'PLT8','active','PU YELLOW',1219.200,2438.400,30.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(710,'PLT8','active','PU YELLOW',1000.000,1000.000,40.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45'),(711,'PLT8','active','PU YELLOW',1000.000,1000.000,50.000,'',0,'','mm','PU YELLOW',0.00,0.00,0.00,0.00,0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_order_details`
--

DROP TABLE IF EXISTS `purchase_order_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `length` int(11) NOT NULL,
  `width` int(11) NOT NULL,
  `thickness` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `quantity_area` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_order_details`
--

LOCK TABLES `purchase_order_details` WRITE;
/*!40000 ALTER TABLE `purchase_order_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `purchase_order_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_order_terms`
--

DROP TABLE IF EXISTS `purchase_order_terms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_order_terms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_order_terms`
--

LOCK TABLES `purchase_order_terms` WRITE;
/*!40000 ALTER TABLE `purchase_order_terms` DISABLE KEYS */;
/*!40000 ALTER TABLE `purchase_order_terms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `purchase_orders`
--

DROP TABLE IF EXISTS `purchase_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `po_no` varchar(20) NOT NULL,
  `po_date` date DEFAULT NULL,
  `customer` varchar(80) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `purchase_orders`
--

LOCK TABLES `purchase_orders` WRITE;
/*!40000 ALTER TABLE `purchase_orders` DISABLE KEYS */;
/*!40000 ALTER TABLE `purchase_orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `supplier_ledgers`
--

DROP TABLE IF EXISTS `supplier_ledgers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `supplier_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `supplier_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `supplier_ledgers`
--

LOCK TABLES `supplier_ledgers` WRITE;
/*!40000 ALTER TABLE `supplier_ledgers` DISABLE KEYS */;
/*!40000 ALTER TABLE `supplier_ledgers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `suppliers`
--

DROP TABLE IF EXISTS `suppliers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `suppliers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) DEFAULT NULL,
  `alias` varchar(20) NOT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `tax_type` char(3) NOT NULL COMMENT 'VAT- VATable, ZRO-  NonVat',
  `tin` varchar(20) NOT NULL,
  `address` varchar(150) NOT NULL,
  `business_style` varchar(150) NOT NULL,
  `unit_system` char(3) NOT NULL COMMENT 'ENG - English, MET - Metric',
  `last_bill` int(11) NOT NULL,
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `suppliers`
--

LOCK TABLES `suppliers` WRITE;
/*!40000 ALTER TABLE `suppliers` DISABLE KEYS */;
/*!40000 ALTER TABLE `suppliers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_details`
--

DROP TABLE IF EXISTS `transaction_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transaction_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` char(36) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_details`
--

LOCK TABLES `transaction_details` WRITE;
/*!40000 ALTER TABLE `transaction_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_payments`
--

DROP TABLE IF EXISTS `transaction_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transaction_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` char(36) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_payments`
--

LOCK TABLES `transaction_payments` WRITE;
/*!40000 ALTER TABLE `transaction_payments` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transactions` (
  `id` char(36) NOT NULL DEFAULT '',
  `user` varchar(10) DEFAULT NULL,
  `type` char(10) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `entity_type` char(10) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `ref_no` varchar(25) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

LOCK TABLES `transactions` WRITE;
/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `username` varchar(10) DEFAULT NULL,
  `password` varchar(150) DEFAULT NULL,
  `status` varchar(10) DEFAULT 'active',
  `type` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'Admin','admin','admin','827ccb0eea8a706c4c34a16891f84e7b','active','staff');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2021-05-07 20:37:09
