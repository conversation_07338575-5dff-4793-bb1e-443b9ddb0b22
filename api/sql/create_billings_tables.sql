-- Create billings table
CREATE TABLE billings (
    id VARCHAR(20) NOT NULL,
    customer_id INT NULL,
    due_date DATE NULL,
    due_amount DECIMAL(12,2) NULL,
    details TEXT NULL,
    hash VARCHAR(64) NULL,
    terms VARCHAR(10) NULL,
    created <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    created_by VA<PERSON>HAR(10) NULL,
    status CHAR(10) NULL DEFAULT 'active',
    CONSTRAINT billings_pk PRIMARY KEY (id),
    INDEX idx_billings_customer (customer_id),
    INDEX idx_billings_due_date (due_date),
    INDEX idx_billings_status (status)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

-- Create billing_details table
CREATE TABLE billing_details (
    id INT AUTO_INCREMENT NOT NULL,
    billing_id VARCHAR(20) NULL,
    ref_no VARCHAR(20) NULL,
    invoice_date DATE NULL,
    invoice_amount DECIMAL(12,2) NULL,
    status CHAR(10) NULL DEFAULT 'active',
    created <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    modified <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    CONSTRAINT billing_details_pk PRIMARY KEY (id),
    INDEX idx_billing_details_billing_id (billing_id),
    CONSTRAINT fk_billing_details_billing_id FOREIGN KEY (billing_id)
        REFERENCES billings(id) ON DELETE CASCADE ON UPDATE CASCADE
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

-- Add comment to explain the id format
ALTER TABLE billings COMMENT 'The id field follows the format RIC-SOA-MMYY-NNNN where MM is month, YY is year, and NNNN is a sequential number';
