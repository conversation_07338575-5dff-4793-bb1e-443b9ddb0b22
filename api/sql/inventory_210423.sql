/*
SQLyog Ultimate v9.10 
MySQL - 5.5.5-10.1.30-MariaDB : Database - inventory_210421
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
/*Table structure for table `cash_flows` */

DROP TABLE IF EXISTS `cash_flows`;

CREATE TABLE `cash_flows` (
  `id` char(36) NOT NULL,
  `particulars` varchar(25) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `flag` char(1) NOT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `cash_flows` */

/*Table structure for table `categories` */

DROP TABLE IF EXISTS `categories`;

CREATE TABLE `categories` (
  `id` char(4) NOT NULL,
  `name` varchar(30) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `categories` */

/*Table structure for table `customer_ledgers` */

DROP TABLE IF EXISTS `customer_ledgers`;

CREATE TABLE `customer_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `customer_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `customer_ledgers` */

insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607d7dc5-99f4-44aa-855e-41ec82e7a674',2,'invoice-1','30','4000.00','d','2021-04-19 20:55:33','2021-04-19 20:55:33','2021-04-19 20:55:33');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607d7e0c-8044-4f39-a067-417382e7a674',2,'invoice-2','30','4000.00','d','2021-04-19 20:56:44','2021-04-19 20:56:44','2021-04-19 20:56:44');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607d7e29-99d8-4ac0-9306-423d82e7a674',2,'invoice-3','30','7200.00','d','2021-04-19 20:57:13','2021-04-19 20:57:13','2021-04-19 20:57:13');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607d7f1c-ea5c-4795-b04d-4f5182e7a674',2,'SI-492837','30','4000.00','d','2021-04-19 21:01:16','2021-04-19 21:01:16','2021-04-19 21:01:16');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607d88e2-adc4-4137-8d0b-4bb382e7a674',2,'SI-234234','30','7200.00','d','2021-04-19 21:42:58','2021-04-19 21:42:58','2021-04-19 21:42:58');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607d9959-fa34-4df6-b428-4c0c82e7a674',2,'SI-123234','15','5000.00','d','2021-04-19 22:53:13','2021-04-19 22:53:13','2021-04-19 22:53:13');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607db893-8230-4f94-85fd-4c1a82e7a674',2,'SI-123124','15','10200.00','c','2021-04-20 01:06:27','2021-04-20 01:06:27','2021-04-20 01:06:27');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('607fccb9-a438-4c68-8d7c-183882e7a674',2,'SI-123','15','3000.00','c','2021-04-21 14:56:57','2021-04-21 14:56:57','2021-04-21 14:56:57');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('60811aba-50e4-4fd2-b819-28dc82e7a674',2,'SI-21321','15','500.00','c','2021-04-22 14:42:01','2021-04-22 14:42:02','2021-04-22 14:42:02');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('60811b7a-d5ac-41d0-918d-28dc82e7a674',2,'SI-123123','15','2050.00','c','2021-04-22 14:45:12','2021-04-22 14:45:14','2021-04-22 14:45:14');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('60816a84-91cc-4a4b-8a02-325482e7a674',2,'SI-*********','15','1000.00','c','2021-04-22 20:22:28','2021-04-22 20:22:28','2021-04-22 20:22:28');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('60816b04-c184-419b-9d13-325482e7a674',2,'SI-1231443434','10','500.00','c','2021-04-22 20:24:36','2021-04-22 20:24:36','2021-04-22 20:24:36');
insert  into `customer_ledgers`(`id`,`customer_id`,`ref_no`,`particulars`,`amount`,`flag`,`timestamp`,`created`,`modified`) values ('6081f5bb-123c-4e8f-9767-325482e7a674',2,'SI-123123','15','500.00','c','2021-04-23 06:16:27','2021-04-23 06:16:27','2021-04-23 06:16:27');

/*Table structure for table `customers` */

DROP TABLE IF EXISTS `customers`;

CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `tin` varchar(20) NOT NULL,
  `address` varchar(150) NOT NULL,
  `business_style` varchar(150) NOT NULL,
  `tax_type` char(3) NOT NULL COMMENT 'VAT - VATable, ZRO -  Non-Vat',
  `unit_system` char(3) NOT NULL COMMENT 'ENG - English, MET - Metric',
  `last_bill` int(11) NOT NULL,
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

/*Data for the table `customers` */

insert  into `customers`(`id`,`name`,`status`,`tin`,`address`,`business_style`,`tax_type`,`unit_system`,`last_bill`,`begin_balance`,`current_balance`,`created`,`modified`) values (1,'CASH','open','','','','','',0,'0.00','0.00',NULL,'2021-04-18 18:35:07');
insert  into `customers`(`id`,`name`,`status`,`tin`,`address`,`business_style`,`tax_type`,`unit_system`,`last_bill`,`begin_balance`,`current_balance`,`created`,`modified`) values (2,'Customer A','open','1232342','Sto Tomas Batangas','Construction','ZRO','ENG',0,'-15200.00','-15200.00','2021-02-27 22:39:20','2021-04-19 21:02:38');

/*Table structure for table `deliveries` */

DROP TABLE IF EXISTS `deliveries`;

CREATE TABLE `deliveries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_date` date DEFAULT NULL,
  `supplier` varchar(50) DEFAULT NULL,
  `doc_no` int(11) DEFAULT NULL,
  `source` char(10) DEFAULT 'delivery' COMMENT 'return,order,delivery',
  `total` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `deliveries` */

/*Table structure for table `delivery_details` */

DROP TABLE IF EXISTS `delivery_details`;

CREATE TABLE `delivery_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `delivery_details` */

/*Table structure for table `delivery_payments` */

DROP TABLE IF EXISTS `delivery_payments`;

CREATE TABLE `delivery_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `delivery_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `delivery_payments` */

/*Table structure for table `inventory_adjustments` */

DROP TABLE IF EXISTS `inventory_adjustments`;

CREATE TABLE `inventory_adjustments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `inventory_adjustments` */

/*Table structure for table `inventory_logs` */

DROP TABLE IF EXISTS `inventory_logs`;

CREATE TABLE `inventory_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `old_quantity` int(11) DEFAULT NULL,
  `act_quantity` int(11) DEFAULT NULL,
  `new_quantity` int(11) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=84 DEFAULT CHARSET=latin1;

/*Data for the table `inventory_logs` */

insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (1,1,2972897,100,2972797,'POS','2021-03-05 16:36:57');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (2,3,2972897,0,2972897,'POS','2021-04-18 00:31:50');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (3,1,2972797,0,2972797,'POS','2021-04-18 00:51:27');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (4,1,2972797,720,2972077,'JOB','2021-04-18 18:13:13');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (5,10,0,18375,-18375,'JOB','2021-04-18 18:13:13');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (6,1,2972077,720,2971357,'JOB','2021-04-18 18:18:11');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (7,10,-18375,18375,-36750,'JOB','2021-04-18 18:18:11');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (8,1,2971357,720,2970637,'JOB','2021-04-18 18:18:30');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (9,10,-36750,18375,-55125,'JOB','2021-04-18 18:18:30');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (10,1,2970637,500,2970137,'JOB','2021-04-18 18:20:48');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (11,1,2970137,1350,2968787,'JOB','2021-04-18 18:20:48');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (12,1,2968787,2500,2966287,'JOB','2021-04-18 18:20:48');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (13,1,2966287,500,2965787,'JOB','2021-04-18 18:23:05');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (14,1,2965787,1350,2964437,'JOB','2021-04-18 18:23:05');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (15,1,2964437,2500,2961937,'JOB','2021-04-18 18:23:05');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (16,1,2961937,500,2961437,'JOB','2021-04-18 18:24:06');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (17,1,2961437,1350,2960087,'JOB','2021-04-18 18:24:06');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (18,1,2960087,2500,2957587,'JOB','2021-04-18 18:24:06');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (19,1,2957587,300,2957287,'JOB','2021-04-18 18:30:31');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (20,1,2957287,300,2956987,'JOB','2021-04-18 18:37:57');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (21,1,2956987,500,2956487,'JOB','2021-04-19 05:32:24');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (22,1,2956487,720,2955767,'JOB','2021-04-19 05:32:24');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (23,1,2955767,2250,2953517,'JOB','2021-04-19 05:32:24');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (24,1,2953517,300,2953217,'JOB','2021-04-19 05:35:04');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (25,1,2953217,2890,2950327,'JOB','2021-04-19 05:36:17');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (26,1,2950327,1210,2949117,'JOB','2021-04-19 05:36:17');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (27,1,2949117,2560,2946557,'JOB','2021-04-19 06:01:28');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (28,1,2946557,605,2945952,'JOB','2021-04-19 06:01:28');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (29,1,2945952,605,2945347,'JOB','2021-04-19 06:06:26');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (30,1,2945347,605,2944742,'JOB','2021-04-19 06:06:26');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (31,1,2944742,2250,2942492,'JOB','2021-04-19 06:20:21');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (32,1,2942492,500,2941992,'JOB','2021-04-19 06:20:21');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (33,1,2941992,2250,2939742,'JOB','2021-04-19 07:55:23');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (34,4,2972897,1000,2971897,'JOB','2021-04-19 09:35:30');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (35,1,2939742,1125,2938617,'JOB','2021-04-19 20:13:27');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (36,1,2938617,500,2938117,'JOB','2021-04-19 20:13:27');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (37,1,2938117,500,2937617,'JOB','2021-04-19 20:48:23');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (38,1,2937617,675,2936942,'JOB','2021-04-19 20:48:23');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (39,1,2936942,576,2936366,'JOB','2021-04-19 20:48:32');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (40,1,2936366,64000,2872366,'JOB','2021-04-19 20:48:32');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (41,2,2972897,50,2972847,'JOB','2021-04-19 20:48:32');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (42,1,2872366,1000,2871366,'JOB','2021-04-19 22:52:55');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (43,1,2871366,2000,2869366,'JOB','2021-04-20 01:06:12');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (44,2,2972847,200,2972647,'JOB','2021-04-20 01:06:12');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (45,1,2869366,5,2869361,'JOB','2021-04-21 14:55:25');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (46,2,2972647,5,2972642,'JOB','2021-04-21 14:55:25');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (47,5,2972897,1,2972896,'JOB','2021-04-21 14:55:26');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (48,615,0,25,-25,'JOB','2021-04-22 14:41:39');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (49,5,2972896,168921,2803975,'JOB','2021-04-22 14:41:40');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (50,216,0,250000,-250000,'JOB','2021-04-22 14:41:40');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (51,496,0,2500000,-2500000,'JOB','2021-04-22 14:41:40');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (52,20,0,10000,-10000,'JOB','2021-04-22 14:41:40');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (53,583,0,1000000,-1000000,'JOB','2021-04-22 14:45:00');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (54,3,2972897,1,2972896,'JOB','2021-04-22 14:45:00');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (55,615,-25,1,-26,'JOB','2021-04-22 14:45:00');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (56,628,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (57,707,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (58,216,-250000,1,-250001,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (59,108,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (60,596,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (61,244,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (62,497,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (63,107,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (64,8,0,1,-1,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (65,1,2869361,1,2869360,'JOB','2021-04-22 14:45:01');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (66,65,0,1,-1,'JOB','2021-04-22 14:45:02');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (67,1,2869360,1,2869359,'JOB','2021-04-22 20:20:25');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (68,3,2972896,1,2972895,'JOB','2021-04-22 20:20:25');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (69,5,2803975,1,2803974,'JOB','2021-04-22 20:20:25');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (70,1,2869359,1,2869358,'JOB','2021-04-22 20:22:12');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (71,611,0,1,-1,'JOB','2021-04-22 20:22:12');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (72,615,-26,1,-27,'JOB','2021-04-22 20:22:12');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (73,108,-1,1,-2,'JOB','2021-04-22 20:22:13');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (74,586,0,1,-1,'JOB','2021-04-22 20:22:13');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (75,614,0,1,-1,'JOB','2021-04-22 20:24:15');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (76,1,2869358,1,2869357,'JOB','2021-04-22 20:24:15');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (77,5,2803974,1,2803973,'JOB','2021-04-22 20:24:15');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (78,5,2803973,1,2803972,'JOB','2021-04-22 20:24:15');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (79,108,-2,1,-3,'JOB','2021-04-22 20:24:15');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (80,614,-1,1,-2,'JOB','2021-04-22 20:24:16');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (81,5,2803972,1,2803971,'JOB','2021-04-23 06:16:14');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (82,79,0,250000,-250000,'JOB','2021-04-23 06:16:14');
insert  into `inventory_logs`(`id`,`product_id`,`old_quantity`,`act_quantity`,`new_quantity`,`source`,`created`) values (83,108,-3,1,-4,'JOB','2021-04-23 06:16:14');

/*Table structure for table `invoice_details` */

DROP TABLE IF EXISTS `invoice_details`;

CREATE TABLE `invoice_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `description` varchar(50) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=latin1;

/*Data for the table `invoice_details` */

insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (1,1,1,'AS ACRYLIC  (CLEAR)  10mm× 10mm× 3.5mm',5,'500.00','2500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (2,1,1,'AS ACRYLIC  (CLEAR)  15mm× 15mm× 3.5mm',3,'500.00','1500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (3,2,1,'AS ACRYLIC  (CLEAR)  10mm× 10mm× 3.5mm',5,'500.00','2500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (4,2,1,'AS ACRYLIC  (CLEAR)  15mm× 15mm× 3.5mm',3,'500.00','1500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (5,3,1,'AS ACRYLIC  (CLEAR)  12mm× 12mm× 3.5mm',4,'500.00','2000.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (6,3,1,'AS ACRYLIC  (CLEAR)  80mm× 80mm× 3.5mm',10,'500.00','5000.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (7,3,2,'AS ACRYLIC  (CLEAR)  5mm× 5mm× 5mm',2,'100.00','200.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (8,4,1,'AS ACRYLIC  (CLEAR)  10mm× 10mm× 3.5mm',5,'500.00','2500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (9,4,1,'AS ACRYLIC  (CLEAR)  15mm× 15mm× 3.5mm',3,'500.00','1500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (10,5,1,'AS ACRYLIC  (CLEAR)  12mm× 12mm× 3.5mm',4,'500.00','2000.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (11,5,1,'AS ACRYLIC  (CLEAR)  80mm× 80mm× 3.5mm',10,'500.00','5000.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (12,5,2,'AS ACRYLIC  (CLEAR)  5mm× 5mm× 5mm',2,'100.00','200.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (13,6,1,'AS ACRYLIC  (CLEAR)  10mm× 10mm× 3.5mm',10,'500.00','5000.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (14,7,1,'AS ACRYLIC  (CLEAR)  10mm× 10mm× 3.5mm',20,'500.00','10000.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (15,7,2,'AS ACRYLIC  (CLEAR)  10mm× 10mm× 5mm',2,'100.00','200.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (16,8,1,'AS ACRYLIC  (CLEAR)  1mm× 1mm× 3.5mm',5,'500.00','2500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (17,8,2,'AS ACRYLIC  (CLEAR)  1mm× 1mm× 5mm',5,'100.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (18,8,5,'AS POLYCARBONATE  (CLEAR)  1mm× 1mm× 2mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (19,9,615,'SEMITRON ESD225   (IVORY)  5mm× 19.05mm',1,'500.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (20,9,5,'AS POLYCARBONATE  (CLEAR)  411mm× 411mm× 2mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (21,9,216,'FR4 G10 (GREEN)  500mm× 500mm× 2mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (22,9,496,'PP  (NAT WHITE)  500mm× 500mm× 10mm',10,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (23,9,20,'POLYCARBONATE   (CLEAR)  100mm× 100mm× 10mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (24,10,583,'RICOCEL  (BLACK)   1000mm× 1000mm× 3mm',1,'1000.00','1000.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (25,10,3,'AS ACRYLIC  (CLEAR)  1mm× 1mm× 6mm',1,'50.00','50.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (26,10,615,'SEMITRON ESD225   (IVORY)  1mm× 19.05mm',1,'500.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (27,10,628,'TEFLON  (WHITE)  1mm× 12mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (28,10,707,'PU YELLOW 1mm× 1mm× 15mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (29,10,216,'FR4 G10 (GREEN)  1mm× 1mm× 2mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (30,10,108,'DELRIN  (WHITE)  1mm× 1mm× 6mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (31,10,596,'ULTEM  (AMBER)  1mm× 1mm× 15.875mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (32,10,244,'NYLON  (BLUE)  1mm× 1mm× 12mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (33,10,497,'PP  (NAT WHITE)  1mm× 1mm× 12mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (34,10,107,'DELRIN  (WHITE)  1mm× 1mm× 5mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (35,10,8,'AS POLYCARBONATE  (CLEAR)  1mm× 1mm× 5mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (36,10,1,'AS ACRYLIC  (CLEAR)  1mm× 1mm× 3.5mm',1,'500.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (37,10,65,'BAKELITE  (ORANGE) 1mm× 1mm× 5mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (38,11,1,'AS ACRYLIC  (CLEAR)  1mm× 1mm× 3.5mm',1,'500.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (39,11,611,'SEMITRON ESD225   (IVORY)  1mm× 6.35mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (40,11,615,'SEMITRON ESD225   (IVORY)  1mm× 19.05mm',1,'500.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (41,11,108,'DELRIN  (WHITE)  1mm× 1mm× 6mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (42,11,586,'RICOCEL  (BLACK)   1mm× 1mm× 5mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (43,12,614,'SEMITRON ESD225   (IVORY)  1mm× 15.875mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (44,12,1,'AS ACRYLIC  (CLEAR)  1mm× 1mm× 3.5mm',1,'500.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (45,12,5,'AS POLYCARBONATE  (CLEAR)  1mm× 1mm× 2mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (46,12,5,'AS POLYCARBONATE  (CLEAR)  1mm× 1mm× 2mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (47,12,108,'DELRIN  (WHITE)  1mm× 1mm× 6mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (48,12,614,'SEMITRON ESD225   (IVORY)  1mm× 15.875mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (49,13,5,'AS POLYCARBONATE  (CLEAR)  1mm× 1mm× 2mm',1,'500.00','500.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (50,13,79,'CDM  (BLACK)  500mm× 500mm× 6mm',1,'0.00','0.00');
insert  into `invoice_details`(`id`,`invoice_id`,`product_id`,`description`,`quantity`,`price`,`amount`) values (51,13,108,'DELRIN  (WHITE)  1mm× 1mm× 6mm',1,'0.00','0.00');

/*Table structure for table `invoice_payments` */

DROP TABLE IF EXISTS `invoice_payments`;

CREATE TABLE `invoice_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=latin1;

/*Data for the table `invoice_payments` */

insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (1,1,'CHRG','30','4000.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (2,2,'CHRG','30','4000.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (3,3,'CHRG','30','7200.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (4,4,'CHRG','30','4000.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (5,5,'CHRG','30','7200.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (6,6,'CHRG','15','5000.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (7,7,'CHRG','15','10200.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (8,8,'CHRG','15','3000.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (9,9,'CHRG','15','500.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (10,10,'CHRG','15','2050.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (11,11,'CHRG','15','1000.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (12,12,'CHRG','10','500.00');
insert  into `invoice_payments`(`id`,`invoice_id`,`payment_type`,`detail`,`amount`) values (13,13,'CHRG','15','500.00');

/*Table structure for table `invoices` */

DROP TABLE IF EXISTS `invoices`;

CREATE TABLE `invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `invoice_date` date DEFAULT NULL,
  `terms` int(11) NOT NULL,
  `term_date` date NOT NULL,
  `po_no` varchar(20) NOT NULL COMMENT 'Purchase Order No',
  `po_date` date NOT NULL COMMENT 'PO Date',
  `si_no` varchar(20) NOT NULL COMMENT 'Sales Invoice No',
  `si_date` date NOT NULL COMMENT 'SI Date',
  `dr_no` varchar(20) NOT NULL COMMENT 'Delivery Receipt No',
  `dr_date` date NOT NULL COMMENT 'DR Date',
  `cr_no` varchar(20) NOT NULL COMMENT 'Collection Receipt No',
  `cr_date` date NOT NULL COMMENT 'CR Date',
  `customer` varchar(80) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `created` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=latin1;

/*Data for the table `invoices` */

insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (1,0,'2021-04-19',0,'0000-00-00','*12323','2021-04-19','492837','2021-04-19','3465987','2021-04-19','','0000-00-00','Customer A','4000.00','0.00','0.00','0.00','0.00','2021-04-19');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (2,0,'2021-04-19',0,'0000-00-00','*12323','2021-04-19','492837','2021-04-19','3465987','2021-04-19','','0000-00-00','Customer A','4000.00','0.00','0.00','0.00','0.00','2021-04-19');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (3,0,'2021-04-19',0,'0000-00-00','','2021-04-19','2423','2021-04-19','532523','2021-04-19','','0000-00-00','Customer A','7200.00','0.00','0.00','0.00','0.00','2021-04-19');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (4,1,'2021-04-19',0,'0000-00-00','*12323','2021-04-19','492837','2021-04-19','3465987','2021-04-19','','0000-00-00','Customer A','4000.00','0.00','0.00','0.00','0.00','2021-04-19');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (5,2,'2021-04-19',0,'0000-00-00','','2021-04-19','234234','2021-04-19','3245234','2021-04-19','','0000-00-00','Customer A','7200.00','0.00','0.00','0.00','0.00','2021-04-19');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (6,3,'2021-04-19',15,'0000-00-00','*9834','2021-04-19','123234','2021-04-19','23534','2021-04-19','','0000-00-00','Customer A','5000.00','0.00','0.00','0.00','0.00','2021-04-19');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (7,4,'2021-04-20',15,'0000-00-00','','2021-04-19','123124','2021-04-20','523453','2021-04-20','','0000-00-00','Customer A','10200.00','0.00','0.00','0.00','0.00','2021-04-20');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (8,6,'2021-04-21',15,'0000-00-00','123','2021-04-21','123','2021-04-21','123','2021-04-21','','0000-00-00','Customer A','3000.00','0.00','0.00','0.00','0.00','2021-04-21');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (9,7,'2021-04-22',15,'0000-00-00','234','2021-04-22','21321','2021-04-22','1232','2021-04-22','','0000-00-00','Customer A','500.00','0.00','0.00','0.00','0.00','2021-04-22');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (10,8,'2021-04-22',15,'0000-00-00','445','2021-04-22','123123','2021-04-22','23123','2021-04-22','','0000-00-00','Customer A','2050.00','0.00','0.00','0.00','0.00','2021-04-22');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (11,10,'2021-04-22',15,'0000-00-00','','2021-04-22','*********','2021-04-22','123123','2021-04-22','','0000-00-00','Customer A','1000.00','0.00','0.00','0.00','0.00','2021-04-22');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (12,11,'2021-04-22',10,'0000-00-00','','2021-04-22','1231443434','2021-04-22','441555','2021-04-22','','0000-00-00','Customer A','500.00','0.00','0.00','0.00','0.00','2021-04-22');
insert  into `invoices`(`id`,`purchase_order_id`,`invoice_date`,`terms`,`term_date`,`po_no`,`po_date`,`si_no`,`si_date`,`dr_no`,`dr_date`,`cr_no`,`cr_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (13,12,'2021-04-23',15,'0000-00-00','123123','2021-04-23','123123','2021-04-23','4145','2021-04-23','','0000-00-00','Customer A','500.00','0.00','0.00','0.00','0.00','2021-04-23');

/*Table structure for table `job_order_details` */

DROP TABLE IF EXISTS `job_order_details`;

CREATE TABLE `job_order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `length` int(11) NOT NULL,
  `length_actual` int(11) NOT NULL,
  `width` int(11) NOT NULL,
  `width_actual` int(11) NOT NULL,
  `thickness` int(11) NOT NULL,
  `thickness_actual` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `quantity_actual` int(11) NOT NULL,
  `quantity_area` int(11) NOT NULL,
  `quantity_area_actual` int(11) NOT NULL,
  `po_price` decimal(8,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=latin1;

/*Data for the table `job_order_details` */

insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (1,1,1,10,10,10,10,0,0,5,5,500,500,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (2,1,1,15,15,15,15,0,0,3,3,675,675,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (3,2,1,12,12,12,12,0,0,4,4,576,576,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (4,2,1,80,80,80,80,0,0,10,10,64000,64000,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (5,2,2,5,5,5,5,0,0,2,2,50,50,'100.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (6,3,1,10,10,10,10,0,0,10,10,1000,1000,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (7,4,1,10,10,10,10,0,0,20,20,2000,2000,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (8,4,2,10,10,10,10,0,0,2,2,200,200,'100.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (9,5,1,1,1,1,1,0,0,5,5,5,5,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (10,5,2,1,1,1,1,0,0,5,5,5,5,'100.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (11,5,5,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (12,6,615,5,5,5,5,0,0,1,1,25,25,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (13,6,5,411,411,411,411,0,0,1,1,168921,168921,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (14,6,216,500,500,500,500,0,0,1,1,250000,250000,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (15,6,496,500,500,500,500,0,0,10,10,2500000,2500000,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (16,6,20,100,100,100,100,0,0,1,1,10000,10000,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (17,7,583,1000,1000,1000,1000,0,0,1,1,1000000,1000000,'1000.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (18,7,3,1,1,1,1,0,0,1,1,1,1,'50.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (19,7,615,1,1,1,1,0,0,1,1,1,1,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (20,7,628,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (21,7,707,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (22,7,216,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (23,7,108,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (24,7,596,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (25,7,244,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (26,7,497,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (27,7,107,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (28,7,8,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (29,7,1,1,1,1,1,0,0,1,1,1,1,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (30,7,65,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (31,8,1,1,1,1,1,0,0,1,1,1,1,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (32,8,3,1,1,1,1,0,0,1,1,1,1,'50.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (33,8,5,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (34,9,1,1,1,1,1,0,0,1,1,1,1,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (35,9,611,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (36,9,615,1,1,1,1,0,0,1,1,1,1,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (37,9,108,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (38,9,586,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (39,10,614,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (40,10,1,1,1,1,1,0,0,1,1,1,1,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (41,10,5,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (42,10,5,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (43,10,108,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (44,10,614,1,1,1,1,0,0,1,1,1,1,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (45,11,5,1,1,1,1,0,0,1,1,1,1,'500.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (46,11,79,500,500,500,500,0,0,1,1,250000,250000,'0.00');
insert  into `job_order_details`(`id`,`job_order_id`,`product_id`,`length`,`length_actual`,`width`,`width_actual`,`thickness`,`thickness_actual`,`quantity`,`quantity_actual`,`quantity_area`,`quantity_area_actual`,`po_price`) values (47,11,108,1,1,1,1,0,0,1,1,1,1,'0.00');

/*Table structure for table `job_orders` */

DROP TABLE IF EXISTS `job_orders`;

CREATE TABLE `job_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `po_no` varchar(20) NOT NULL,
  `po_date` date NOT NULL,
  `jo_date` date DEFAULT NULL,
  `user` varchar(80) DEFAULT NULL,
  `remarks` varchar(150) DEFAULT NULL,
  `created` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=latin1;

/*Data for the table `job_orders` */

insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (1,1,'*12323','2021-04-19','2021-04-19','admin',NULL,'2021-04-19');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (2,2,'','2021-04-19','2021-04-19','admin',NULL,'2021-04-19');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (3,3,'*9834','2021-04-19','2021-04-19','admin',NULL,'2021-04-19');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (4,4,'','2021-04-19','2021-04-20','admin',NULL,'2021-04-20');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (5,6,'123','2021-04-21','2021-04-21','admin',NULL,'2021-04-21');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (6,7,'234','2021-04-22','2021-04-22','admin',NULL,'2021-04-22');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (7,8,'445','2021-04-22','2021-04-22','admin',NULL,'2021-04-22');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (8,9,'213154','2021-04-22','2021-04-22','admin',NULL,'2021-04-22');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (9,10,'','2021-04-22','2021-04-22','admin',NULL,'2021-04-22');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (10,11,'','2021-04-22','2021-04-22','admin',NULL,'2021-04-22');
insert  into `job_orders`(`id`,`purchase_order_id`,`po_no`,`po_date`,`jo_date`,`user`,`remarks`,`created`) values (11,12,'123123','2021-04-23','2021-04-23',NULL,NULL,'2021-04-23');

/*Table structure for table `module_users` */

DROP TABLE IF EXISTS `module_users`;

CREATE TABLE `module_users` (
  `id` char(36) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `module_id` char(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `module_users` */

insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-001c-466c-8f4b-04d8c0a80107',6,'users');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-0d5c-463b-a870-04d8c0a80107',6,'ordrs');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-1a9c-4492-a90c-04d8c0a80107',6,'sales');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-6bd8-4887-8088-04d8c0a80107',6,'invtry');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-797c-4eef-bd3b-04d8c0a80107',6,'rtrnordr');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-7ad4-41e9-91f5-04d8c0a80107',6,'accnt');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-93fc-4b98-a9a6-04d8c0a80107',6,'asssmnt');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-a13c-4f61-8151-04d8c0a80107',6,'trnx');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-d7f8-4c09-92e2-04d8c0a80107',6,'ldgr');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-e538-4c02-8bd4-04d8c0a80107',6,'stckrm');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('574fe2cb-f2dc-440f-8790-04d8c0a80107',6,'delvry');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-162c-4838-9067-0850c0a8fe07',5,'ordrs');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-162c-4b09-bf80-0850c0a8fe07',5,'rtrnordr');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-23d0-44e2-b89f-0850c0a8fe07',5,'users');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-23d0-45b8-b2e0-0850c0a8fe07',5,'accnt');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-258c-49da-b1d3-0850c0a8fe07',5,'invtry');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-3110-4557-bf06-0850c0a8fe07',5,'ldgr');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-8f8c-4151-95da-0850c0a8fe07',5,'stckrm');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-9d30-4965-b407-0850c0a8fe07',5,'asssmnt');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-9d30-4994-b685-0850c0a8fe07',5,'sales');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-aa70-42ed-948a-0850c0a8fe07',5,'delvry');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('5bff6078-aa70-4f21-bbde-0850c0a8fe07',5,'trnx');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-1ac0-477a-a86c-745d68f89d23',2,'rtrnordr');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-40c0-436a-881f-745d68f89d23',2,'trnx');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-6ee0-4c9e-9677-745d68f89d23',2,'ordrs');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-71b8-45b6-a2b6-745d68f89d23',2,'invtry');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-9918-47de-9259-745d68f89d23',2,'asssmnt');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-ae4c-46ce-ac08-745d68f89d23',2,'users');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-b188-4322-b450-745d68f89d23',2,'sales');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('602a5ec9-e4bc-4d0e-99d9-745d68f89d23',2,'delvry');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-1834-4ca5-922d-444c82e7a674',1,'accnt');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-3cac-44ad-a9e5-456482e7a674',1,'jo');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-51e8-4910-bd21-4eda82e7a674',1,'stckrm');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-7cd8-4fbb-b33c-42dd82e7a674',1,'montr');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-8148-43ce-8db3-4be982e7a674',1,'ldgr');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-9794-40f1-8412-4b1a82e7a674',1,'invoice');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-ad90-416a-af6e-4ff282e7a674',1,'po');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-bc0c-4d80-90e3-49ad82e7a674',1,'users');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-cee8-4e86-8979-48cf82e7a674',1,'trnx');
insert  into `module_users`(`id`,`user_id`,`module_id`) values ('607d8c2c-e9f8-40a3-9a85-474982e7a674',1,'invtry');

/*Table structure for table `modules` */

DROP TABLE IF EXISTS `modules`;

CREATE TABLE `modules` (
  `id` char(10) NOT NULL,
  `title` varchar(20) DEFAULT NULL,
  `link` varchar(50) DEFAULT NULL,
  `description` varchar(150) DEFAULT NULL,
  `icon` varchar(30) DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `type` char(2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `modules` */

insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('accnt','Accounts','accounts/homepage','Manage customer or supplier information and outstanding balances.','user',3,'BR');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('asssmnt','Assessment','inventory/assessment','Process trade in and return transaction from customers using the assessment module.','tags',2,'FD');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('delvry','Deliveries','deliveries/homepage','Record incoming items using the delivery module.','log-in',3,'FD');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('invoice','Sales Invoice','sales/invoice','Complete PO with Sales Invoice','shopping-cart',1,'FD');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('invtry','Inventory','inventory/homepage','Keep track of your stocks and pricing using the','inbox',1,'BR');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('jo','Job Order','orders/job','Fulfill orders','object-align-bottom',6,'BR');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('ldgr','Ledgers','accounts/ledger','Review customer or supplier charges and payments.','th-list',4,'BR');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('montr','PO Monitoring','transactions/monitoring','Monitor PO transactions','transfer',2,'BR');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('ordrs','Orders','orders/homepage','Request new inventory using the purchase order module.','log-out',4,'FD');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('po','Purchase Order','orders/purchase','Create PO from customers','shopping-cart',6,'FD');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('rtrnordr','Return Orders','inventory/returnorder','Send back items to supplier using the return.','repeat',5,'FD');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('sales','Sales','sales/homepage','Sell products using the point of sales module.','shopping-cart',1,'FD');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('stckrm','Stock Room','inventory/stockroom','Update your physical count using the stock room module.','object-align-bottom',6,'BR');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('trnx','Transactions','transactions/homepage','Review all inbound and outbound transactions.','transfer',2,'BR');
insert  into `modules`(`id`,`title`,`link`,`description`,`icon`,`order`,`type`) values ('users','Users','accounts/users','Manage staff\'s access using the user module.','pushpin',5,'BR');

/*Table structure for table `order_details` */

DROP TABLE IF EXISTS `order_details`;

CREATE TABLE `order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

/*Data for the table `order_details` */

insert  into `order_details`(`id`,`order_id`,`product_id`,`quantity`,`price`,`amount`) values (1,1,1,1,'100.00','100.00');
insert  into `order_details`(`id`,`order_id`,`product_id`,`quantity`,`price`,`amount`) values (2,2,1,10,'100.00','1000.00');

/*Table structure for table `orders` */

DROP TABLE IF EXISTS `orders`;

CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier` varchar(50) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

/*Data for the table `orders` */

insert  into `orders`(`id`,`supplier`,`order_date`,`delivery_date`,`total`,`created`) values (1,'Supplier A','2021-03-09',NULL,'100.00','2021-03-09 12:10:29');
insert  into `orders`(`id`,`supplier`,`order_date`,`delivery_date`,`total`,`created`) values (2,'Supplier A','2021-04-18',NULL,'1000.00','2021-04-18 01:01:53');

/*Table structure for table `price_logs` */

DROP TABLE IF EXISTS `price_logs`;

CREATE TABLE `price_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `old_price` decimal(10,2) DEFAULT NULL,
  `new_price` decimal(10,2) DEFAULT NULL,
  `source` char(3) DEFAULT NULL COMMENT 'INV-Invoice, DEL-Delivery',
  `ref_no` int(11) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `price_logs` */

/*Table structure for table `products` */

DROP TABLE IF EXISTS `products`;

CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` char(4) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'active',
  `particular` varchar(50) DEFAULT ' ',
  `length` decimal(7,3) DEFAULT NULL,
  `width` decimal(7,3) DEFAULT NULL,
  `thickness` decimal(7,3) NOT NULL,
  `type` char(3) NOT NULL COMMENT 'IMP - Import, LOC -  Local',
  `allowance` int(11) NOT NULL,
  `part_no` varchar(30) DEFAULT ' ',
  `unit` varchar(10) DEFAULT ' ',
  `description` varchar(255) DEFAULT ' ',
  `capital` decimal(8,2) DEFAULT NULL,
  `markup` decimal(8,2) DEFAULT NULL,
  `srp` decimal(8,2) DEFAULT NULL,
  `tmp_srp` decimal(10,2) NOT NULL,
  `soh_quantity` int(11) DEFAULT NULL,
  `tmp_quantity` int(11) DEFAULT NULL,
  `min_quantity` int(11) DEFAULT NULL,
  `max_quantity` int(11) DEFAULT NULL,
  `discountable` tinyint(1) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=712 DEFAULT CHARSET=latin1;

/*Data for the table `products` */

insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (1,'PLT8','active','AS ACRYLIC  (CLEAR) ','1219.200','2438.400','3.500','LOC',3,'','mm',' ','0.00','0.00','0.00','500.00',2869357,0,0,0,0,'2020-02-27 01:23:45','2021-04-22 20:24:36');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (2,'PLT8','active','AS ACRYLIC  (CLEAR) ','1219.200','2438.400','5.000','IMP',0,'','mm',' ','0.00','0.00','0.00','100.00',2972642,0,0,0,0,'2020-02-27 01:23:45','2021-04-21 14:56:57');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (3,'PLT8','active','AS ACRYLIC  (CLEAR) ','1219.200','2438.400','6.000','',0,'','mm',' ','0.00','0.00','0.00','50.00',2972895,0,0,0,0,'2020-02-27 01:23:45','2021-04-22 14:45:13');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (4,'PLT8','active','AS ACRYLIC  (CLEAR) ','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','50.00',2971897,0,0,0,0,'2020-02-27 01:23:45','2021-04-19 10:43:27');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (5,'PLT8','active','AS POLYCARBONATE  (CLEAR) ','1219.200','2438.400','2.000','',0,'','mm',' ','0.00','0.00','0.00','500.00',2803971,0,0,0,0,'2020-02-27 01:23:45','2021-04-23 06:16:27');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (6,'PLT8','active','AS POLYCARBONATE  (CLEAR) ','1219.200','2438.400','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (7,'PLT8','active','AS POLYCARBONATE  (CLEAR) ','1219.200','2438.400','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (8,'PLT8','active','AS POLYCARBONATE  (CLEAR) ','1219.200','2438.400','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (9,'PLT8','active','AS POLYCARBONATE  (CLEAR) ','1219.200','2438.400','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (10,'PLT8','active','AS POLYCARBONATE  (CLEAR) ','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-55125,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (11,'PLT8','active','PET  (CLEAR)','1200.000','1200.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (12,'PLT8','active','PET  (CLEAR)','1200.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (13,'PLT8','active','PET  (CLEAR)','1200.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (14,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (15,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (16,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (17,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (18,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (19,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (20,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-10000,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (21,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (22,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (23,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (24,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (25,'PLT8','active','POLYCARBONATE   (CLEAR) ','1219.200','2438.400','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (26,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (27,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (28,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (29,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (30,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (31,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (32,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (33,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (34,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (35,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (36,'PLT8','active','PVC  (CLEAR)','1219.200','2438.400','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (37,'PLT8','active','PVC  (CLEAR)','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (38,'PLT8','active','PVC  (CLEAR)','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (39,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (40,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (41,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (42,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (43,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (44,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (45,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (46,'PLT8','active','ABS  (IVORY) ','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (47,'PLT8','active','ABS  (IVORY) ','1200.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (48,'PLT8','active','ABS  (IVORY) ','1200.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (49,'PLT8','active','ABS  (IVORY) ','1200.000','2000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (50,'PLT8','active','ABS  (IVORY) ','1200.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (51,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (52,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (53,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (54,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','4.600','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (55,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',2972897,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:28:34');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (56,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (57,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (58,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (59,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (60,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','18.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (61,'PLT8','active','ACRYLIC  (CLEAR) ','1219.200','2438.400','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (62,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (63,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (64,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (65,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (66,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (67,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (68,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (69,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (70,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (71,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (72,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (73,'PLT8','active','BAKELITE  (ORANGE)','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (74,'PLT8','active','BAKELITE  (ORANGE)','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (75,'PLT8','active','BAKELITE  (ORANGE)','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (76,'PLT8','active','CDM  (BLACK) ','1219.200','2438.400','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (77,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (78,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (79,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-250000,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (80,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (81,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (82,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (83,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (84,'PLT8','active','CDM  (BLACK) ','1150.000','1250.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (85,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (86,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (87,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','7.500','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (88,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (89,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (90,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','16.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (91,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (92,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (93,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (94,'PLT8','active','MC501  CDR6  (BLACK) ','600.000','1200.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (95,'RODS','active','MC501 CDR6','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:35:47');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (96,'RODS','active','MC501 CDR6','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:35:58');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (97,'RODS','active','MC501 CDR6','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:35:53');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (98,'RODS','active','MC501 CDR6','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:02');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (99,'RODS','active','MC501 CDR6','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:07');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (100,'RODS','active','MC501 CDR6','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:11');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (101,'RODS','active','MC501 CDR6','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:16');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (102,'RODS','active','MC501 CDR6','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:21');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (103,'RODS','active','MC501 CDR6','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2021-02-27 22:36:26');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (104,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (105,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (106,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (107,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (108,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-4,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (109,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (110,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (111,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (112,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (113,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (114,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (115,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (116,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (117,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (118,'PLT8','active','DELRIN  (WHITE) ','1000.000','2000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (119,'PLT8','active','DELRIN  (WHITE) ','600.000','1200.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (120,'PLT8','active','DELRIN  (WHITE) ','600.000','1200.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (121,'PLT8','active','DELRIN  (WHITE) ','600.000','1200.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (122,'PLT8','active','DELRIN  (WHITE) ','600.000','1219.200','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (123,'PLT8','active','DELRIN  (WHITE) ','600.000','1219.200','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (124,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (125,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (126,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (127,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (128,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (129,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (130,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (131,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (132,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (133,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (134,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (135,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (136,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (137,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (138,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','65.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (139,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (140,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (141,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (142,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','85.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (143,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (144,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','95.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (145,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (146,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (147,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (148,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (149,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (150,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','145.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (151,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (152,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (153,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (154,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (155,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (156,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (157,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (158,'ROD','active','DELRIN (WHITE) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (159,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (160,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (161,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (162,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (163,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (164,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (165,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (166,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (167,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (168,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (169,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (170,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (171,'PLT8','active','DELRIN  (BLACK) ','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (172,'PLT8','active','DELRIN  (BLACK) ','600.000','1219.200','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (173,'PLT8','active','DELRIN  (BLACK) ','600.000','1219.200','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (174,'PLT8','active','DELRIN  (BLACK) ','600.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (175,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (176,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (177,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (178,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (179,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (180,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (181,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (182,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (183,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (184,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (185,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (186,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (187,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (188,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','65.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (189,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (190,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (191,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (192,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','85.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (193,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (194,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','95.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (195,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (196,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (197,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (198,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (199,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (200,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','145.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (201,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (202,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (203,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (204,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (205,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (206,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (207,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (208,'ROD','active','DELRIN (BLACK) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (209,'PLT8','active','DELRIN  (BLUE)','600.000','1200.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (210,'PLT8','active','DELRIN  (BLUE)','600.000','1200.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (211,'PLT8','active','DELRIN  (BLUE)','600.000','1200.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (212,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','0.500','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (213,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','0.800','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (214,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','1.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (215,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','0.900','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (216,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-250001,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (217,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (218,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','4.500','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (219,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (220,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (221,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (222,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (223,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (224,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (225,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (226,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (227,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (228,'PLT8','active','FR4 G10 (GREEN) ','1000.000','1200.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (229,'PLT8','active','G11 (YELLOW) ','1200.000','1200.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (230,'PLT8','active','G11 (YELLOW) ','1200.000','1200.000','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (231,'PLT8','active','G11 (YELLOW) ','1200.000','1200.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (232,'PLT8','active','G11 (YELLOW) ','1200.000','1200.000','5.500','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (233,'PLT8','active','G11 (YELLOW) ','1200.000','1200.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (234,'PLT8','active','G11 (YELLOW) ','1200.000','1200.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (235,'PLT8','active','G11 (YELLOW) ','1000.000','1200.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (236,'PLT8','active','G11 (YELLOW) ','1000.000','1200.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (237,'PLT8','active','G11 (YELLOW) ','1000.000','1200.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (238,'PLT8','active','G11 (YELLOW) ','1000.000','1200.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (239,'PLT8','active','G11 (YELLOW) ','1000.000','1200.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (240,'PLT8','active','G11 (YELLOW) ','1000.000','1200.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (241,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (242,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (243,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (244,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (245,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (246,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (247,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (248,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (249,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (250,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (251,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (252,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (253,'PLT8','active','NYLON  (BLUE) ','1000.000','2000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (254,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (255,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (256,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (257,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (258,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (259,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (260,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (261,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (262,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (263,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (264,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (265,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (266,'ROD','active','NYLON  (BLUE) ','600.000','0.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (267,'ROD','active','NYLON  (BLUE) ','500.000','0.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (268,'ROD','active','NYLON  (BLUE) ','600.000','0.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (269,'ROD','active','NYLON  (BLUE) ','600.000','0.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (270,'ROD','active','NYLON  (BLUE) ','600.000','0.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (271,'ROD','active','NYLON  (BLUE) ','600.000','0.000','65.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (272,'ROD','active','NYLON  (BLUE) ','600.000','0.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (273,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (274,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (275,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (276,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (277,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (278,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (279,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','115.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (280,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (281,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (282,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (283,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (284,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (285,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (286,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (287,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (288,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (289,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','210.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (290,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (291,'ROD','active','NYLON  (BLUE) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (292,'ROD','active','NYLON IVORY ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (293,'ROD','active','NYLON IVORY ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (294,'ROD','active','NYLON IVORY ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (295,'ROD','active','NYLON IVORY ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (296,'ROD','active','NYLON IVORY ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (297,'ROD','active','NYLON IVORY ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (298,'ROD','active','NYLON IVORY ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (299,'ROD','active','NYLON IVORY ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (300,'ROD','active','NYLON IVORY ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (301,'ROD','active','NYLON IVORY ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (302,'ROD','active','NYLON IVORY ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (303,'ROD','active','NYLON IVORY ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (304,'ROD','active','NYLON IVORY ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (305,'ROD','active','NYLON IVORY ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (306,'ROD','active','NYLON IVORY ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (307,'ROD','active','NYLON IVORY ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (308,'ROD','active','NYLON IVORY ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (309,'ROD','active','NYLON IVORY ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (310,'ROD','active','NYLON IVORY ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (311,'ROD','active','NYLON IVORY ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (312,'ROD','active','NYLON IVORY ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (313,'ROD','active','NYLON IVORY ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (314,'ROD','active','NYLON IVORY ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (315,'ROD','active','NYLON IVORY ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (316,'ROD','active','NYLON IVORY ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (317,'ROD','active','NYLON IVORY ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (318,'ROD','active','NYLON IVORY ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (319,'ROD','active','NYLON IVORY ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (320,'ROD','active','NYLON IVORY ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (321,'ROD','active','NYLON IVORY ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (322,'ROD','active','NYLON IVORY ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (323,'PLT8','active','NYLON (ivory)','1000.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (324,'PLT8','active','NYLON (ivory)','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (325,'PLT8','active','NYLON (ivory)','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (326,'PLT8','active','NYLON (ivory)','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (327,'PLT8','active','NYLON (ivory)','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (328,'PLT8','active','NYLON (ivory)','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (329,'PLT8','active','NYLON (ivory)','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (330,'PLT8','active','NYLON (ivory)','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (331,'PLT8','active','NYLON (ivory)','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (332,'PLT8','active','NYLON (ivory)','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (333,'PLT8','active','NYLON (ivory)','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (334,'PLT8','active','NYLON (ivory)','1000.000','2000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (335,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (336,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (337,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (338,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (339,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (340,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (341,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (342,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (343,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (344,'PLT8','active','NYLON  (BLACK)','1000.000','2000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (345,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (346,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (347,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (348,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (349,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (350,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (351,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (352,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (353,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (354,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (355,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (356,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (357,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (358,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (359,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (360,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (361,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (362,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (363,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (364,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (365,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (366,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (367,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (368,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (369,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (370,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (371,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (372,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (373,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (374,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (375,'ROD','active','NYLON (BLACK) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (376,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (377,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (378,'PLT8','active','PE  (WHITE) ','1219.200','2438.400','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (379,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (380,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (381,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (382,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (383,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (384,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (385,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (386,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (387,'PLT8','active','PE  (WHITE) ','1000.000','2000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (388,'ROD','active','PE  (WHITE) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (389,'ROD','active','PE  (WHITE) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (390,'ROD','active','PE  (WHITE) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (391,'ROD','active','PE  (WHITE) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (392,'ROD','active','PE  (WHITE) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (393,'ROD','active','PE  (WHITE) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (394,'ROD','active','PE  (WHITE) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (395,'ROD','active','PE  (WHITE) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (396,'ROD','active','PE  (WHITE) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (397,'ROD','active','PE  (WHITE) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (398,'ROD','active','PE  (WHITE) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (399,'ROD','active','PE  (WHITE) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (400,'ROD','active','PE  (WHITE) ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (401,'ROD','active','PE  (WHITE) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (402,'ROD','active','PE  (WHITE) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (403,'ROD','active','PE  (WHITE) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (404,'ROD','active','PE  (WHITE) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (405,'ROD','active','PE  (WHITE) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (406,'ROD','active','PE  (WHITE) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (407,'ROD','active','PE  (WHITE) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (408,'ROD','active','PE  (WHITE) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (409,'ROD','active','PE  (WHITE) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (410,'ROD','active','PE  (WHITE) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (411,'ROD','active','PE  (WHITE) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (412,'ROD','active','PE  (WHITE) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (413,'ROD','active','PE  (WHITE) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (414,'ROD','active','PE  (WHITE) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (415,'ROD','active','PE  (WHITE) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (416,'ROD','active','PE  (WHITE) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (417,'ROD','active','PE  (WHITE) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (418,'ROD','active','PE  (WHITE) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (419,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (420,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (421,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (422,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (423,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (424,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (425,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (426,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (427,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (428,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (429,'PLT8','active','PE  (BLACK) ','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (430,'ROD','active','PE (BLACK) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (431,'ROD','active','PE (BLACK) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (432,'ROD','active','PE (BLACK) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (433,'ROD','active','PE (BLACK) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (434,'ROD','active','PE (BLACK) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (435,'ROD','active','PE (BLACK) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (436,'ROD','active','PE (BLACK) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (437,'ROD','active','PE (BLACK) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (438,'ROD','active','PE (BLACK) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (439,'ROD','active','PE (BLACK) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (440,'ROD','active','PE (BLACK) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (441,'ROD','active','PE (BLACK) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (442,'ROD','active','PE (BLACK) ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (443,'ROD','active','PE (BLACK) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (444,'ROD','active','PE (BLACK) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (445,'ROD','active','PE (BLACK) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (446,'ROD','active','PE (BLACK) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (447,'ROD','active','PE (BLACK) ','1000.000','1000.000','85.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (448,'ROD','active','PE (BLACK) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (449,'ROD','active','PE (BLACK) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (450,'ROD','active','PE (BLACK) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (451,'ROD','active','PE (BLACK) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (452,'ROD','active','PE (BLACK) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (453,'ROD','active','PE (BLACK) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (454,'ROD','active','PE (BLACK) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (455,'ROD','active','PE (BLACK) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (456,'ROD','active','PE (BLACK) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (457,'ROD','active','PE (BLACK) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (458,'ROD','active','PE (BLACK) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (459,'ROD','active','PE (BLACK) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (460,'ROD','active','PE (BLACK) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (461,'ROD','active','PE (BLACK) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (462,'PLT8','active','PE  (GREEN)','1000.000','2000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (463,'PLT8','active','PE  (GREEN)','1000.000','2000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (464,'PLT8','active','PE  (GREEN)','1000.000','2000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (465,'PLT8','active','PE  (GREEN)','1000.000','2000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (466,'PLT8','active','PE  (GREEN)','1000.000','2000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (467,'PLT8','active','PE  (GREEN)','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (468,'PLT8','active','PE  (GREEN)','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (469,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (470,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (471,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (472,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (473,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (474,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (475,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','16.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (476,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (477,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','22.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (478,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (479,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (480,'PLT8','active','PEEK  (BEIGE) ','600.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (481,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (482,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (483,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (484,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (485,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (486,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (487,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (488,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (489,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (490,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (491,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (492,'ROD','active','PEEK  (BEIGE) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (493,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (494,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (495,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (496,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-2500000,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (497,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (498,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (499,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (500,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (501,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (502,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (503,'PLT8','active','PP  (NAT WHITE) ','1219.200','2438.400','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (504,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (505,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (506,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (507,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (508,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (509,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (510,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (511,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (512,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (513,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (514,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (515,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (516,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (517,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','65.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (518,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (519,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (520,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (521,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (522,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','85.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (523,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (524,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','95.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (525,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (526,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (527,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (528,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (529,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (530,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (531,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (532,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (533,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (534,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (535,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (536,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (537,'ROD','active','PP  (NAT WHITE) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (538,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','2.700','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (539,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (540,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (541,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (542,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (543,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (544,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (545,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (546,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (547,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (548,'PLT8','active','PVC  (GRAY) ','1219.200','2438.400','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (549,'PLT8','active','PVC  (GRAY) ','1000.000','2000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (550,'PLT8','active','PVC  (GRAY) ','1000.000','2000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (551,'ROD','active','PVC  (GRAY) ','1000.000','0.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (552,'ROD','active','PVC  (GRAY) ','1000.000','0.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (553,'ROD','active','PVC  (GRAY) ','1000.000','0.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (554,'ROD','active','PVC  (GRAY) ','1000.000','0.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (555,'ROD','active','PVC  (GRAY) ','1000.000','0.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (556,'ROD','active','PVC  (GRAY) ','1000.000','0.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (557,'ROD','active','PVC  (GRAY) ','1000.000','0.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (558,'ROD','active','PVC  (GRAY) ','1000.000','0.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (559,'ROD','active','PVC  (GRAY) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (560,'ROD','active','PVC  (GRAY) ','1000.000','0.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (561,'ROD','active','PVC  (GRAY) ','1000.000','0.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (562,'ROD','active','PVC  (GRAY) ','1000.000','0.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (563,'ROD','active','PVC  (GRAY) ','1000.000','0.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (564,'ROD','active','PVC  (GRAY) ','1000.000','0.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (565,'ROD','active','PVC  (GRAY) ','1000.000','0.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (566,'ROD','active','PVC  (GRAY) ','1000.000','0.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (567,'ROD','active','PVC  (GRAY) ','1000.000','0.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (568,'ROD','active','PVC  (GRAY) ','1000.000','1000.000','85.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (569,'ROD','active','PVC  (GRAY) ','1000.000','0.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (570,'ROD','active','PVC  (GRAY) ','1000.000','0.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (571,'ROD','active','PVC  (GRAY) ','1000.000','0.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (572,'ROD','active','PVC  (GRAY) ','1000.000','0.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (573,'ROD','active','PVC  (GRAY) ','1000.000','0.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (574,'ROD','active','PVC  (GRAY) ','1000.000','0.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (575,'ROD','active','PVC  (GRAY) ','1000.000','0.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (576,'ROD','active','PVC  (GRAY) ','1000.000','0.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (577,'ROD','active','PVC  (GRAY) ','1000.000','0.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (578,'ROD','active','PVC  (GRAY) ','1000.000','0.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (579,'ROD','active','PVC  (GRAY) ','1000.000','0.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (580,'ROD','active','PVC  (GRAY) ','1000.000','0.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (581,'ROD','active','PVC  (GRAY) ','1000.000','0.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (582,'ROD','active','PVC  (GRAY) ','1000.000','0.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (583,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','1000.00',-1000000,0,0,0,0,'2020-02-27 01:23:45','2021-04-22 14:45:13');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (584,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','4.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (585,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','4.500','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (586,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (587,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (588,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (589,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (590,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (591,'PLT8','active','RICOCEL  (BLACK)  ','1200.000','1200.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (592,'PLT8','active','ULTEM  (AMBER) ','600.000','1200.000','9.525','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (593,'PLT8','active','ULTEM  (AMBER) ','600.000','1200.000','9.525','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (594,'PLT8','active','ULTEM  (AMBER) ','600.000','1200.000','12.700','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (595,'PLT8','active','ULTEM  (AMBER) ','600.000','1200.000','12.700','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (596,'PLT8','active','ULTEM  (AMBER) ','600.000','1200.000','15.875','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (597,'PLT8','active','ULTEM  (AMBER) ','600.000','1200.000','25.400','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (598,'PLT8','active','ULTEM  (AMBER) ','600.000','1200.000','31.750','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (599,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','6.350','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (600,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','9.525','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (601,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','12.700','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (602,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','15.875','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (603,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','19.050','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (604,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','25.400','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (605,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','31.750','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (606,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','38.100','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (607,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','44.450','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (608,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','63.750','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (609,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','76.200','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (610,'ROD','active','ULTEM  (AMBER) ','1000.000','1000.000','69.850','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (611,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','6.350','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (612,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','9.525','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (613,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','12.700','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (614,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','15.875','',0,'','mm',' ','0.00','0.00','0.00','0.00',-2,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (615,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','19.050','',0,'','mm',' ','0.00','0.00','0.00','500.00',-27,0,0,0,0,'2020-02-27 01:23:45','2021-04-22 20:22:28');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (616,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','22.220','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (617,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','25.400','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (618,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','31.750','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (619,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','38.100','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (620,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','44.450','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (621,'ROD','active','SEMITRON ESD225   (IVORY) ','1000.000','1000.000','50.800','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (622,'ROD','active','POLYCARBONATE   (CLEAR) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (623,'ROD','active','POLYCARBONATE   (CLEAR) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (624,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (625,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (626,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (627,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (628,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (629,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (630,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (631,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (632,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (633,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (634,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (635,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (636,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (637,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (638,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (639,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (640,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (641,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (642,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (643,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (644,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (645,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (646,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (647,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (648,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (649,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','160.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (650,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','170.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (651,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','180.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (652,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','190.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (653,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','200.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (654,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','220.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (655,'ROD','active','TEFLON  (WHITE) ','1000.000','1000.000','250.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (656,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','1.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (657,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (658,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (659,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (660,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (661,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (662,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (663,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (664,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (665,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (666,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (667,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (668,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (669,'PLT8','active','TEFLON  (WHITE) ','1200.000','1200.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (670,'PLT8','active','TORLON  4203   (YELLOW)','300.000','300.000','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (671,'PLT8','active','TORLON  4203   (YELLOW)','300.000','300.000','7.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (672,'PLT8','active','TORLON  4203   (YELLOW)','300.000','300.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (673,'PLT8','active','TORLON  4203   (YELLOW)','300.000','300.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (674,'PLT8','active','TORLON  4203   (YELLOW)','300.000','300.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (675,'PLT8','active','TORLON  4203   (YELLOW)','300.000','300.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (676,'PLT8','active','TORLON  4203   (YELLOW)','300.000','300.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (677,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','6.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (678,'ROD','active','PU (YELLOW) PLATE ','300.000','0.000','8.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (679,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (680,'ROD','active','PU (YELLOW) PLATE ','300.000','0.000','12.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (681,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (682,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (683,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','25.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (684,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (685,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','35.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (686,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (687,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','45.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (688,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (689,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','55.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (690,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','60.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (691,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','65.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (692,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','70.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (693,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','75.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (694,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','80.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (695,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','85.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (696,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','90.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (697,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','100.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (698,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','110.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (699,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','120.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (700,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','130.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (701,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','140.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (702,'ROD','active','PU (YELLOW) PLATE ','500.000','0.000','150.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (703,'PLT8','active','PU YELLOW','1219.200','2438.400','2.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (704,'PLT8','active','PU YELLOW','1219.200','2438.400','3.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (705,'PLT8','active','PU YELLOW','1219.200','2438.400','5.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (706,'PLT8','active','PU YELLOW','1219.200','2438.400','10.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (707,'PLT8','active','PU YELLOW','1219.200','2438.400','15.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',-1,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (708,'PLT8','active','PU YELLOW','1219.200','2438.400','20.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (709,'PLT8','active','PU YELLOW','1219.200','2438.400','30.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (710,'PLT8','active','PU YELLOW','1000.000','1000.000','40.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');
insert  into `products`(`id`,`category_id`,`status`,`particular`,`length`,`width`,`thickness`,`type`,`allowance`,`part_no`,`unit`,`description`,`capital`,`markup`,`srp`,`tmp_srp`,`soh_quantity`,`tmp_quantity`,`min_quantity`,`max_quantity`,`discountable`,`created`,`modified`) values (711,'PLT8','active','PU YELLOW','1000.000','1000.000','50.000','',0,'','mm',' ','0.00','0.00','0.00','0.00',0,0,0,0,0,'2020-02-27 01:23:45','2020-02-27 01:23:45');

/*Table structure for table `purchase_order_details` */

DROP TABLE IF EXISTS `purchase_order_details`;

CREATE TABLE `purchase_order_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `length` int(11) NOT NULL,
  `width` int(11) NOT NULL,
  `thickness` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `quantity_area` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=latin1;

/*Data for the table `purchase_order_details` */

insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (1,1,1,10,10,4,5,500,'500.00','2500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (2,1,1,15,15,4,3,675,'500.00','1500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (3,2,1,12,12,4,4,576,'500.00','2000.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (4,2,1,80,80,4,10,64000,'500.00','5000.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (5,2,2,5,5,5,2,50,'100.00','200.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (6,3,1,10,10,4,10,1000,'500.00','5000.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (7,4,1,10,10,4,20,2000,'500.00','10000.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (8,4,2,10,10,5,2,200,'100.00','200.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (9,5,1,10,10,4,6,600,'500.00','3000.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (10,6,1,1,1,4,5,5,'500.00','2500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (11,6,2,1,1,5,5,5,'100.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (12,6,5,1,1,2,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (13,7,615,5,5,19,1,25,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (14,7,5,411,411,2,1,168921,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (15,7,216,500,500,2,1,250000,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (16,7,496,500,500,10,10,2500000,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (17,7,20,100,100,10,1,10000,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (18,8,583,1000,1000,3,1,1000000,'1000.00','1000.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (19,8,3,1,1,6,1,1,'50.00','50.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (20,8,615,1,1,19,1,1,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (21,8,628,1,1,12,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (22,8,707,1,1,15,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (23,8,216,1,1,2,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (24,8,108,1,1,6,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (25,8,596,1,1,16,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (26,8,244,1,1,12,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (27,8,497,1,1,12,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (28,8,107,1,1,5,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (29,8,8,1,1,5,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (30,8,1,1,1,4,1,1,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (31,8,65,1,1,5,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (32,9,1,1,1,4,1,1,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (33,9,3,1,1,6,1,1,'50.00','50.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (34,9,5,1,1,2,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (35,10,1,1,1,4,1,1,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (36,10,611,1,1,6,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (37,10,615,1,1,19,1,1,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (38,10,108,1,1,6,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (39,10,586,1,1,5,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (40,11,614,1,1,16,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (41,11,1,1,1,4,1,1,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (42,11,5,1,1,2,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (43,11,5,1,1,2,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (44,11,108,1,1,6,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (45,11,614,1,1,16,1,1,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (46,12,5,1,1,2,1,1,'500.00','500.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (47,12,79,500,500,6,1,250000,'0.00','0.00');
insert  into `purchase_order_details`(`id`,`purchase_order_id`,`product_id`,`length`,`width`,`thickness`,`quantity`,`quantity_area`,`price`,`amount`) values (48,12,108,1,1,6,1,1,'0.00','0.00');

/*Table structure for table `purchase_order_terms` */

DROP TABLE IF EXISTS `purchase_order_terms`;

CREATE TABLE `purchase_order_terms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `created` datetime NOT NULL,
  `modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=latin1;

/*Data for the table `purchase_order_terms` */

insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (1,3,'CHRG','15','500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (2,4,'CHRG','10','500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (3,5,'CHRG','15','7500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (4,6,'CHRG','15','2500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (5,7,'CHRG','15','2500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (6,8,'CHRG','15','2500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (7,9,'CHRG','15','2500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (8,10,'CHRG','15','2725.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (9,11,'CHRG','15','7500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (10,12,'CHRG','15','1500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (11,13,'CHRG','15','1500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (12,14,'CHRG','15','1500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (13,15,'CHRG','15','10000.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (14,16,'CHRG','15','10000.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (15,17,'CHRG','15','7500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (16,18,'CHRG','15','5000.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (17,19,'CHRG','15','7500.00','0000-00-00 00:00:00','0000-00-00 00:00:00');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (18,20,'CHRG','15','5000.00','2021-04-19 07:55:10','2021-04-19 07:55:10');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (19,21,'CHRG','15','500.00','2021-04-19 09:30:40','2021-04-19 09:30:40');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (20,22,'CHRG','15','5000.00','2021-04-19 20:12:39','2021-04-19 20:12:39');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (21,1,'CHRG','15','4000.00','2021-04-19 20:47:07','2021-04-19 20:47:07');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (22,2,'CHRG','30','7200.00','2021-04-19 20:48:07','2021-04-19 20:48:07');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (23,3,'CHRG','15','5000.00','2021-04-19 22:49:44','2021-04-19 22:49:44');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (24,4,'CHRG','30','10200.00','2021-04-19 23:01:53','2021-04-19 23:01:53');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (25,5,'CHRG','15','3000.00','2021-04-20 01:06:01','2021-04-20 01:06:01');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (26,6,'CHRG','15','3000.00','2021-04-21 14:54:41','2021-04-21 14:54:41');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (27,7,'CHRG','15','500.00','2021-04-22 14:41:10','2021-04-22 14:41:10');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (28,8,'CHRG','15','2050.00','2021-04-22 14:44:46','2021-04-22 14:44:46');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (29,9,'CHRG','10','550.00','2021-04-22 14:48:49','2021-04-22 14:48:49');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (30,10,'CHRG','15','1000.00','2021-04-22 20:21:59','2021-04-22 20:21:59');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (31,11,'CHRG','10','500.00','2021-04-22 20:24:03','2021-04-22 20:24:03');
insert  into `purchase_order_terms`(`id`,`purchase_order_id`,`payment_type`,`detail`,`amount`,`created`,`modified`) values (32,12,'CHRG','15','500.00','2021-04-23 06:15:59','2021-04-23 06:15:59');

/*Table structure for table `purchase_orders` */

DROP TABLE IF EXISTS `purchase_orders`;

CREATE TABLE `purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `po_no` varchar(20) NOT NULL,
  `po_date` date DEFAULT NULL,
  `customer` varchar(80) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;

/*Data for the table `purchase_orders` */

insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (1,'*12323','2021-04-19','Customer A','4000.00','0.00','0.00','0.00','0.00','2021-04-19 20:47:07');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (2,'','2021-04-19','Customer A','7200.00','0.00','0.00','0.00','0.00','2021-04-19 20:48:07');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (3,'*9834','2021-04-19','Customer A','5000.00','0.00','0.00','0.00','0.00','2021-04-19 22:49:44');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (4,'','2021-04-19','Customer A','10200.00','0.00','0.00','0.00','0.00','2021-04-19 23:01:53');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (5,'','2021-04-20','Customer A','3000.00','0.00','0.00','0.00','0.00','2021-04-20 01:06:01');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (6,'123','2021-04-21','Customer A','3000.00','0.00','0.00','0.00','0.00','2021-04-21 14:54:41');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (7,'234','2021-04-22','Customer A','500.00','0.00','0.00','0.00','0.00','2021-04-22 14:41:10');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (8,'445','2021-04-22','Customer A','2050.00','0.00','0.00','0.00','0.00','2021-04-22 14:44:45');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (9,'213154','2021-04-22','CASH','550.00','0.00','0.00','0.00','0.00','2021-04-22 14:48:49');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (10,'','2021-04-22','Customer A','1000.00','0.00','0.00','0.00','0.00','2021-04-22 20:21:58');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (11,'','2021-04-22','Customer A','500.00','0.00','0.00','0.00','0.00','2021-04-22 20:24:02');
insert  into `purchase_orders`(`id`,`po_no`,`po_date`,`customer`,`total`,`commission`,`discount`,`tax`,`interest`,`created`) values (12,'123123','2021-04-23','Customer A','500.00','0.00','0.00','0.00','0.00','2021-04-23 06:15:59');

/*Table structure for table `supplier_ledgers` */

DROP TABLE IF EXISTS `supplier_ledgers`;

CREATE TABLE `supplier_ledgers` (
  `id` char(36) NOT NULL DEFAULT '',
  `supplier_id` int(11) DEFAULT NULL,
  `ref_no` varchar(20) DEFAULT NULL,
  `particulars` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `supplier_ledgers` */

/*Table structure for table `suppliers` */

DROP TABLE IF EXISTS `suppliers`;

CREATE TABLE `suppliers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'open',
  `tax_type` char(3) NOT NULL COMMENT 'VAT- VATable, ZRO-  NonVat',
  `tin` varchar(20) NOT NULL,
  `address` varchar(150) NOT NULL,
  `business_style` varchar(150) NOT NULL,
  `unit_system` char(3) NOT NULL COMMENT 'ENG - English, MET - Metric',
  `last_bill` int(11) NOT NULL,
  `begin_balance` decimal(10,2) NOT NULL,
  `current_balance` decimal(10,2) NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

/*Data for the table `suppliers` */

insert  into `suppliers`(`id`,`name`,`status`,`tax_type`,`tin`,`address`,`business_style`,`unit_system`,`last_bill`,`begin_balance`,`current_balance`,`created`,`modified`) values (1,'Supplier A','open','VAT','1121432','asfsadf','23412fw','ENG',0,'0.00','0.00','2021-03-09 12:09:37','2021-04-17 21:33:28');

/*Table structure for table `transaction_details` */

DROP TABLE IF EXISTS `transaction_details`;

CREATE TABLE `transaction_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` char(36) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=147 DEFAULT CHARSET=latin1;

/*Data for the table `transaction_details` */

insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (1,'607d7bcb-ca38-4405-853e-41fa82e7a674',1,5,'500.00','2500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (2,'607d7bcb-ca38-4405-853e-41fa82e7a674',1,3,'500.00','1500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (3,'607d7c07-6a9c-453c-bc57-405782e7a674',1,4,'500.00','2000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (4,'607d7c07-6a9c-453c-bc57-405782e7a674',1,10,'500.00','5000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (5,'607d7c07-6a9c-453c-bc57-405782e7a674',2,2,'100.00','200.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (6,'607d7c17-62f8-4ac1-b46d-481482e7a674',1,500,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (7,'607d7c17-62f8-4ac1-b46d-481482e7a674',1,675,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (8,'607d7c20-1754-4b75-aa63-484b82e7a674',1,576,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (9,'607d7c20-1754-4b75-aa63-484b82e7a674',1,64000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (10,'607d7c20-1754-4b75-aa63-484b82e7a674',2,50,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (11,'607d7dc5-84a0-477f-8ebc-4bf982e7a674',1,5,'500.00','2500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (12,'607d7dc5-84a0-477f-8ebc-4bf982e7a674',1,3,'500.00','1500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (13,'607d7e0c-d540-4ac9-b6cd-465382e7a674',1,5,'500.00','2500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (14,'607d7e0c-d540-4ac9-b6cd-465382e7a674',1,3,'500.00','1500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (15,'607d7e29-66a4-4c32-ba2a-405a82e7a674',1,4,'500.00','2000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (16,'607d7e29-66a4-4c32-ba2a-405a82e7a674',1,10,'500.00','5000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (17,'607d7e29-66a4-4c32-ba2a-405a82e7a674',2,2,'100.00','200.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (18,'607d7f1c-3f6c-474e-81e9-477182e7a674',1,5,'500.00','2500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (19,'607d7f1c-3f6c-474e-81e9-477182e7a674',1,3,'500.00','1500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (20,'607d88e2-c4fc-40a4-a1f7-428682e7a674',1,4,'500.00','2000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (21,'607d88e2-c4fc-40a4-a1f7-428682e7a674',1,10,'500.00','5000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (22,'607d88e2-c4fc-40a4-a1f7-428682e7a674',2,2,'100.00','200.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (23,'607d9888-d224-411f-a954-4fbc82e7a674',1,10,'500.00','5000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (24,'607d9947-9cf8-4a12-a16e-499282e7a674',1,1000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (25,'607d9959-50cc-4bdd-ba7d-4c3c82e7a674',1,10,'500.00','5000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (26,'607d9b61-14e0-4714-a5f9-43fa82e7a674',1,20,'500.00','10000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (27,'607d9b61-14e0-4714-a5f9-43fa82e7a674',2,2,'100.00','200.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (28,'607db879-feac-45f6-86fe-459282e7a674',1,6,'500.00','3000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (29,'607db884-e5e0-4d68-9b96-45a782e7a674',1,2000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (30,'607db884-e5e0-4d68-9b96-45a782e7a674',2,200,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (31,'607db893-f05c-4edf-8bf2-488882e7a674',1,20,'500.00','10000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (32,'607db893-f05c-4edf-8bf2-488882e7a674',2,2,'100.00','200.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (33,'607fcc31-45f8-4eb3-bcf1-183882e7a674',1,5,'500.00','2500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (34,'607fcc31-45f8-4eb3-bcf1-183882e7a674',2,5,'100.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (35,'607fcc31-45f8-4eb3-bcf1-183882e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (36,'607fcc5d-1aa0-4019-94a5-183882e7a674',1,5,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (37,'607fcc5d-1aa0-4019-94a5-183882e7a674',2,5,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (38,'607fcc5d-1aa0-4019-94a5-183882e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (39,'607fccb9-3004-4b96-b0e2-183882e7a674',1,5,'500.00','2500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (40,'607fccb9-3004-4b96-b0e2-183882e7a674',2,5,'100.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (41,'607fccb9-3004-4b96-b0e2-183882e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (42,'60811a86-e818-4cde-9c1f-28dc82e7a674',615,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (43,'60811a86-e818-4cde-9c1f-28dc82e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (44,'60811a86-e818-4cde-9c1f-28dc82e7a674',216,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (45,'60811a86-e818-4cde-9c1f-28dc82e7a674',496,10,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (46,'60811a86-e818-4cde-9c1f-28dc82e7a674',20,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (47,'60811aa3-a914-4b0a-9de8-28dc82e7a674',615,25,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (48,'60811aa3-a914-4b0a-9de8-28dc82e7a674',5,168921,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (49,'60811aa3-a914-4b0a-9de8-28dc82e7a674',216,250000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (50,'60811aa3-a914-4b0a-9de8-28dc82e7a674',496,2500000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (51,'60811aa3-a914-4b0a-9de8-28dc82e7a674',20,10000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (52,'60811ab9-1108-4b8e-985c-28dc82e7a674',615,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (53,'60811ab9-1108-4b8e-985c-28dc82e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (54,'60811ab9-1108-4b8e-985c-28dc82e7a674',216,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (55,'60811ab9-1108-4b8e-985c-28dc82e7a674',496,10,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (56,'60811ab9-1108-4b8e-985c-28dc82e7a674',20,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (57,'60811b5d-e348-4e58-8104-28dc82e7a674',583,1,'1000.00','1000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (58,'60811b5d-e348-4e58-8104-28dc82e7a674',3,1,'50.00','50.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (59,'60811b5d-e348-4e58-8104-28dc82e7a674',615,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (60,'60811b5d-e348-4e58-8104-28dc82e7a674',628,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (61,'60811b5d-e348-4e58-8104-28dc82e7a674',707,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (62,'60811b5d-e348-4e58-8104-28dc82e7a674',216,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (63,'60811b5d-e348-4e58-8104-28dc82e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (64,'60811b5d-e348-4e58-8104-28dc82e7a674',596,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (65,'60811b5d-e348-4e58-8104-28dc82e7a674',244,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (66,'60811b5d-e348-4e58-8104-28dc82e7a674',497,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (67,'60811b5d-e348-4e58-8104-28dc82e7a674',107,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (68,'60811b5d-e348-4e58-8104-28dc82e7a674',8,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (69,'60811b5d-e348-4e58-8104-28dc82e7a674',1,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (70,'60811b5d-e348-4e58-8104-28dc82e7a674',65,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (71,'60811b6c-7794-45e9-88ff-28dc82e7a674',583,1000000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (72,'60811b6c-7794-45e9-88ff-28dc82e7a674',3,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (73,'60811b6c-7794-45e9-88ff-28dc82e7a674',615,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (74,'60811b6c-7794-45e9-88ff-28dc82e7a674',628,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (75,'60811b6c-7794-45e9-88ff-28dc82e7a674',707,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (76,'60811b6c-7794-45e9-88ff-28dc82e7a674',216,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (77,'60811b6c-7794-45e9-88ff-28dc82e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (78,'60811b6c-7794-45e9-88ff-28dc82e7a674',596,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (79,'60811b6c-7794-45e9-88ff-28dc82e7a674',244,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (80,'60811b6c-7794-45e9-88ff-28dc82e7a674',497,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (81,'60811b6c-7794-45e9-88ff-28dc82e7a674',107,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (82,'60811b6c-7794-45e9-88ff-28dc82e7a674',8,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (83,'60811b6c-7794-45e9-88ff-28dc82e7a674',1,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (84,'60811b6c-7794-45e9-88ff-28dc82e7a674',65,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (85,'60811b79-fb7c-4dba-b88e-28dc82e7a674',583,1,'1000.00','1000.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (86,'60811b79-fb7c-4dba-b88e-28dc82e7a674',3,1,'50.00','50.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (87,'60811b79-fb7c-4dba-b88e-28dc82e7a674',615,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (88,'60811b79-fb7c-4dba-b88e-28dc82e7a674',628,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (89,'60811b79-fb7c-4dba-b88e-28dc82e7a674',707,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (90,'60811b79-fb7c-4dba-b88e-28dc82e7a674',216,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (91,'60811b79-fb7c-4dba-b88e-28dc82e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (92,'60811b79-fb7c-4dba-b88e-28dc82e7a674',596,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (93,'60811b79-fb7c-4dba-b88e-28dc82e7a674',244,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (94,'60811b79-fb7c-4dba-b88e-28dc82e7a674',497,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (95,'60811b79-fb7c-4dba-b88e-28dc82e7a674',107,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (96,'60811b79-fb7c-4dba-b88e-28dc82e7a674',8,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (97,'60811b79-fb7c-4dba-b88e-28dc82e7a674',1,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (98,'60811b79-fb7c-4dba-b88e-28dc82e7a674',65,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (99,'60811c51-85b4-41a0-bb6a-28dc82e7a674',1,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (100,'60811c51-85b4-41a0-bb6a-28dc82e7a674',3,1,'50.00','50.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (101,'60811c51-85b4-41a0-bb6a-28dc82e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (102,'60816a09-e728-4b79-ae9b-325482e7a674',1,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (103,'60816a09-e728-4b79-ae9b-325482e7a674',3,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (104,'60816a09-e728-4b79-ae9b-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (105,'60816a66-ddbc-487d-a6d7-325482e7a674',1,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (106,'60816a66-ddbc-487d-a6d7-325482e7a674',611,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (107,'60816a66-ddbc-487d-a6d7-325482e7a674',615,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (108,'60816a66-ddbc-487d-a6d7-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (109,'60816a66-ddbc-487d-a6d7-325482e7a674',586,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (110,'60816a74-c894-4d86-8814-325482e7a674',1,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (111,'60816a74-c894-4d86-8814-325482e7a674',611,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (112,'60816a74-c894-4d86-8814-325482e7a674',615,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (113,'60816a74-c894-4d86-8814-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (114,'60816a74-c894-4d86-8814-325482e7a674',586,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (115,'60816a84-9abc-4b10-be09-325482e7a674',1,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (116,'60816a84-9abc-4b10-be09-325482e7a674',611,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (117,'60816a84-9abc-4b10-be09-325482e7a674',615,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (118,'60816a84-9abc-4b10-be09-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (119,'60816a84-9abc-4b10-be09-325482e7a674',586,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (120,'60816ae2-be70-4641-8f2f-325482e7a674',614,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (121,'60816ae2-be70-4641-8f2f-325482e7a674',1,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (122,'60816ae2-be70-4641-8f2f-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (123,'60816ae2-be70-4641-8f2f-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (124,'60816ae2-be70-4641-8f2f-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (125,'60816ae2-be70-4641-8f2f-325482e7a674',614,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (126,'60816aef-5d8c-4d31-bfa5-325482e7a674',614,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (127,'60816aef-5d8c-4d31-bfa5-325482e7a674',1,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (128,'60816aef-5d8c-4d31-bfa5-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (129,'60816aef-5d8c-4d31-bfa5-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (130,'60816aef-5d8c-4d31-bfa5-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (131,'60816aef-5d8c-4d31-bfa5-325482e7a674',614,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (132,'60816b04-4ef0-47ea-b50a-325482e7a674',614,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (133,'60816b04-4ef0-47ea-b50a-325482e7a674',1,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (134,'60816b04-4ef0-47ea-b50a-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (135,'60816b04-4ef0-47ea-b50a-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (136,'60816b04-4ef0-47ea-b50a-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (137,'60816b04-4ef0-47ea-b50a-325482e7a674',614,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (138,'6081f59f-e50c-4325-b0e0-325482e7a674',5,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (139,'6081f59f-e50c-4325-b0e0-325482e7a674',79,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (140,'6081f59f-e50c-4325-b0e0-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (141,'6081f5ae-3450-42fb-866b-325482e7a674',5,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (142,'6081f5ae-3450-42fb-866b-325482e7a674',79,250000,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (143,'6081f5ae-3450-42fb-866b-325482e7a674',108,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (144,'6081f5bb-035c-4d31-ad34-325482e7a674',5,1,'500.00','500.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (145,'6081f5bb-035c-4d31-ad34-325482e7a674',79,1,'0.00','0.00');
insert  into `transaction_details`(`id`,`transaction_id`,`product_id`,`quantity`,`price`,`amount`) values (146,'6081f5bb-035c-4d31-ad34-325482e7a674',108,1,'0.00','0.00');

/*Table structure for table `transaction_payments` */

DROP TABLE IF EXISTS `transaction_payments`;

CREATE TABLE `transaction_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` char(36) DEFAULT NULL,
  `payment_type` char(4) DEFAULT NULL COMMENT 'CASH, CARD, CHQE, CHRG',
  `detail` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=latin1;

/*Data for the table `transaction_payments` */

insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (1,'607d7bcb-ca38-4405-853e-41fa82e7a674','CHRG','15','4000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (2,'607d7c07-6a9c-453c-bc57-405782e7a674','CHRG','30','7200.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (3,'607d7dc5-84a0-477f-8ebc-4bf982e7a674','CHRG','30','4000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (4,'607d7e0c-d540-4ac9-b6cd-465382e7a674','CHRG','30','4000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (5,'607d7e29-66a4-4c32-ba2a-405a82e7a674','CHRG','30','7200.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (6,'607d7f1c-3f6c-474e-81e9-477182e7a674','CHRG','30','4000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (7,'607d88e2-c4fc-40a4-a1f7-428682e7a674','CHRG','30','7200.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (8,'607d9888-d224-411f-a954-4fbc82e7a674','CHRG','15','5000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (9,'607d9959-50cc-4bdd-ba7d-4c3c82e7a674','CHRG','15','5000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (10,'607d9b61-14e0-4714-a5f9-43fa82e7a674','CHRG','30','10200.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (11,'607db879-feac-45f6-86fe-459282e7a674','CHRG','15','3000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (12,'607db893-f05c-4edf-8bf2-488882e7a674','CHRG','15','10200.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (13,'607fcc31-45f8-4eb3-bcf1-183882e7a674','CHRG','15','3000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (14,'607fccb9-3004-4b96-b0e2-183882e7a674','CHRG','15','3000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (15,'60811a86-e818-4cde-9c1f-28dc82e7a674','CHRG','15','500.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (16,'60811ab9-1108-4b8e-985c-28dc82e7a674','CHRG','15','500.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (17,'60811b5d-e348-4e58-8104-28dc82e7a674','CHRG','15','2050.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (18,'60811b79-fb7c-4dba-b88e-28dc82e7a674','CHRG','15','2050.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (19,'60811c51-85b4-41a0-bb6a-28dc82e7a674','CHRG','10','550.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (20,'60816a66-ddbc-487d-a6d7-325482e7a674','CHRG','15','1000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (21,'60816a84-9abc-4b10-be09-325482e7a674','CHRG','15','1000.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (22,'60816ae2-be70-4641-8f2f-325482e7a674','CHRG','10','500.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (23,'60816b04-4ef0-47ea-b50a-325482e7a674','CHRG','10','500.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (24,'6081f59f-e50c-4325-b0e0-325482e7a674','CHRG','15','500.00');
insert  into `transaction_payments`(`id`,`transaction_id`,`payment_type`,`detail`,`amount`) values (25,'6081f5bb-035c-4d31-ad34-325482e7a674','CHRG','15','500.00');

/*Table structure for table `transactions` */

DROP TABLE IF EXISTS `transactions`;

CREATE TABLE `transactions` (
  `id` char(36) NOT NULL DEFAULT '',
  `user` varchar(10) DEFAULT NULL,
  `type` char(10) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `entity_type` char(10) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `ref_no` varchar(25) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `commission` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `tax` decimal(10,2) DEFAULT NULL,
  `interest` decimal(10,2) DEFAULT NULL,
  `flag` char(1) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `created` datetime DEFAULT NULL,
  `modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

/*Data for the table `transactions` */

insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d7bcb-ca38-4405-853e-41fa82e7a674','admin','po','invoiced','customer',2,'1','4000.00','0.00','0.00','0.00','0.00',NULL,'2021-03-19 20:47:07','2021-03-19 20:47:07','2021-04-19 21:01:16');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d7c07-6a9c-453c-bc57-405782e7a674','admin','po','invoiced','customer',2,'2','7200.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 20:48:07','2021-04-19 20:48:07','2021-04-19 21:42:58');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d7c17-62f8-4ac1-b46d-481482e7a674','admin','jo','invoiced','customer',2,'1','8.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 20:48:23','2021-04-19 20:48:23','2021-04-19 21:01:16');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d7c20-1754-4b75-aa63-484b82e7a674','admin','jo','invoiced','customer',2,'2','16.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 20:48:32','2021-04-19 20:48:32','2021-04-19 21:42:58');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d7dc5-84a0-477f-8ebc-4bf982e7a674','admin','invoice','fulfilled','customer',2,'1','4000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 20:55:33','2021-04-19 20:55:33','2021-04-19 20:55:33');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d88e2-c4fc-40a4-a1f7-428682e7a674','admin','invoice','fulfilled','customer',2,'5','7200.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 21:42:58','2021-04-19 21:42:58','2021-04-19 21:42:58');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d9888-d224-411f-a954-4fbc82e7a674','admin','po','invoiced','customer',2,'3','5000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 22:49:44','2021-04-19 22:49:44','2021-04-19 22:53:13');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d9947-9cf8-4a12-a16e-499282e7a674','admin','jo','invoiced','customer',2,'3','10.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 22:52:55','2021-04-19 22:52:55','2021-04-19 22:53:13');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d9959-50cc-4bdd-ba7d-4c3c82e7a674','admin','invoice','fulfilled','customer',2,'6','5000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 22:53:13','2021-04-19 22:53:13','2021-04-19 22:53:13');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607d9b61-14e0-4714-a5f9-43fa82e7a674','admin','po','invoiced','customer',2,'4','10200.00','0.00','0.00','0.00','0.00',NULL,'2021-04-19 23:01:53','2021-04-19 23:01:53','2021-04-20 01:06:27');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607db879-feac-45f6-86fe-459282e7a674','admin','po','created','customer',2,'5','3000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-20 01:06:01','2021-04-20 01:06:01','2021-04-20 01:06:01');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607db884-e5e0-4d68-9b96-45a782e7a674','admin','jo','invoiced','customer',2,'4','22.00','0.00','0.00','0.00','0.00',NULL,'2021-04-20 01:06:12','2021-04-20 01:06:12','2021-04-20 01:06:27');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607db893-f05c-4edf-8bf2-488882e7a674','admin','invoice','fulfilled','customer',2,'7','10200.00','0.00','0.00','0.00','0.00',NULL,'2021-04-20 01:06:27','2021-04-20 01:06:27','2021-04-20 01:06:27');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607fcc31-45f8-4eb3-bcf1-183882e7a674','admin','po','invoiced','customer',2,'6','3000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-21 14:54:41','2021-04-21 14:54:41','2021-04-21 14:56:57');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607fcc5d-1aa0-4019-94a5-183882e7a674','admin','jo','invoiced','customer',2,'5','11.00','0.00','0.00','0.00','0.00',NULL,'2021-04-21 14:55:25','2021-04-21 14:55:25','2021-04-21 14:56:57');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('607fccb9-3004-4b96-b0e2-183882e7a674','admin','invoice','fulfilled','customer',2,'8','3000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-21 14:56:57','2021-04-21 14:56:57','2021-04-21 14:56:57');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60811a86-e818-4cde-9c1f-28dc82e7a674','admin','po','invoiced','customer',2,'7','500.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 14:41:10','2021-04-22 14:41:10','2021-04-22 14:42:02');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60811aa3-a914-4b0a-9de8-28dc82e7a674','admin','jo','invoiced','customer',2,'6','14.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 14:41:39','2021-04-22 14:41:39','2021-04-22 14:42:02');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60811ab9-1108-4b8e-985c-28dc82e7a674','admin','invoice','fulfilled','customer',2,'9','500.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 14:42:01','2021-04-22 14:42:01','2021-04-22 14:42:01');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60811b5d-e348-4e58-8104-28dc82e7a674','admin','po','invoiced','customer',2,'8','2050.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 14:44:45','2021-04-22 14:44:45','2021-04-22 14:45:14');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60811b6c-7794-45e9-88ff-28dc82e7a674','admin','jo','invoiced','customer',2,'7','14.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 14:45:00','2021-04-22 14:45:00','2021-04-22 14:45:14');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60811b79-fb7c-4dba-b88e-28dc82e7a674','admin','invoice','fulfilled','customer',2,'10','2050.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 14:45:12','2021-04-22 14:45:13','2021-04-22 14:45:13');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60811c51-85b4-41a0-bb6a-28dc82e7a674','admin','po','inprogress','customer',1,'9','550.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 14:48:49','2021-04-22 14:48:49','2021-04-22 20:20:25');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60816a09-e728-4b79-ae9b-325482e7a674','admin','jo','created','customer',1,'8','3.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 20:20:25','2021-04-22 20:20:25','2021-04-22 20:20:25');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60816a66-ddbc-487d-a6d7-325482e7a674','admin','po','invoiced','customer',2,'10','1000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 20:21:58','2021-04-22 20:21:58','2021-04-22 20:22:28');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60816a74-c894-4d86-8814-325482e7a674','admin','jo','invoiced','customer',2,'9','5.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 20:22:12','2021-04-22 20:22:12','2021-04-22 20:22:28');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60816a84-9abc-4b10-be09-325482e7a674','admin','invoice','fulfilled','customer',2,'11','1000.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 20:22:28','2021-04-22 20:22:28','2021-04-22 20:22:28');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60816ae2-be70-4641-8f2f-325482e7a674','admin','po','invoiced','customer',2,'11','500.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 20:24:02','2021-04-22 20:24:02','2021-04-22 20:24:37');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60816aef-5d8c-4d31-bfa5-325482e7a674','admin','jo','invoiced','customer',2,'10','6.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 20:24:15','2021-04-22 20:24:15','2021-04-22 20:24:36');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('60816b04-4ef0-47ea-b50a-325482e7a674','admin','invoice','fulfilled','customer',2,'12','500.00','0.00','0.00','0.00','0.00',NULL,'2021-04-22 20:24:36','2021-04-22 20:24:36','2021-04-22 20:24:36');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('6081f59f-e50c-4325-b0e0-325482e7a674',NULL,'po','invoiced','customer',2,'12','500.00','0.00','0.00','0.00','0.00',NULL,'2021-04-23 06:15:59','2021-04-23 06:15:59','2021-04-23 06:16:27');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('6081f5ae-3450-42fb-866b-325482e7a674',NULL,'jo','invoiced','customer',2,'11','3.00','0.00','0.00','0.00','0.00',NULL,'2021-04-23 06:16:14','2021-04-23 06:16:14','2021-04-23 06:16:27');
insert  into `transactions`(`id`,`user`,`type`,`status`,`entity_type`,`entity_id`,`ref_no`,`amount`,`commission`,`discount`,`tax`,`interest`,`flag`,`timestamp`,`created`,`modified`) values ('6081f5bb-035c-4d31-ad34-325482e7a674',NULL,'invoice','fulfilled','customer',2,'13','500.00','0.00','0.00','0.00','0.00',NULL,'2021-04-23 06:16:27','2021-04-23 06:16:27','2021-04-23 06:16:27');

/*Table structure for table `users` */

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `username` varchar(10) DEFAULT NULL,
  `password` varchar(150) DEFAULT NULL,
  `status` varchar(10) DEFAULT 'active',
  `type` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

/*Data for the table `users` */

insert  into `users`(`id`,`first_name`,`last_name`,`username`,`password`,`status`,`type`) values (1,'Admin','admin','admin','827ccb0eea8a706c4c34a16891f84e7b','active','staff');

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
