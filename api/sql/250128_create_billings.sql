CREATE TABLE billings (
	id char(20) NOT NULL COMMENT 'Format: RIC-SOA-MMYY-NNNN where MMYY is month/year and NNNN is sequence',
	customer_id INT NULL,
	due_amount DECIMAL(12,2) NULL,
	due_date DATE NULL,
	details TEXT NULL,
	hash VARCHAR(64) NULL COMMENT 'SHA-256 hash for verification',
	terms VARCHAR(10) NULL,
	created DATETIME NULL,
	created_by varchar(10) NULL,
	status char(10) NULL DEFAULT 'active',
	CONSTRAINT billings_pk PRIMARY KEY (id),
	INDEX idx_billings_customer (customer_id),
	INDEX idx_billings_due_date (due_date),
	INDEX idx_billings_status (status)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

CREATE TABLE billing_details (
	id INT auto_increment NOT NULL,
	billing_id varchar(20) NULL,
	ref_no varchar(20) NULL COMMENT 'Reference to invoice number',
	invoice_date DATE NULL,
	invoice_amount DECIMAL(12,2) NULL,
	status char(10) NULL DEFAULT 'active',
	created DATETIME NULL,
	modified DATETIME NULL,
	CONSTRAINT billing_details_pk PRIMARY KEY (id),
	INDEX idx_billing_details_billing_id (billing_id),
	CONSTRAINT fk_billing_details_billing_id FOREIGN KEY (billing_id)
		REFERENCES billings(id) ON DELETE CASCADE ON UPDATE CASCADE
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;


-- Add comment to explain the purpose of these tables
-- The billings table stores statement of account (SOA) information for customers
-- The billing_details table stores the individual invoice items included in each SOA
-- The id field in billings follows the format RIC-SOA-MMYY-NNNN where MM is month, YY is year, and NNNN is a sequential number
-- Example: RIC-SOA-0125-0004 for the 4th SOA in January 2025